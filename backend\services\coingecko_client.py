"""
CoinGecko API client implementation
"""
from typing import Dict, Any, List, Optional
from backend.services.api_client import BaseAPIClient
from backend.config.settings import settings


class CoinGeckoClient(BaseAPIClient):
    """Client for CoinGecko API"""
    
    def __init__(self):
        super().__init__(
            base_url="https://api.coingecko.com/api/v3",
            api_key=settings.COINGECKO_API_KEY,
            requests_per_minute=50 if settings.COINGECKO_API_KEY else 10,  # Higher limit with API key
            timeout=20
        )
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get CoinGecko authentication headers"""
        if self.api_key:
            return {"x-cg-demo-api-key": self.api_key}
        return {}
    
    async def get_coins_list(self, include_platform: bool = True) -> Optional[List[Dict[str, Any]]]:
        """Get list of all coins"""
        params = {"include_platform": str(include_platform).lower()}
        return await self.get("/coins/list", params=params, cache_ttl=3600)  # Cache for 1 hour
    
    async def get_coin_data(self, coin_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed coin data"""
        return await self.get(f"/coins/{coin_id}", cache_ttl=300)
    
    async def get_coin_market_data(
        self, 
        vs_currency: str = "usd",
        order: str = "market_cap_desc",
        per_page: int = 250,
        page: int = 1,
        sparkline: bool = False,
        price_change_percentage: str = "1h,24h,7d"
    ) -> Optional[List[Dict[str, Any]]]:
        """Get coin market data"""
        params = {
            "vs_currency": vs_currency,
            "order": order,
            "per_page": per_page,
            "page": page,
            "sparkline": str(sparkline).lower(),
            "price_change_percentage": price_change_percentage
        }
        return await self.get("/coins/markets", params=params, cache_ttl=60)
    
    async def get_trending_coins(self) -> Optional[Dict[str, Any]]:
        """Get trending coins"""
        return await self.get("/search/trending", cache_ttl=300)
    
    async def get_coin_price(
        self, 
        coin_ids: List[str], 
        vs_currencies: List[str] = ["usd"],
        include_market_cap: bool = True,
        include_24hr_vol: bool = True,
        include_24hr_change: bool = True
    ) -> Optional[Dict[str, Any]]:
        """Get current price of coins"""
        params = {
            "ids": ",".join(coin_ids),
            "vs_currencies": ",".join(vs_currencies),
            "include_market_cap": str(include_market_cap).lower(),
            "include_24hr_vol": str(include_24hr_vol).lower(),
            "include_24hr_change": str(include_24hr_change).lower()
        }
        return await self.get("/simple/price", params=params, cache_ttl=60)
    
    async def get_token_price_by_contract(
        self, 
        platform: str, 
        contract_address: str,
        vs_currencies: List[str] = ["usd"]
    ) -> Optional[Dict[str, Any]]:
        """Get token price by contract address"""
        params = {"vs_currencies": ",".join(vs_currencies)}
        return await self.get(
            f"/simple/token_price/{platform}",
            params={**params, "contract_addresses": contract_address},
            cache_ttl=60
        )
    
    async def search_coins(self, query: str) -> Optional[Dict[str, Any]]:
        """Search for coins"""
        params = {"query": query}
        return await self.get("/search", params=params, cache_ttl=300)
    
    async def get_coin_history(
        self, 
        coin_id: str, 
        date: str,  # Format: dd-mm-yyyy
        localization: bool = False
    ) -> Optional[Dict[str, Any]]:
        """Get historical data for a coin"""
        params = {"date": date, "localization": str(localization).lower()}
        return await self.get(f"/coins/{coin_id}/history", params=params, cache_ttl=3600)
    
    async def get_coin_market_chart(
        self, 
        coin_id: str, 
        vs_currency: str = "usd",
        days: str = "1",
        interval: str = "hourly"
    ) -> Optional[Dict[str, Any]]:
        """Get market chart data"""
        params = {
            "vs_currency": vs_currency,
            "days": days,
            "interval": interval
        }
        return await self.get(f"/coins/{coin_id}/market_chart", params=params, cache_ttl=300)
    
    async def get_exchanges(self) -> Optional[List[Dict[str, Any]]]:
        """Get list of exchanges"""
        return await self.get("/exchanges", cache_ttl=3600)
    
    async def get_exchange_tickers(
        self, 
        exchange_id: str,
        coin_ids: Optional[str] = None,
        page: int = 1
    ) -> Optional[Dict[str, Any]]:
        """Get exchange tickers"""
        params = {"page": page}
        if coin_ids:
            params["coin_ids"] = coin_ids
        return await self.get(f"/exchanges/{exchange_id}/tickers", params=params, cache_ttl=60)
    
    async def get_global_data(self) -> Optional[Dict[str, Any]]:
        """Get global cryptocurrency data"""
        return await self.get("/global", cache_ttl=300)
    
    async def get_supported_platforms(self) -> Optional[List[Dict[str, Any]]]:
        """Get supported asset platforms"""
        return await self.get("/asset_platforms", cache_ttl=3600)
    
    async def validate_token_legitimacy(self, token_symbol: str, token_name: str) -> Dict[str, Any]:
        """Validate if a token is legitimate using CoinGecko data"""
        validation_result = {
            "is_legitimate": False,
            "confidence_score": 0.0,
            "coingecko_id": None,
            "market_cap_rank": None,
            "verification_factors": []
        }
        
        try:
            # Search for the token
            search_result = await self.search_coins(token_symbol)
            if not search_result:
                return validation_result
            
            coins = search_result.get("coins", [])
            
            # Look for exact matches
            exact_matches = [
                coin for coin in coins
                if (coin.get("symbol", "").lower() == token_symbol.lower() or
                    coin.get("name", "").lower() == token_name.lower())
            ]
            
            if exact_matches:
                best_match = exact_matches[0]
                validation_result["is_legitimate"] = True
                validation_result["coingecko_id"] = best_match.get("id")
                validation_result["market_cap_rank"] = best_match.get("market_cap_rank")
                
                # Calculate confidence score based on various factors
                confidence_factors = []
                
                # Market cap rank (lower is better)
                if best_match.get("market_cap_rank"):
                    rank = best_match["market_cap_rank"]
                    if rank <= 100:
                        confidence_factors.append(0.4)
                        validation_result["verification_factors"].append("Top 100 by market cap")
                    elif rank <= 500:
                        confidence_factors.append(0.3)
                        validation_result["verification_factors"].append("Top 500 by market cap")
                    elif rank <= 1000:
                        confidence_factors.append(0.2)
                        validation_result["verification_factors"].append("Top 1000 by market cap")
                    else:
                        confidence_factors.append(0.1)
                
                # Symbol exact match
                if best_match.get("symbol", "").lower() == token_symbol.lower():
                    confidence_factors.append(0.3)
                    validation_result["verification_factors"].append("Symbol exact match")
                
                # Name exact match
                if best_match.get("name", "").lower() == token_name.lower():
                    confidence_factors.append(0.3)
                    validation_result["verification_factors"].append("Name exact match")
                
                validation_result["confidence_score"] = min(1.0, sum(confidence_factors))
            
        except Exception as e:
            self.logger.logger.error(f"Error validating token legitimacy: {e}")
        
        return validation_result
    
    async def get_trending_tokens_by_category(self, category: str = "decentralized-finance-defi") -> Optional[List[Dict[str, Any]]]:
        """Get trending tokens by category"""
        try:
            # Get coins by category
            market_data = await self.get_coin_market_data(
                order="volume_desc",
                per_page=100,
                price_change_percentage="1h,24h"
            )
            
            if not market_data:
                return []
            
            # Filter and sort by volume and price change
            trending_tokens = []
            for coin in market_data:
                if (coin.get("total_volume", 0) > 1000000 and  # Min $1M volume
                    coin.get("price_change_percentage_1h", 0) > 2):  # Min 2% price increase in 1h
                    trending_tokens.append(coin)
            
            # Sort by combination of volume and price change
            trending_tokens.sort(
                key=lambda x: (x.get("total_volume", 0) * (1 + x.get("price_change_percentage_1h", 0) / 100)),
                reverse=True
            )
            
            return trending_tokens[:50]  # Return top 50
            
        except Exception as e:
            self.logger.logger.error(f"Error getting trending tokens: {e}")
            return []
