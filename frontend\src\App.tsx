import React, { useEffect, useState } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from 'react-query'
import { Toaster } from 'react-hot-toast'
import { motion, AnimatePresence } from 'framer-motion'

// Components
import Dashboard from '@components/dashboard/Dashboard'
import OpportunityDetails from '@components/arbitrage/OpportunityDetails'
import Analytics from '@components/analytics/Analytics'
import Settings from '@components/settings/Settings'
import Sidebar from '@components/layout/Sidebar'
import Header from '@components/layout/Header'
import LoadingScreen from '@components/ui/LoadingScreen'

// Hooks and Store
import { useArbitrageStore } from '@store/arbitrageStore'
import { useArbitrageWebSocket } from '@hooks/useWebSocket'

// Types
import { ArbitrageOpportunity } from '@types/index'

// Styles
import './App.css'

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 30000, // 30 seconds
    },
  },
})

const App: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true)
  const [sidebarOpen, setSidebarOpen] = useState(true)

  // Zustand store
  const {
    opportunities,
    isScanning,
    stats,
    selectedOpportunity,
    startScan,
    stopScan,
    setSelectedOpportunity,
    fetchEngineStatus,
    refreshOpportunities,
    validateOpportunity,
  } = useArbitrageStore()

  // WebSocket connection for real-time updates
  const {
    isConnected: wsConnected,
    opportunities: wsOpportunities,
    scanStatus: wsScanStatus,
    stats: wsStats,
  } = useArbitrageWebSocket()

  // Initialize app
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Fetch initial data
        await Promise.all([
          fetchEngineStatus(),
          refreshOpportunities(),
        ])
      } catch (error) {
        console.error('Failed to initialize app:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeApp()
  }, [fetchEngineStatus, refreshOpportunities])

  // Update store with WebSocket data
  useEffect(() => {
    if (wsOpportunities.length > 0) {
      useArbitrageStore.getState().setOpportunities(wsOpportunities)
    }
  }, [wsOpportunities])

  useEffect(() => {
    if (wsStats) {
      useArbitrageStore.getState().setStats(wsStats)
    }
  }, [wsStats])

  const handleStartScan = async () => {
    await startScan()
  }

  const handleStopScan = async () => {
    await stopScan()
  }

  const handleConfigureScan = () => {
    // Navigate to settings or open modal
    console.log('Configure scan')
  }

  const handleSelectOpportunity = (opportunity: ArbitrageOpportunity) => {
    setSelectedOpportunity(opportunity)
  }

  const handleValidateOpportunity = async (opportunity: ArbitrageOpportunity) => {
    if (opportunity.id) {
      await validateOpportunity(opportunity.id)
    }
  }

  if (isLoading) {
    return <LoadingScreen />
  }

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-tertiary">
          {/* Background Effects */}
          <div className="fixed inset-0 bg-noise opacity-5 pointer-events-none" />
          <div className="fixed inset-0 bg-gradient-to-br from-neon-purple/5 via-transparent to-neon-blue/5 pointer-events-none" />
          
          {/* Main Layout */}
          <div className="flex h-screen overflow-hidden">
            {/* Sidebar */}
            <AnimatePresence>
              {sidebarOpen && (
                <motion.div
                  initial={{ x: -300 }}
                  animate={{ x: 0 }}
                  exit={{ x: -300 }}
                  transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                  className="flex-shrink-0"
                >
                  <Sidebar
                    isOpen={sidebarOpen}
                    onClose={() => setSidebarOpen(false)}
                    wsConnected={wsConnected}
                    isScanning={isScanning}
                  />
                </motion.div>
              )}
            </AnimatePresence>

            {/* Main Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* Header */}
              <Header
                sidebarOpen={sidebarOpen}
                onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
                wsConnected={wsConnected}
                isScanning={isScanning}
                opportunitiesCount={opportunities.length}
              />

              {/* Page Content */}
              <main className="flex-1 overflow-auto">
                <Routes>
                  <Route
                    path="/"
                    element={
                      <Dashboard
                        opportunities={opportunities}
                        isScanning={isScanning}
                        stats={stats}
                        onStartScan={handleStartScan}
                        onStopScan={handleStopScan}
                        onConfigureScan={handleConfigureScan}
                        onSelectOpportunity={handleSelectOpportunity}
                      />
                    }
                  />
                  
                  <Route
                    path="/analytics"
                    element={
                      <Analytics
                        opportunities={opportunities}
                        isScanning={isScanning}
                      />
                    }
                  />
                  
                  <Route
                    path="/settings"
                    element={<Settings />}
                  />
                  
                  {/* Redirect unknown routes to dashboard */}
                  <Route path="*" element={<Navigate to="/" replace />} />
                </Routes>
              </main>
            </div>
          </div>

          {/* Opportunity Details Modal */}
          <AnimatePresence>
            {selectedOpportunity && (
              <OpportunityDetails
                opportunity={selectedOpportunity}
                onClose={() => setSelectedOpportunity(null)}
                onValidate={() => handleValidateOpportunity(selectedOpportunity)}
              />
            )}
          </AnimatePresence>

          {/* Toast Notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'rgba(15, 15, 35, 0.95)',
                color: 'rgba(255, 255, 255, 0.9)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                borderRadius: '12px',
                backdropFilter: 'blur(10px)',
              },
              success: {
                iconTheme: {
                  primary: '#10b981',
                  secondary: 'rgba(255, 255, 255, 0.9)',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: 'rgba(255, 255, 255, 0.9)',
                },
              },
            }}
          />

          {/* Connection Status Indicator */}
          <div className="fixed bottom-4 right-4 z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg glass ${
                wsConnected ? 'border-success/30' : 'border-error/30'
              }`}
            >
              <div
                className={`w-2 h-2 rounded-full ${
                  wsConnected ? 'bg-success animate-pulse' : 'bg-error'
                }`}
              />
              <span className="text-xs text-white">
                {wsConnected ? 'Connected' : 'Disconnected'}
              </span>
            </motion.div>
          </div>
        </div>
      </Router>
    </QueryClientProvider>
  )
}

export default App
