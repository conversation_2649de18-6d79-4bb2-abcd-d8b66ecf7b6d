"""
Enhanced Crypto Arbitrage Bot v3.0 - Clean Version
Complete solution with Flask web interface (no WebSocket dependencies)
All-in-one file for easy deployment
"""

# Core imports
import asyncio
import aiohttp
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
import json
import random
from collections import defaultdict, deque
import threading
import requests

# Flask imports (no SocketIO for simplicity)
try:
    from flask import Flask, render_template_string, jsonify, request
    FLASK_AVAILABLE = True
except ImportError:
    print("⚠️ Flask not installed. Web interface will be disabled.")
    print("💡 Install with: pip install Flask aiohttp requests")
    FLASK_AVAILABLE = False

# Simple logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app if available
if FLASK_AVAILABLE:
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'crypto_arbitrage_bot_v3_secret'
    
    # Enhanced State Management untuk Web Interface
    web_app_state = {
        'status': 'Idle',
        'is_running': False,
        'opportunities': [],
        'scan_count': 0,
        'last_scan_time': None,
        'logs': deque(maxlen=500),
        'progress': {
            'percentage': 0,
            'current_action': 'Menunggu...',
            'tokens_scanned': 0,
            'total_tokens': 0,
            'opportunities_found': 0,
        },
        'statistics': {
            'total_scans': 0,
            'total_opportunities': 0,
            'uptime_seconds': 0,
            'start_time': None
        }
    }
else:
    app = None
    web_app_state = {}

@dataclass
class TradingPair:
    """Data class for normalized trading pairs"""
    base_token: str
    quote_token: str
    base_address: str
    quote_address: str
    dex_id: str
    chain: str
    price_usd: float
    liquidity_usd: float
    pair_address: str
    volume_24h: float
    
    def get_pair_key(self) -> str:
        """Generate normalized pair key for comparison"""
        # Sort tokens alphabetically to ensure consistent pairing
        tokens = sorted([self.base_token.upper(), self.quote_token.upper()])
        return f"{tokens[0]}/{tokens[1]}"

class PairValidator:
    """Advanced trading pair validation system to prevent false arbitrage signals"""
    
    def __init__(self):
        # Token normalization mappings
        self.token_aliases = {
            'WETH': 'ETH', 'WBTC': 'BTC', 'WBNB': 'BNB', 'WMATIC': 'MATIC', 'WSOL': 'SOL',
            'USDC.E': 'USDC', 'USDT.E': 'USDT', 'DAI.E': 'DAI',
        }
        
        # Tokens that should NEVER be normalized (different assets with similar names)
        self.protected_tokens = {
            'STSOL', 'MSOL', 'JITOSOL', 'LSTSOL', 'BSOL', 'SCNSOL', 'DAOSOL',
            'RETH', 'STETH', 'CBETH', 'WSTETH', 'FRXETH', 'SFRXETH'
        }
    
    def normalize_token_symbol(self, symbol: str) -> str:
        """Normalize token symbol while preserving liquid staking tokens"""
        if not symbol:
            return symbol
            
        symbol_upper = symbol.upper()
        
        # Never normalize protected tokens
        if symbol_upper in self.protected_tokens:
            return symbol_upper
            
        # Apply normalization mapping
        return self.token_aliases.get(symbol_upper, symbol_upper)
    
    def extract_trading_pair(self, pair_data: Dict) -> Optional[TradingPair]:
        """Extract and normalize trading pair from DexScreener data"""
        try:
            base_token = pair_data.get('baseToken', {})
            quote_token = pair_data.get('quoteToken', {})
            
            if not base_token or not quote_token:
                return None
                
            base_symbol = self.normalize_token_symbol(base_token.get('symbol', ''))
            quote_symbol = self.normalize_token_symbol(quote_token.get('symbol', ''))
            
            if not base_symbol or not quote_symbol:
                return None
                
            return TradingPair(
                base_token=base_symbol,
                quote_token=quote_symbol,
                base_address=base_token.get('address', ''),
                quote_address=quote_token.get('address', ''),
                dex_id=pair_data.get('dexId', ''),
                chain=pair_data.get('chainId', ''),
                price_usd=float(pair_data.get('priceUsd', 0)),
                liquidity_usd=pair_data.get('liquidity', {}).get('usd', 0),
                pair_address=pair_data.get('pairAddress', ''),
                volume_24h=pair_data.get('volume', {}).get('h24', 0)
            )
            
        except Exception as e:
            logger.warning(f"Failed to extract trading pair: {e}")
            return None
    
    def validate_arbitrage_pairs(self, pairs: List[Dict], token_symbol: str) -> Tuple[List[TradingPair], List[str]]:
        """Validate and filter pairs for true arbitrage opportunities"""
        valid_pairs = []
        validation_logs = []
        
        # Extract and normalize all pairs
        normalized_pairs = []
        for pair_data in pairs:
            trading_pair = self.extract_trading_pair(pair_data)
            if trading_pair:
                normalized_pairs.append(trading_pair)
        
        if len(normalized_pairs) < 2:
            validation_logs.append(f"❌ {token_symbol}: Insufficient pairs ({len(normalized_pairs)}) for arbitrage")
            return [], validation_logs
        
        # Group pairs by normalized pair key
        pair_groups = {}
        for pair in normalized_pairs:
            pair_key = pair.get_pair_key()
            if pair_key not in pair_groups:
                pair_groups[pair_key] = []
            pair_groups[pair_key].append(pair)
        
        # Find groups with multiple DEXs (true arbitrage opportunities)
        for pair_key, group_pairs in pair_groups.items():
            if len(group_pairs) >= 2:
                # Ensure different DEXs
                unique_dexs = set(pair.dex_id for pair in group_pairs)
                if len(unique_dexs) >= 2:
                    valid_pairs.extend(group_pairs)
                    validation_logs.append(f"✅ {token_symbol}: Valid arbitrage pair {pair_key} across {len(unique_dexs)} DEXs")
                else:
                    validation_logs.append(f"⚠️ {token_symbol}: Same DEX for pair {pair_key}, skipping")
            else:
                validation_logs.append(f"⚠️ {token_symbol}: Single DEX for pair {pair_key}, skipping")
        
        return valid_pairs, validation_logs

class AdvancedArbitrageDetector:
    def __init__(self):
        self.opportunities = []
        self.last_scan = None
        self.scan_logs = []
        
        # v3.0 CRITICAL: Pair validation system to prevent false arbitrage signals
        self.pair_validator = PairValidator()
        
        # Configuration
        self.config = {
            "profit_min": 0.5,
            "profit_max": 200.0,
            "min_liquidity": 5000,
            "max_opportunities": 50,
            "scan_timeout": 30
        }
        
        # Enhanced token database with popular tokens
        self.token_categories = {
            "stablecoins": {
                "tokens": ["USDT", "USDC", "DAI", "BUSD", "FRAX", "TUSD", "USDP", "LUSD", "sUSD", "USDN"]
            },
            "blue_chips": {
                "tokens": ["BTC", "ETH", "BNB", "XRP", "ADA", "SOL", "DOGE", "DOT", "MATIC", "SHIB", "AVAX", "LTC", "UNI", "LINK", "ATOM"]
            },
            "meme_coins": {
                "tokens": ["DOGE", "SHIB", "PEPE", "FLOKI", "ELON", "BONK", "WIF", "POPCAT", "MYRO", "BOME"]
            }
        }
        
        # Supported chains
        self.supported_chains = ["ethereum", "bsc", "polygon", "arbitrum", "solana"]
        
    def add_log(self, message: str, level: str = "info"):
        """Add log entry"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {message}"
        self.scan_logs.append(log_entry)
        print(log_entry)
        
        # Also add to web logs if available
        if FLASK_AVAILABLE and 'logs' in web_app_state:
            web_log_entry = {
                'timestamp': timestamp,
                'message': message,
                'level': level,
                'full_text': log_entry
            }
            web_app_state['logs'].appendleft(web_log_entry)
    
    async def get_token_pairs(self, token_symbol: str) -> List[Dict]:
        """Get token pairs from DexScreener API"""
        try:
            url = f"https://api.dexscreener.com/latest/dex/search?q={token_symbol}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        pairs = data.get('pairs', [])
                        
                        # Filter for supported chains
                        filtered_pairs = [
                            pair for pair in pairs 
                            if pair.get('chainId') in self.supported_chains
                        ]
                        
                        return filtered_pairs
                    else:
                        self.add_log(f"❌ API error for {token_symbol}: {response.status}", "error")
                        return []
                        
        except Exception as e:
            self.add_log(f"❌ Error fetching pairs for {token_symbol}: {e}", "error")
            return []

    async def _find_arbitrage_in_pairs(self, pairs: List[Dict], token_symbol: str, chain: str) -> List[Dict]:
        """Find arbitrage opportunities within a group of pairs with strict pair validation"""
        opportunities = []

        try:
            # CRITICAL: Validate pairs to prevent false arbitrage signals
            valid_pairs, validation_logs = self.pair_validator.validate_arbitrage_pairs(pairs, token_symbol)

            # Log validation results
            for log_msg in validation_logs:
                self.add_log(log_msg, "validation")

            if len(valid_pairs) < 2:
                return []

            # Group validated pairs by normalized pair key
            pair_groups = {}
            for trading_pair in valid_pairs:
                pair_key = trading_pair.get_pair_key()
                if pair_key not in pair_groups:
                    pair_groups[pair_key] = []
                pair_groups[pair_key].append(trading_pair)

            # Find arbitrage opportunities within each validated pair group
            for pair_key, group_pairs in pair_groups.items():
                if len(group_pairs) < 2:
                    continue

                # Create DEX price mapping for this specific pair
                dex_prices = {}
                for trading_pair in group_pairs:
                    if trading_pair.price_usd > 0 and trading_pair.liquidity_usd > 1000:
                        dex_prices[trading_pair.dex_id] = {
                            'price': trading_pair.price_usd,
                            'liquidity': trading_pair.liquidity_usd,
                            'pair_address': trading_pair.pair_address,
                            'pair_key': pair_key
                        }

                # Find arbitrage opportunities between DEXs
                dex_list = list(dex_prices.items())
                for i, (dex1_id, dex1_data) in enumerate(dex_list):
                    for j, (dex2_id, dex2_data) in enumerate(dex_list[i+1:], i+1):

                        price1 = dex1_data["price"]
                        price2 = dex2_data["price"]

                        if price1 <= 0 or price2 <= 0:
                            continue

                        # Calculate profit percentage
                        if price2 > price1:
                            profit_pct = ((price2 - price1) / price1) * 100
                            buy_dex = dex1_id
                            sell_dex = dex2_id
                            buy_price = price1
                            sell_price = price2
                            min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                        else:
                            profit_pct = ((price1 - price2) / price2) * 100
                            buy_dex = dex2_id
                            sell_dex = dex1_id
                            buy_price = price2
                            sell_price = price1
                            min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])

                        # Check if profitable
                        if (self.config["profit_min"] < profit_pct <= self.config["profit_max"] and
                            min_liquidity > self.config["min_liquidity"]):

                            opportunity = {
                                "id": f"{token_symbol}_{chain}_{int(time.time())}_{i}_{j}",
                                "token_symbol": token_symbol,
                                "validated_pair": pair_key,
                                "buy_exchange": f"{chain}_{buy_dex}",
                                "sell_exchange": f"{chain}_{sell_dex}",
                                "buy_price": round(buy_price, 6),
                                "sell_price": round(sell_price, 6),
                                "profit_percentage": round(profit_pct, 4),
                                "min_liquidity": round(min_liquidity, 2),
                                "buy_dex_name": buy_dex,
                                "sell_dex_name": sell_dex,
                                "timestamp": datetime.now().isoformat(),
                                "validation_status": "✅ PAIR_VALIDATED"
                            }

                            opportunities.append(opportunity)
                            self.add_log(f"✅ {token_symbol}: Valid arbitrage found for {pair_key} - {profit_pct:.2f}% profit", "success")

            return opportunities

        except Exception as e:
            logger.error(f"Arbitrage finding error: {e}")
            return []

    async def scan_for_arbitrage_opportunities(self):
        """Main scanning function"""
        self.add_log("🚀 Starting Enhanced Crypto Arbitrage Bot v3.0 scan", "info")
        self.add_log("💎 Pair validation system active - preventing false signals", "info")

        start_time = time.time()
        total_opportunities = 0

        # Get all tokens from all categories
        all_tokens = []
        for category_name, category_data in self.token_categories.items():
            tokens = category_data.get('tokens', [])
            all_tokens.extend(tokens[:5])  # Limit per category for demo

        self.add_log(f"🔍 Scanning {len(all_tokens)} tokens across {len(self.supported_chains)} blockchains", "info")

        # Update web state
        if FLASK_AVAILABLE:
            web_app_state['progress']['total_tokens'] = len(all_tokens)

        # Scan tokens
        for i, token in enumerate(all_tokens):
            try:
                self.add_log(f"📊 Scanning token {i+1}/{len(all_tokens)}: {token}", "info")

                # Update progress
                if FLASK_AVAILABLE:
                    web_app_state['progress']['tokens_scanned'] = i + 1
                    web_app_state['progress']['percentage'] = int((i + 1) / len(all_tokens) * 100)
                    web_app_state['progress']['current_action'] = f"Scanning {token}..."

                # Get pairs for this token
                pairs = await self.get_token_pairs(token)

                if pairs:
                    # Group pairs by chain
                    chain_pairs = {}
                    for pair in pairs:
                        chain = pair.get('chainId', 'unknown')
                        if chain not in chain_pairs:
                            chain_pairs[chain] = []
                        chain_pairs[chain].append(pair)

                    # Find arbitrage within each chain
                    for chain, chain_pair_list in chain_pairs.items():
                        if len(chain_pair_list) >= 2:
                            opportunities = await self._find_arbitrage_in_pairs(chain_pair_list, token, chain)
                            if opportunities:
                                self.opportunities.extend(opportunities)
                                total_opportunities += len(opportunities)

                # Rate limiting
                await asyncio.sleep(0.2)

            except Exception as e:
                self.add_log(f"❌ Error scanning {token}: {e}", "error")

        # Keep only latest opportunities
        self.opportunities = self.opportunities[-self.config["max_opportunities"]:]

        scan_time = time.time() - start_time
        self.last_scan = datetime.now().isoformat()

        # Update web state
        if FLASK_AVAILABLE:
            web_app_state['opportunities'] = self.opportunities
            web_app_state['scan_count'] += 1
            web_app_state['statistics']['total_scans'] += 1
            web_app_state['statistics']['total_opportunities'] += total_opportunities
            web_app_state['progress']['opportunities_found'] = total_opportunities
            web_app_state['progress']['percentage'] = 100
            web_app_state['progress']['current_action'] = f"Scan completed - {total_opportunities} opportunities found"

        self.add_log(f"✅ Scan completed in {scan_time:.2f}s", "success")
        self.add_log(f"📈 Found {total_opportunities} total opportunities", "success")
        self.add_log(f"💎 All opportunities validated - zero false signals", "success")

        return self.opportunities

# Initialize the detector globally
detector = AdvancedArbitrageDetector()

# ===== FLASK WEB INTERFACE =====

if FLASK_AVAILABLE:

    def add_web_log(message, level="info"):
        """Enhanced logging for web interface"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = {
            'timestamp': timestamp,
            'message': message,
            'level': level,
            'full_text': f"[{timestamp}] {message}"
        }
        web_app_state['logs'].appendleft(log_entry)
        print(log_entry['full_text'])

    def start_web_arbitrage_bot():
        """Start the arbitrage bot"""
        if web_app_state['is_running']:
            add_web_log("⚠️ Bot sudah berjalan", "warning")
            return

        web_app_state['is_running'] = True
        web_app_state['status'] = 'Running'
        web_app_state['statistics']['start_time'] = time.time()
        web_app_state['scan_count'] = 0

        add_web_log("🚀 Memulai Enhanced Crypto Arbitrage Bot v3.0", "success")

        # Start bot in separate thread
        bot_thread = threading.Thread(target=run_web_arbitrage_bot, daemon=True)
        bot_thread.start()

    def stop_web_arbitrage_bot():
        """Stop the arbitrage bot"""
        web_app_state['is_running'] = False
        web_app_state['status'] = 'Stopped'

        add_web_log("🛑 Bot dihentikan", "warning")

        # Calculate final statistics
        if web_app_state['statistics']['start_time']:
            web_app_state['statistics']['uptime_seconds'] = int(time.time() - web_app_state['statistics']['start_time'])

    def run_web_arbitrage_bot():
        """Main bot execution loop"""
        try:
            while web_app_state['is_running']:
                web_app_state['progress']['current_action'] = "Memulai scan arbitrase..."

                # Run the main detector scan
                asyncio.run(detector.scan_for_arbitrage_opportunities())

                if not web_app_state['is_running']:
                    break

                # Get opportunities from detector
                opportunities = detector.opportunities[-10:]  # Latest 10
                web_app_state['opportunities'] = opportunities

                web_app_state['scan_count'] += 1
                web_app_state['statistics']['total_scans'] += 1
                web_app_state['statistics']['total_opportunities'] += len(opportunities)

                add_web_log(f"✅ Scan #{web_app_state['scan_count']} selesai - {len(opportunities)} peluang ditemukan", "success")

                # Wait before next scan
                for i in range(30):
                    if not web_app_state['is_running']:
                        break
                    web_app_state['progress']['current_action'] = f"Menunggu scan berikutnya... {30-i}s"
                    time.sleep(1)

        except Exception as e:
            add_web_log(f"❌ Error dalam bot execution: {e}", "error")
        finally:
            web_app_state['is_running'] = False
            web_app_state['status'] = 'Idle'

    # Simple HTML Template
    SIMPLE_HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Enhanced Crypto Arbitrage Bot v3.0 - BOBACHEESE</title>

    <style>
        :root {
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
            --accent-cyan: #00ffff;
            --accent-purple: #8a2be2;
            --accent-green: #00ff41;
            --accent-red: #ff073a;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --text-muted: #666666;
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%);
        }

        .header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .panel {
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
        }

        .panel-title {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: var(--accent-cyan);
        }

        .control-section {
            grid-column: 1 / -1;
        }

        .control-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .btn {
            padding: 0.8rem 2rem;
            border: 2px solid var(--accent-cyan);
            background: transparent;
            color: var(--accent-cyan);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            font-weight: 600;
        }

        .btn:hover {
            background: rgba(0, 255, 255, 0.1);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .btn.start {
            border-color: var(--accent-green);
            color: var(--accent-green);
        }

        .btn.start:hover {
            background: rgba(0, 255, 65, 0.1);
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
        }

        .btn.stop {
            border-color: var(--accent-red);
            color: var(--accent-red);
        }

        .btn.stop:hover {
            background: rgba(255, 7, 58, 0.1);
            box-shadow: 0 0 20px rgba(255, 7, 58, 0.5);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status-panel {
            background: var(--bg-secondary);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .opportunities-container {
            max-height: 400px;
            overflow-y: auto;
        }

        .opportunity-card {
            background: var(--bg-secondary);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .opportunity-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-green), var(--accent-cyan));
        }

        .logs-container {
            max-height: 300px;
            overflow-y: auto;
            background: var(--bg-secondary);
            border-radius: 10px;
            border: 1px solid var(--glass-border);
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .log-entry {
            padding: 0.3rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-time {
            color: var(--text-muted);
            margin-right: 0.5rem;
        }

        .log-message {
            color: var(--text-primary);
        }

        .log-entry.info .log-message {
            color: var(--accent-cyan);
        }

        .log-entry.success .log-message {
            color: var(--accent-green);
        }

        .log-entry.warning .log-message {
            color: #ff8c00;
        }

        .log-entry.error .log-message {
            color: var(--accent-red);
        }

        .no-opportunities {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                padding: 1rem;
            }

            .control-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <h1>🚀 Enhanced Crypto Arbitrage Bot v3.0</h1>
        <div style="color: var(--accent-purple);">
            by BOBACHEESE | Advanced Multi-Chain Arbitrage Detection
        </div>
    </header>

    <div class="container">
        <!-- Control Section -->
        <div class="panel control-section">
            <div class="panel-title">🎮 Bot Control Center</div>

            <div class="control-buttons">
                <button class="btn start" id="startBtn" onclick="startBot()">
                    ▶️ Start Bot
                </button>
                <button class="btn stop" id="stopBtn" onclick="stopBot()" disabled>
                    ⏹️ Stop Bot
                </button>
                <button class="btn" onclick="resetStats()">
                    🔄 Reset Stats
                </button>
            </div>

            <div class="status-panel">
                <h4>📊 Bot Status</h4>
                <div>Status: <span id="currentStatus">Idle</span></div>
                <div>Scan Count: <span id="scanCount">0</span></div>
                <div>Opportunities: <span id="opportunitiesCount">0</span></div>
                <div>Current Action: <span id="currentAction">Menunggu...</span></div>
            </div>
        </div>

        <!-- Arbitrage Opportunities Display -->
        <div class="panel">
            <div class="panel-title">💎 Live Arbitrage Opportunities</div>

            <div class="opportunities-container" id="opportunitiesContainer">
                <div class="no-opportunities">
                    <div style="font-size: 3rem;">🔍</div>
                    <p>Menunggu peluang arbitrase...</p>
                </div>
            </div>
        </div>

        <!-- Real-time Logs -->
        <div class="panel" style="grid-column: 1 / -1;">
            <div class="panel-title">📋 Real-time Logs</div>

            <div class="logs-container" id="logsContainer">
                <div class="log-entry info">
                    <span class="log-time">[00:00:00]</span>
                    <span class="log-message">🚀 Enhanced Crypto Arbitrage Bot v3.0 siap digunakan</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = false;

        // Bot Control Functions
        function startBot() {
            if (!isRunning) {
                fetch('/api/start', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateButtonStates(true);
                            addLogEntry('🚀 Memulai bot...', 'info');
                        }
                    });
            }
        }

        function stopBot() {
            if (isRunning) {
                fetch('/api/stop', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateButtonStates(false);
                            addLogEntry('🛑 Menghentikan bot...', 'warning');
                        }
                    });
            }
        }

        function resetStats() {
            document.getElementById('scanCount').textContent = '0';
            document.getElementById('opportunitiesCount').textContent = '0';

            const container = document.getElementById('opportunitiesContainer');
            container.innerHTML = `
                <div class="no-opportunities">
                    <div style="font-size: 3rem;">🔍</div>
                    <p>Menunggu peluang arbitrase...</p>
                </div>
            `;

            addLogEntry('🔄 Statistik direset', 'info');
        }

        function updateButtonStates(running) {
            isRunning = running;
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');

            if (running) {
                startBtn.disabled = true;
                stopBtn.disabled = false;
                document.getElementById('currentStatus').textContent = 'Running';
            } else {
                startBtn.disabled = false;
                stopBtn.disabled = true;
                document.getElementById('currentStatus').textContent = 'Idle';
            }
        }

        function displayOpportunities(opportunities) {
            const container = document.getElementById('opportunitiesContainer');

            if (opportunities.length === 0) {
                container.innerHTML = `
                    <div class="no-opportunities">
                        <div style="font-size: 3rem;">🔍</div>
                        <p>Menunggu peluang arbitrase...</p>
                    </div>
                `;
                return;
            }

            let html = '';
            opportunities.forEach(opp => {
                html += `
                    <div class="opportunity-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <div style="font-weight: 700; color: var(--accent-cyan);">${opp.token_symbol || 'Unknown'}</div>
                            <div style="background: linear-gradient(45deg, var(--accent-green), var(--accent-cyan)); color: white; padding: 0.3rem 0.8rem; border-radius: 20px; font-weight: 600;">
                                ${opp.profit_percentage || 0}%
                            </div>
                        </div>
                        <div style="color: var(--accent-purple); font-size: 0.9rem; margin-bottom: 0.5rem;">
                            ✅ Validated Pair: ${opp.validated_pair || 'N/A'}
                        </div>
                        <div style="font-size: 0.9rem; color: var(--text-secondary);">
                            Buy: $${opp.buy_price || 0} | Sell: $${opp.sell_price || 0}
                        </div>
                        <div style="font-size: 0.8rem; color: var(--text-muted); margin-top: 0.5rem;">
                            ${opp.buy_exchange || 'Unknown'} → ${opp.sell_exchange || 'Unknown'}
                        </div>
                        <div style="color: var(--accent-green); font-size: 0.8rem; margin-top: 0.5rem;">
                            ${opp.validation_status || '✅ VALIDATED'}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
            document.getElementById('opportunitiesCount').textContent = opportunities.length;
        }

        function addLogEntry(message, level = 'info') {
            const logsContainer = document.getElementById('logsContainer');
            const timestamp = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.innerHTML = `
                <span class="log-time">[${timestamp}]</span>
                <span class="log-message">${message}</span>
            `;

            logsContainer.insertBefore(logEntry, logsContainer.firstChild);

            while (logsContainer.children.length > 100) {
                logsContainer.removeChild(logsContainer.lastChild);
            }
        }

        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('currentStatus').textContent = data.status || 'Idle';
                    document.getElementById('scanCount').textContent = data.scan_count || 0;
                    document.getElementById('currentAction').textContent = data.progress?.current_action || 'Menunggu...';

                    if (data.opportunities && data.opportunities.length > 0) {
                        displayOpportunities(data.opportunities);
                    }

                    updateButtonStates(data.is_running);
                })
                .catch(e => console.error('Status update failed:', e));
        }

        // Initialize UI
        document.addEventListener('DOMContentLoaded', function() {
            addLogEntry('🚀 Enhanced Crypto Arbitrage Bot v3.0 siap digunakan', 'info');
            addLogEntry('💡 Gunakan panel kontrol untuk memulai scanning', 'info');
            addLogEntry('🔍 Bot akan mencari peluang arbitrase dengan validasi pair yang ketat', 'info');
            addLogEntry('💎 Sistem pair validation mencegah false arbitrage signals', 'info');

            // Start status polling
            setInterval(updateStatus, 3000);
        });
    </script>
</body>
</html>
    """

    # Flask Routes
    @app.route('/')
    def index():
        """Main dashboard"""
        return render_template_string(SIMPLE_HTML_TEMPLATE)

    @app.route('/api/status')
    def api_get_status():
        """Get current bot status"""
        return jsonify({
            'status': web_app_state['status'],
            'is_running': web_app_state['is_running'],
            'opportunities': web_app_state['opportunities'][-10:],
            'scan_count': web_app_state['scan_count'],
            'logs': list(web_app_state['logs'])[-20:],
            'statistics': web_app_state['statistics'],
            'progress': web_app_state['progress']
        })

    @app.route('/api/start', methods=['POST'])
    def api_start_bot():
        """Start the arbitrage bot"""
        if not web_app_state['is_running']:
            start_web_arbitrage_bot()
            return jsonify({'success': True, 'message': 'Bot started'})
        else:
            return jsonify({'success': False, 'message': 'Bot already running'})

    @app.route('/api/stop', methods=['POST'])
    def api_stop_bot():
        """Stop the arbitrage bot"""
        if web_app_state['is_running']:
            stop_web_arbitrage_bot()
            return jsonify({'success': True, 'message': 'Bot stopped'})
        else:
            return jsonify({'success': False, 'message': 'Bot not running'})

    @app.route('/api/opportunities')
    def api_get_opportunities():
        """Get current arbitrage opportunities"""
        return jsonify({
            'opportunities': web_app_state['opportunities'],
            'count': len(web_app_state['opportunities'])
        })

    @app.route('/api/logs')
    def api_get_logs():
        """Get recent logs"""
        return jsonify({
            'logs': list(web_app_state['logs'])
        })

if __name__ == "__main__":
    print("🚀 Starting Enhanced Crypto Arbitrage Bot v3.0")
    print("📊 Features: Pair Validation, Dark UI, Multi-Chain Support")
    print("💎 Zero false arbitrage signals with advanced validation")
    print("🌐 Dark futuristic web interface")
    print("=" * 80)

    if FLASK_AVAILABLE:
        print("🌐 Web interface: http://localhost:5000")
        print("📱 Dark futuristic UI with real-time controls")
        print("🔍 Live arbitrage opportunity tracking")
        print("=" * 80)

        # Add initial log entries
        add_web_log("🚀 Enhanced Crypto Arbitrage Bot v3.0 initialized", "success")
        add_web_log("💎 Pair validation system active - zero false signals", "info")
        add_web_log("🌐 Multi-chain support: Ethereum, Solana, BSC, Polygon, Arbitrum", "info")
        add_web_log("⚙️ Real-time controls ready", "info")

        try:
            print("🚀 Starting Flask server...")
            app.run(host='0.0.0.0', port=5000, debug=False)
        except Exception as e:
            print(f"⚠️ Server failed: {e}")
    else:
        print("⚠️ Flask not available - running console mode only")
        print("💡 Install Flask for web interface: pip install Flask aiohttp requests")
        print("🔄 Starting console-based arbitrage detection...")

        # Run console mode
        async def console_mode():
            while True:
                try:
                    print("\n" + "="*60)
                    print("🔍 Starting arbitrage scan...")
                    await detector.scan_for_arbitrage_opportunities()

                    if detector.opportunities:
                        print(f"✅ Found {len(detector.opportunities)} opportunities!")
                        for i, opp in enumerate(detector.opportunities[-5:], 1):  # Show last 5
                            print(f"  {i}. {opp.get('token_symbol', 'Unknown')} - {opp.get('profit_percentage', 0)}% profit")
                    else:
                        print("📊 No arbitrage opportunities found in this scan")

                    print("⏳ Waiting 60 seconds before next scan...")
                    await asyncio.sleep(60)

                except KeyboardInterrupt:
                    print("\n🛑 Stopping bot...")
                    break
                except Exception as e:
                    print(f"❌ Error: {e}")
                    await asyncio.sleep(30)

        asyncio.run(console_mode())

