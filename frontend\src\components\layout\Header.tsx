import React from 'react'
import { motion } from 'framer-motion'
import Button from '@components/ui/Button'
import Badge from '@components/ui/Badge'
import {
  Bars3Icon,
  BellIcon,
  CogIcon,
  PlayIcon,
  PauseIcon,
  SignalIcon,
  ClockIcon,
} from '@heroicons/react/24/outline'

interface HeaderProps {
  sidebarOpen: boolean
  onToggleSidebar: () => void
  wsConnected: boolean
  isScanning: boolean
  opportunitiesCount: number
}

const Header: React.FC<HeaderProps> = ({
  sidebarOpen,
  onToggleSidebar,
  wsConnected,
  isScanning,
  opportunitiesCount
}) => {
  const currentTime = new Date().toLocaleTimeString()

  return (
    <header className="bg-dark-900/80 backdrop-blur-lg border-b border-dark-700/50 sticky top-0 z-40">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left side */}
          <div className="flex items-center space-x-4">
            {/* Sidebar toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleSidebar}
              icon={<Bars3Icon className="w-5 h-5" />}
              className="lg:hidden"
            />

            {/* Logo for mobile when sidebar is closed */}
            {!sidebarOpen && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex items-center space-x-2 lg:hidden"
              >
                <div className="w-8 h-8 bg-gradient-cyber rounded-lg flex items-center justify-center">
                  <SignalIcon className="w-5 h-5 text-white" />
                </div>
                <span className="text-lg font-bold text-gradient">ArbiBot</span>
              </motion.div>
            )}

            {/* Status indicators */}
            <div className="hidden sm:flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  isScanning ? 'bg-success animate-pulse' : 'bg-gray-600'
                }`} />
                <span className="text-sm text-gray-300">
                  {isScanning ? 'Scanning' : 'Idle'}
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <SignalIcon className={`w-4 h-4 ${
                  wsConnected ? 'text-success' : 'text-error'
                }`} />
                <span className="text-sm text-gray-300">
                  {wsConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <ClockIcon className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-300 font-mono">
                  {currentTime}
                </span>
              </div>
            </div>
          </div>

          {/* Center - Quick stats */}
          <div className="hidden md:flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-400">Opportunities:</span>
              <Badge variant="info" glow={opportunitiesCount > 0}>
                {opportunitiesCount}
              </Badge>
            </div>

            {isScanning && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex items-center space-x-2"
              >
                <div className="w-2 h-2 bg-neon-blue rounded-full animate-ping" />
                <span className="text-sm text-neon-blue font-medium">
                  Live Scanning
                </span>
              </motion.div>
            )}
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-3">
            {/* Quick scan toggle */}
            <Button
              variant={isScanning ? 'error' : 'success'}
              size="sm"
              icon={isScanning ? <PauseIcon className="w-4 h-4" /> : <PlayIcon className="w-4 h-4" />}
              className="hidden sm:flex"
            >
              {isScanning ? 'Stop' : 'Start'}
            </Button>

            {/* Notifications */}
            <Button
              variant="ghost"
              size="sm"
              icon={<BellIcon className="w-5 h-5" />}
              className="relative"
            >
              {/* Notification badge */}
              {opportunitiesCount > 0 && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute -top-1 -right-1 w-5 h-5 bg-error rounded-full flex items-center justify-center"
                >
                  <span className="text-xs text-white font-medium">
                    {opportunitiesCount > 9 ? '9+' : opportunitiesCount}
                  </span>
                </motion.div>
              )}
            </Button>

            {/* Settings */}
            <Button
              variant="ghost"
              size="sm"
              icon={<CogIcon className="w-5 h-5" />}
            />

            {/* Connection status indicator */}
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                wsConnected 
                  ? 'bg-success shadow-neon-green' 
                  : 'bg-error animate-pulse'
              }`} />
              <span className="text-xs text-gray-400 hidden lg:block">
                {wsConnected ? 'Online' : 'Offline'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile status bar */}
      <div className="sm:hidden px-4 py-2 border-t border-dark-700/50">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full ${
                isScanning ? 'bg-success' : 'bg-gray-600'
              }`} />
              <span className="text-gray-300">
                {isScanning ? 'Scanning' : 'Idle'}
              </span>
            </div>

            <div className="flex items-center space-x-1">
              <SignalIcon className={`w-3 h-3 ${
                wsConnected ? 'text-success' : 'text-error'
              }`} />
              <span className="text-gray-300">
                {wsConnected ? 'Online' : 'Offline'}
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-gray-400">Opportunities:</span>
            <Badge variant="info" size="xs">
              {opportunitiesCount}
            </Badge>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
