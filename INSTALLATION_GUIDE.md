# 🚀 Installation Guide - Crypto Arbitrage Bot v2.0

## 📋 Prerequisites

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, or Linux
- **Python**: Version 3.9 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 2GB free space
- **Internet**: Stable connection for API calls

### Optional Components
- **Redis**: For enhanced caching (can use in-memory cache without it)
- **PostgreSQL**: For production database (SQLite works for development)

## 🔧 Step-by-Step Installation

### Step 1: Install Python

#### Windows
1. Download Python from [python.org](https://www.python.org/downloads/)
2. Run installer and **check "Add Python to PATH"**
3. Verify installation:
   ```cmd
   python --version
   # Should show: Python 3.9.x or higher
   ```

#### macOS
```bash
# Using Homebrew (recommended)
brew install python@3.9

# Or download from python.org
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3.9 python3.9-pip python3.9-venv
```

### Step 2: Clone the Repository

```bash
# Clone the repository
git clone https://github.com/your-username/crypto-arbitrage-bot.git
cd crypto-arbitrage-bot

# Or download ZIP and extract
```

### Step 3: Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Verify activation (should show (venv) in prompt)
```

### Step 4: Install Dependencies

```bash
# Install all required packages
pip install -r requirements.txt

# Verify installation
pip list | grep fastapi
# Should show: fastapi 0.104.1
```

### Step 5: Configuration (Optional)

```bash
# Copy environment template
cp .env.example .env

# Edit .env file (optional - defaults work fine)
# No API keys needed!
```

### Step 6: Test Installation

```bash
# Test basic functionality
python simple_test.py

# Test public APIs (requires internet)
python test_public_apis.py

# Expected output:
# 🦎 Testing CoinGecko API...
#    ✅ Prices: BTC=$45000, ETH=$3000
# 📊 Testing DexScreener API...
#    ✅ Found 150 USDC pairs
# ...
# 🎯 Overall: 6/6 tests passed
```

### Step 7: Start the Application

```bash
# Easy startup (recommended)
python start.py

# Or manual startup
python main.py

# Or with uvicorn directly
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### Step 8: Access the Application

Open your browser and navigate to:
- **🌐 Web Interface**: http://localhost:8000
- **📚 API Documentation**: http://localhost:8000/docs
- **❤️ Health Check**: http://localhost:8000/health

## 🐳 Docker Installation (Alternative)

### Quick Docker Setup

```bash
# Build and start with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Manual Docker Build

```bash
# Build backend image
docker build -t arbitrage-bot-backend .

# Build frontend image
cd frontend
docker build -t arbitrage-bot-frontend .

# Run backend
docker run -p 8000:8000 arbitrage-bot-backend

# Run frontend
docker run -p 3000:3000 arbitrage-bot-frontend
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Python Not Found
```bash
# Error: 'python' is not recognized
# Solution: Add Python to PATH or use 'py' command on Windows
py --version
```

#### 2. Permission Denied
```bash
# Error: Permission denied
# Solution: Use --user flag or run as administrator
pip install --user -r requirements.txt
```

#### 3. Module Not Found
```bash
# Error: ModuleNotFoundError: No module named 'fastapi'
# Solution: Activate virtual environment and reinstall
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
```

#### 4. Port Already in Use
```bash
# Error: Port 8000 is already in use
# Solution: Use different port
python main.py --port 8001
# Or: uvicorn main:app --port 8001
```

#### 5. API Connection Issues
```bash
# Error: Failed to connect to APIs
# Solution: Check internet connection and firewall
curl -I https://api.coingecko.com/api/v3/ping
```

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG  # Linux/macOS
set LOG_LEVEL=DEBUG     # Windows

# Run with debug
python main.py
```

### Reset Installation

```bash
# Clean virtual environment
deactivate
rm -rf venv  # Linux/macOS
rmdir /s venv  # Windows

# Start fresh
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate
pip install -r requirements.txt
```

## 📊 Verification Checklist

After installation, verify these components work:

- [ ] **Python Environment**: `python --version` shows 3.9+
- [ ] **Dependencies**: `pip list` shows fastapi, httpx, etc.
- [ ] **Basic Test**: `python simple_test.py` passes all tests
- [ ] **API Test**: `python test_public_apis.py` connects to APIs
- [ ] **Web Interface**: http://localhost:8000 loads successfully
- [ ] **API Docs**: http://localhost:8000/docs shows Swagger UI
- [ ] **Health Check**: http://localhost:8000/health returns OK

## 🚀 Quick Start Commands

```bash
# Complete setup in one go
git clone https://github.com/your-username/crypto-arbitrage-bot.git
cd crypto-arbitrage-bot
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python test_public_apis.py
python start.py
```

## 🔧 Development Setup

For developers who want to contribute:

```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Run tests
pytest

# Run linting
black .
isort .
flake8 .

# Type checking
mypy backend/
```

## 📱 Frontend Development

```bash
# Navigate to frontend directory
cd frontend

# Install Node.js dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## 🌐 Production Deployment

### Environment Variables
```bash
# Production .env
DEBUG=false
DATABASE_URL="postgresql://user:pass@localhost/arbitrage_bot"
REDIS_URL="redis://localhost:6379/0"
```

### Systemd Service (Linux)
```ini
# /etc/systemd/system/arbitrage-bot.service
[Unit]
Description=Crypto Arbitrage Bot
After=network.target

[Service]
Type=simple
User=arbitrage
WorkingDirectory=/opt/arbitrage-bot
Environment=PATH=/opt/arbitrage-bot/venv/bin
ExecStart=/opt/arbitrage-bot/venv/bin/python main.py
Restart=always

[Install]
WantedBy=multi-user.target
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /ws {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 📞 Support

If you encounter issues:

1. **Check the logs**: Look for error messages in the console
2. **Verify internet connection**: APIs require internet access
3. **Update dependencies**: `pip install --upgrade -r requirements.txt`
4. **Check GitHub Issues**: Search for similar problems
5. **Create new issue**: Provide error logs and system info

## 🎉 Success!

If you see this output, installation was successful:

```
🚀 Crypto Arbitrage Bot v2.0 - Live Demo Simulation
======================================================================
📊 Using Public APIs: CoinGecko, DexScreener, Binance, Reddit
🔑 No API keys required - all endpoints are public!

✅ Found 3 arbitrage opportunities
🧠 Sentiment analysis: BTC bullish, ETH bullish
📈 Market data updated successfully
🎉 Demo completed successfully!
```

**🎯 You're ready to start detecting crypto arbitrage opportunities!**
