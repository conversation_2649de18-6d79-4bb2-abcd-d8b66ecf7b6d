name: arbitrage-bot
channels:
  - conda-forge
  - defaults
dependencies:
  # Python version
  - python=3.11
  
  # Core ML packages (pre-compiled, no compilation issues)
  - scikit-learn>=1.3.0
  - pandas>=2.0.0
  - numpy>=1.24.0
  
  # Web framework
  - fastapi>=0.100.0
  - uvicorn>=0.20.0
  
  # HTTP clients
  - aiohttp>=3.8.0
  - requests>=2.30.0
  
  # Data processing
  - python-dateutil>=2.8.0
  
  # Optional: Database (if you want to use PostgreSQL later)
  # - postgresql
  # - psycopg2
  
  # Optional: Redis (if you want caching)
  # - redis-py
  
  # Pip dependencies (not available in conda)
  - pip
  - pip:
    - httpx>=0.25.0
    - python-multipart>=0.0.6
    - websockets>=11.0.0
    - python-dotenv>=1.0.0
    - pydantic>=2.0.0
    - structlog>=23.0.0
    - aiofiles>=23.0.0
    - textblob>=0.17.0
