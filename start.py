#!/usr/bin/env python3
"""
Crypto Arbitrage Bot v2.0 - Startup Script
Easy startup script for the public API version
"""
import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """Print startup banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🚀 Crypto Arbitrage Bot v2.0 - Public APIs Only      ║
    ║                                                              ║
    ║        ✅ No API Keys Required                               ║
    ║        ✅ Free Public Endpoints                              ║
    ║        ✅ ML-Enhanced Detection                              ║
    ║        ✅ Real-time WebSocket Updates                        ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 9):
        print("❌ Error: Python 3.9+ is required")
        print(f"   Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version.split()[0]}")

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        "fastapi", "uvicorn", "httpx", "sqlalchemy", "redis"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("   Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies installed")
    return True

def setup_environment():
    """Setup environment variables"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("📝 Creating .env file...")
        env_content = """# Crypto Arbitrage Bot v2.0 - Public APIs Configuration
# No API keys required!

# Application Settings
APP_NAME="Crypto Arbitrage Bot v2.0"
DEBUG=true
HOST="0.0.0.0"
PORT=8000

# Database (SQLite for development)
DATABASE_URL="sqlite:///./arbitrage.db"

# Redis (optional, for caching)
REDIS_URL="redis://localhost:6379/0"

# Public APIs Configuration
USE_PUBLIC_APIS_ONLY=true

# Rate Limiting (conservative settings)
COINGECKO_RATE_LIMIT=10
DEXSCREENER_RATE_LIMIT=60
BINANCE_RATE_LIMIT=300
REDDIT_RATE_LIMIT=60

# ML Settings
ML_MODEL_UPDATE_INTERVAL=3600
SENTIMENT_ANALYSIS_ENABLED=true
PATTERN_RECOGNITION_ENABLED=true

# Logging
LOG_LEVEL="INFO"
"""
        env_file.write_text(env_content)
        print("✅ Environment file created")
    else:
        print("✅ Environment file exists")

def check_redis():
    """Check if Redis is available (optional)"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis connection successful")
        return True
    except Exception:
        print("⚠️  Redis not available (optional - will use in-memory cache)")
        return False

def start_backend():
    """Start the backend server"""
    print("\n🚀 Starting Crypto Arbitrage Bot backend...")
    print("📊 Data Sources:")
    print("   🦎 CoinGecko (market data)")
    print("   📊 DexScreener (DEX pairs)")
    print("   🔶 Binance (CEX data)")
    print("   💬 Reddit (sentiment)")
    print("\n🌐 Access URLs:")
    print("   📱 Web Interface: http://localhost:8000")
    print("   📚 API Docs: http://localhost:8000/docs")
    print("   ❤️  Health Check: http://localhost:8000/health")
    print("\n" + "="*60)
    
    try:
        # Start the FastAPI server
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n\n👋 Shutting down Crypto Arbitrage Bot...")
        print("   Thank you for using the public API version!")

def main():
    """Main startup function"""
    print_banner()
    
    print("🔍 Checking system requirements...")
    check_python_version()
    
    if not check_dependencies():
        print("\n💡 To install dependencies:")
        print("   pip install -r requirements.txt")
        sys.exit(1)
    
    setup_environment()
    check_redis()
    
    print("\n✅ All checks passed!")
    print("🎯 Starting in 3 seconds...")
    time.sleep(3)
    
    start_backend()

if __name__ == "__main__":
    main()
