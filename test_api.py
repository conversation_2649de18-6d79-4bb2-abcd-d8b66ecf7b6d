#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import json

async def test_v3_apis():
    """Test v3.0 API endpoints"""
    
    base_url = "http://localhost:8000"
    
    endpoints = [
        "/api/v3/tier-stats",
        "/api/v3/profit-thresholds", 
        "/api/v3/token-distribution",
        "/api/status",
        "/api/token-categories"
    ]
    
    async with aiohttp.ClientSession() as session:
        print("🚀 Testing Enhanced Crypto Arbitrage Bot v3.0 APIs\n")
        
        for endpoint in endpoints:
            try:
                url = f"{base_url}{endpoint}"
                print(f"Testing: {endpoint}")
                
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ {endpoint}: SUCCESS")
                        
                        # Show key information
                        if endpoint == "/api/v3/tier-stats":
                            if 'tier_statistics' in data:
                                stats = data['tier_statistics']
                                print(f"   📊 Total tokens: {stats.get('total_tokens', 0)}")
                                tier_stats = stats.get('tier_stats', {})
                                for tier, info in tier_stats.items():
                                    print(f"   🎯 {tier}: {info.get('token_count', 0)} tokens")
                        
                        elif endpoint == "/api/v3/profit-thresholds":
                            if 'profit_thresholds' in data:
                                thresholds = data['profit_thresholds']
                                volatility = thresholds.get('current_volatility', 'unknown')
                                print(f"   📈 Market volatility: {volatility}")
                                
                        elif endpoint == "/api/v3/token-distribution":
                            if 'token_distribution' in data:
                                dist = data['token_distribution']
                                total = dist.get('total_tokens', 0)
                                blockchain_dist = dist.get('blockchain_distribution', {})
                                print(f"   🌐 Total tokens: {total}")
                                for blockchain, count in blockchain_dist.items():
                                    print(f"   {blockchain}: {count} tokens")
                        
                        elif endpoint == "/api/status":
                            print(f"   🔄 Status: {data.get('status', 'unknown')}")
                            
                        elif endpoint == "/api/token-categories":
                            if 'categories' in data:
                                categories = data['categories']
                                print(f"   📂 Categories: {len(categories)} available")
                        
                    else:
                        print(f"❌ {endpoint}: HTTP {response.status}")
                        
            except Exception as e:
                print(f"❌ {endpoint}: Error - {e}")
            
            print()
        
        print("🎯 v3.0 API Testing Complete!")

if __name__ == "__main__":
    asyncio.run(test_v3_apis())
