#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import uvicorn
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_app():
    """Run the Enhanced Crypto Arbitrage Bot v3.0"""
    print("🚀 Starting Enhanced Crypto Arbitrage Bot v3.0")
    print("📊 Features: 1000+ Tokens, Intelligent Tiers, Dynamic Thresholds, Advanced Security")
    print("🌐 Web interface: http://localhost:8000")
    print("📚 API docs: http://localhost:8000/docs")
    print("=" * 60)
    
    try:
        # Import the app
        from main_fixed import app
        
        # Run with uvicorn
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True
        )
        
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_app()
