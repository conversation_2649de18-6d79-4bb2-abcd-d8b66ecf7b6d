"""
Advanced arbitrage detection engine with ML integration
"""
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import uuid
from backend.services.data_aggregator import data_aggregator
from backend.ml.price_predictor import price_prediction_service
from backend.ml.sentiment_analyzer import sentiment_service
from backend.ml.pattern_recognition import pattern_engine
from backend.core.logging import get_logger, ArbitrageLogger
from backend.core.cache import cache
from backend.config.settings import settings
from backend.models.arbitrage import ArbitrageOpportunity
from backend.core.database import AsyncSessionLocal

logger = get_logger(__name__)
arbitrage_logger = ArbitrageLogger()


class OpportunityScorer:
    """Score arbitrage opportunities using multiple factors"""
    
    @staticmethod
    def calculate_comprehensive_score(
        opportunity: Dict[str, Any],
        ml_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate comprehensive opportunity score"""
        
        base_profit = opportunity.get("profit_percentage", 0)
        
        # Base score from profit percentage
        profit_score = min(1.0, base_profit / 10.0)  # Normalize to 10% max
        
        # ML enhancement factors
        price_prediction = ml_data.get("price_prediction", {})
        sentiment = ml_data.get("sentiment", {})
        patterns = ml_data.get("patterns", {})
        
        # Price prediction factor
        prediction_factor = 1.0
        if price_prediction.get("predictions"):
            for horizon, pred in price_prediction["predictions"].items():
                if pred.get("status") == "success":
                    predicted_change = pred.get("predicted_change_percentage", 0)
                    confidence = pred.get("confidence_score", 0)
                    
                    # Positive prediction enhances score
                    if predicted_change > 0:
                        prediction_factor += (predicted_change / 100) * confidence * 0.3
                    else:
                        prediction_factor += (predicted_change / 100) * confidence * 0.2
        
        # Sentiment factor
        sentiment_factor = 1.0
        if sentiment.get("overall_sentiment"):
            sentiment_score = sentiment["overall_sentiment"].get("score", 0)
            sentiment_confidence = sentiment["overall_sentiment"].get("confidence", 0)
            
            # Positive sentiment enhances score
            sentiment_factor += sentiment_score * sentiment_confidence * 0.2
        
        # Pattern factor
        pattern_factor = 1.0
        if patterns.get("pattern_score"):
            bullish_score = patterns["pattern_score"].get("bullish_score", 0)
            bearish_score = patterns["pattern_score"].get("bearish_score", 0)
            
            # Bullish patterns enhance score, bearish patterns reduce it
            pattern_factor += (bullish_score - bearish_score) * 0.25
        
        # Volume factor
        volume_factor = 1.0
        buy_liquidity = opportunity.get("buy_liquidity_usd", 0)
        sell_liquidity = opportunity.get("sell_liquidity_usd", 0)
        min_liquidity = min(buy_liquidity, sell_liquidity)
        
        if min_liquidity > 100000:  # $100k+
            volume_factor = 1.2
        elif min_liquidity > 50000:  # $50k+
            volume_factor = 1.1
        elif min_liquidity < 10000:  # Less than $10k
            volume_factor = 0.8
        
        # Risk factor
        risk_factor = 1.0
        chain_id = opportunity.get("chain_id", "")
        
        # Lower risk for established chains
        if chain_id in ["ethereum", "bsc", "polygon"]:
            risk_factor = 1.1
        elif chain_id in ["arbitrum", "avalanche"]:
            risk_factor = 1.05
        else:
            risk_factor = 0.95
        
        # Calculate final score
        final_score = (
            profit_score * 
            prediction_factor * 
            sentiment_factor * 
            pattern_factor * 
            volume_factor * 
            risk_factor
        )
        
        # Cap the score at reasonable maximum
        final_score = min(2.0, final_score)
        
        return {
            "total_score": final_score,
            "profit_score": profit_score,
            "prediction_factor": prediction_factor,
            "sentiment_factor": sentiment_factor,
            "pattern_factor": pattern_factor,
            "volume_factor": volume_factor,
            "risk_factor": risk_factor,
            "confidence": min(1.0, final_score / 1.5)  # Normalize confidence
        }


class ArbitrageEngine:
    """Advanced arbitrage detection engine"""
    
    def __init__(self):
        self.session_id = None
        self.is_running = False
        self.opportunities = []
        self.scorer = OpportunityScorer()
    
    async def start_scanning(self, config: Dict[str, Any]) -> str:
        """Start arbitrage scanning session"""
        if self.is_running:
            raise ValueError("Scanning session already running")
        
        self.session_id = str(uuid.uuid4())
        self.is_running = True
        self.opportunities = []
        
        await arbitrage_logger.log_scan_started(self.session_id, config)
        
        # Initialize data aggregator
        await data_aggregator.initialize()
        
        logger.info(f"Started arbitrage scanning session: {self.session_id}")
        
        return self.session_id
    
    async def stop_scanning(self):
        """Stop arbitrage scanning session"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.session_id:
            await arbitrage_logger.log_scan_completed(
                self.session_id,
                0,  # Duration will be calculated elsewhere
                len(self.opportunities),
                0   # Tokens analyzed will be tracked elsewhere
            )
        
        logger.info(f"Stopped arbitrage scanning session: {self.session_id}")
    
    async def scan_for_opportunities(
        self, 
        config: Dict[str, Any],
        token_symbols: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Scan for arbitrage opportunities with ML enhancement"""
        
        if not token_symbols:
            # Get trending tokens from data aggregator
            trending_tokens = await data_aggregator.get_trending_opportunities(50)
            token_symbols = [token.get("symbol") for token in trending_tokens if token.get("symbol")]
        
        opportunities = []
        
        # Process tokens in batches
        batch_size = 10
        for i in range(0, len(token_symbols), batch_size):
            if not self.is_running:
                break
                
            batch = token_symbols[i:i + batch_size]
            batch_opportunities = await self._process_token_batch(batch, config)
            opportunities.extend(batch_opportunities)
            
            # Rate limiting between batches
            await asyncio.sleep(1)
        
        # Sort by score and filter
        opportunities.sort(key=lambda x: x.get("ml_score", {}).get("total_score", 0), reverse=True)
        
        # Apply filters
        filtered_opportunities = self._apply_filters(opportunities, config)
        
        # Update internal state
        self.opportunities = filtered_opportunities
        
        return filtered_opportunities
    
    async def _process_token_batch(
        self, 
        token_symbols: List[str], 
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Process a batch of tokens for arbitrage opportunities"""
        
        batch_opportunities = []
        
        for token_symbol in token_symbols:
            if not self.is_running:
                break
                
            try:
                # Get comprehensive token data
                token_data = await data_aggregator.get_comprehensive_token_data(token_symbol)
                
                if not token_data.get("aggregated", {}).get("price_usd"):
                    continue
                
                # Find DEX arbitrage opportunities
                dex_opportunities = await self._find_dex_arbitrage(token_symbol, token_data, config)
                
                # Find CEX vs DEX opportunities
                cex_opportunities = await self._find_cex_dex_arbitrage(token_symbol, token_data, config)
                
                # Combine opportunities
                all_opportunities = dex_opportunities + cex_opportunities
                
                # Enhance with ML analysis
                for opportunity in all_opportunities:
                    ml_enhancement = await self._enhance_with_ml(token_symbol, opportunity)
                    opportunity.update(ml_enhancement)
                
                batch_opportunities.extend(all_opportunities)
                
            except Exception as e:
                logger.error(f"Error processing token {token_symbol}: {e}")
                continue
        
        return batch_opportunities
    
    async def _find_dex_arbitrage(
        self, 
        token_symbol: str, 
        token_data: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Find DEX to DEX arbitrage opportunities"""
        
        opportunities = []
        
        # Get DexScreener data
        dexscreener_data = token_data.get("sources", {}).get("dexscreener", {})
        all_pairs = dexscreener_data.get("all_pairs", [])
        
        if len(all_pairs) < 2:
            return opportunities
        
        # Group pairs by quote token and chain
        grouped_pairs = {}
        for pair in all_pairs:
            quote_symbol = pair.get("quoteToken", {}).get("symbol", "")
            chain_id = pair.get("chainId", "")
            
            if quote_symbol and chain_id:
                key = f"{quote_symbol}_{chain_id}"
                if key not in grouped_pairs:
                    grouped_pairs[key] = []
                grouped_pairs[key].append(pair)
        
        # Find arbitrage opportunities within each group
        for group_key, pairs in grouped_pairs.items():
            if len(pairs) < 2:
                continue
            
            # Sort pairs by price
            valid_pairs = [
                pair for pair in pairs
                if (pair.get("priceUsd") and 
                    pair.get("liquidity", {}).get("usd", 0) > config.get("min_liquidity", 1000))
            ]
            
            if len(valid_pairs) < 2:
                continue
            
            valid_pairs.sort(key=lambda p: float(p.get("priceUsd", 0)))
            
            # Check each pair combination
            for i in range(len(valid_pairs)):
                for j in range(i + 1, len(valid_pairs)):
                    buy_pair = valid_pairs[i]
                    sell_pair = valid_pairs[j]
                    
                    opportunity = self._calculate_arbitrage_opportunity(
                        token_symbol, buy_pair, sell_pair, config
                    )
                    
                    if opportunity:
                        opportunities.append(opportunity)
        
        return opportunities
    
    async def _find_cex_dex_arbitrage(
        self, 
        token_symbol: str, 
        token_data: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Find CEX vs DEX arbitrage opportunities"""
        
        opportunities = []
        
        # Get CEX price (Binance)
        binance_data = token_data.get("sources", {}).get("binance", {})
        cex_price = binance_data.get("price")
        
        if not cex_price:
            return opportunities
        
        # Get DEX prices
        dexscreener_data = token_data.get("sources", {}).get("dexscreener", {})
        all_pairs = dexscreener_data.get("all_pairs", [])
        
        for pair in all_pairs:
            dex_price = pair.get("priceUsd")
            if not dex_price:
                continue
            
            dex_price = float(dex_price)
            liquidity = pair.get("liquidity", {}).get("usd", 0)
            
            if liquidity < config.get("min_liquidity", 1000):
                continue
            
            # Calculate profit potential
            if dex_price < cex_price:  # Buy DEX, sell CEX
                profit_percentage = ((cex_price - dex_price) / dex_price) * 100
                direction = "dex_to_cex"
            else:  # Buy CEX, sell DEX
                profit_percentage = ((dex_price - cex_price) / cex_price) * 100
                direction = "cex_to_dex"
            
            if profit_percentage > config.get("min_profit", 0.5):
                opportunity = {
                    "type": "cex_dex_arbitrage",
                    "direction": direction,
                    "token_symbol": token_symbol,
                    "profit_percentage": profit_percentage,
                    "cex_price": cex_price,
                    "dex_price": dex_price,
                    "dex_info": pair,
                    "liquidity_usd": liquidity,
                    "chain_id": pair.get("chainId", ""),
                    "discovered_at": datetime.utcnow().isoformat()
                }
                opportunities.append(opportunity)
        
        return opportunities
    
    def _calculate_arbitrage_opportunity(
        self, 
        token_symbol: str, 
        buy_pair: Dict[str, Any], 
        sell_pair: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Calculate arbitrage opportunity between two DEX pairs"""
        
        buy_price = float(buy_pair.get("priceUsd", 0))
        sell_price = float(sell_pair.get("priceUsd", 0))
        
        if buy_price <= 0 or sell_price <= 0 or buy_price >= sell_price:
            return None
        
        profit_percentage = ((sell_price - buy_price) / buy_price) * 100
        
        if profit_percentage < config.get("min_profit", 0.5):
            return None
        
        buy_liquidity = buy_pair.get("liquidity", {}).get("usd", 0)
        sell_liquidity = sell_pair.get("liquidity", {}).get("usd", 0)
        
        return {
            "type": "dex_arbitrage",
            "token_symbol": token_symbol,
            "base_token_address": buy_pair.get("baseToken", {}).get("address", ""),
            "quote_token_symbol": buy_pair.get("quoteToken", {}).get("symbol", ""),
            "chain_id": buy_pair.get("chainId", ""),
            "buy_dex_id": buy_pair.get("dexId", ""),
            "buy_price_usd": buy_price,
            "buy_liquidity_usd": buy_liquidity,
            "buy_url": buy_pair.get("url", ""),
            "sell_dex_id": sell_pair.get("dexId", ""),
            "sell_price_usd": sell_price,
            "sell_liquidity_usd": sell_liquidity,
            "sell_url": sell_pair.get("url", ""),
            "profit_percentage": profit_percentage,
            "discovered_at": datetime.utcnow().isoformat()
        }
    
    async def _enhance_with_ml(
        self, 
        token_symbol: str, 
        opportunity: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Enhance opportunity with ML analysis"""
        
        ml_data = {}
        
        try:
            # Get ML analysis concurrently
            tasks = [
                self._get_price_prediction(token_symbol),
                self._get_sentiment_analysis(token_symbol),
                self._get_pattern_analysis(token_symbol)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            if isinstance(results[0], dict):
                ml_data["price_prediction"] = results[0]
            if isinstance(results[1], dict):
                ml_data["sentiment"] = results[1]
            if isinstance(results[2], dict):
                ml_data["patterns"] = results[2]
            
            # Calculate ML-enhanced score
            ml_score = self.scorer.calculate_comprehensive_score(opportunity, ml_data)
            
            return {
                "ml_data": ml_data,
                "ml_score": ml_score
            }
            
        except Exception as e:
            logger.error(f"Error enhancing opportunity with ML for {token_symbol}: {e}")
            return {"ml_data": {}, "ml_score": {"total_score": 0.5, "confidence": 0.0}}
    
    async def _get_price_prediction(self, token_symbol: str) -> Dict[str, Any]:
        """Get price prediction for token"""
        # This would require historical price data
        # For now, return empty prediction
        return {}
    
    async def _get_sentiment_analysis(self, token_symbol: str) -> Dict[str, Any]:
        """Get sentiment analysis for token"""
        try:
            return await sentiment_service.get_sentiment_analysis(token_symbol)
        except Exception as e:
            logger.error(f"Error getting sentiment for {token_symbol}: {e}")
            return {}
    
    async def _get_pattern_analysis(self, token_symbol: str) -> Dict[str, Any]:
        """Get pattern analysis for token"""
        # This would require historical price data
        # For now, return empty analysis
        return {}
    
    def _apply_filters(
        self, 
        opportunities: List[Dict[str, Any]], 
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Apply filters to opportunities"""
        
        filtered = []
        
        for opp in opportunities:
            # Profit filter
            if opp.get("profit_percentage", 0) < config.get("min_profit", 0.5):
                continue
            
            # Liquidity filter
            min_liquidity = min(
                opp.get("buy_liquidity_usd", 0),
                opp.get("sell_liquidity_usd", 0),
                opp.get("liquidity_usd", 0)
            )
            
            if min_liquidity < config.get("min_liquidity", 1000):
                continue
            
            # ML score filter
            ml_score = opp.get("ml_score", {}).get("total_score", 0)
            if ml_score < config.get("min_ml_score", 0.3):
                continue
            
            filtered.append(opp)
        
        return filtered


# Global arbitrage engine instance
arbitrage_engine = ArbitrageEngine()
