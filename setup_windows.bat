@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║        🚀 Crypto Arbitrage Bot v2.0 - Windows Setup         ║
echo ║                                                              ║
echo ║        ✅ Conda Environment Setup                            ║
echo ║        ✅ Pre-compiled Packages                              ║
echo ║        ✅ No Compilation Issues                              ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Checking conda installation...
conda --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Conda not found!
    echo 💡 Please install Miniconda or Anaconda first:
    echo    https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

echo ✅ Conda found!
echo.

echo 🔧 Creating conda environment...
conda env remove -n arbitrage-bot -y >nul 2>&1
conda create -n arbitrage-bot python=3.11 -y

if errorlevel 1 (
    echo ❌ Failed to create environment
    pause
    exit /b 1
)

echo ✅ Environment created!
echo.

echo 📦 Installing core packages via conda...
call conda activate arbitrage-bot
conda install -c conda-forge scikit-learn pandas numpy fastapi uvicorn aiohttp requests python-dateutil -y

if errorlevel 1 (
    echo ❌ Failed to install conda packages
    pause
    exit /b 1
)

echo ✅ Conda packages installed!
echo.

echo 📦 Installing additional packages via pip...
pip install httpx python-multipart websockets python-dotenv pydantic structlog aiofiles textblob

if errorlevel 1 (
    echo ⚠️  Some pip packages failed, but continuing...
)

echo ✅ Pip packages installed!
echo.

echo 🧪 Testing installation...
python -c "import fastapi, uvicorn, httpx, sklearn, pandas, numpy; print('✅ All core packages working!')"

if errorlevel 1 (
    echo ❌ Some packages failed to import
    echo ⚠️  But basic functionality should still work
)

echo.
echo 📝 Creating startup scripts...

echo @echo off > start_bot.bat
echo echo 🐍 Activating environment... >> start_bot.bat
echo call conda activate arbitrage-bot >> start_bot.bat
echo echo ✅ Environment activated! >> start_bot.bat
echo echo. >> start_bot.bat
echo echo 🚀 Starting Crypto Arbitrage Bot... >> start_bot.bat
echo echo 🌐 Web interface: http://localhost:8000 >> start_bot.bat
echo echo 📚 API docs: http://localhost:8000/docs >> start_bot.bat
echo echo. >> start_bot.bat
echo python main.py >> start_bot.bat
echo pause >> start_bot.bat

echo @echo off > start_simple.bat
echo echo 🐍 Activating environment... >> start_simple.bat
echo call conda activate arbitrage-bot >> start_simple.bat
echo echo ✅ Environment activated! >> start_simple.bat
echo echo. >> start_simple.bat
echo echo 🚀 Starting Simplified Bot... >> start_simple.bat
echo echo 🌐 Web interface: http://localhost:8000 >> start_simple.bat
echo echo. >> start_simple.bat
echo python main_simple.py >> start_simple.bat
echo pause >> start_simple.bat

echo @echo off > test_apis.bat
echo echo 🐍 Activating environment... >> test_apis.bat
echo call conda activate arbitrage-bot >> test_apis.bat
echo echo ✅ Environment activated! >> test_apis.bat
echo echo. >> test_apis.bat
echo echo 🧪 Testing public APIs... >> test_apis.bat
echo python test_public_apis.py >> test_apis.bat
echo pause >> test_apis.bat

echo ✅ Startup scripts created!
echo.

echo ═══════════════════════════════════════════════════════════════
echo 🎉 Setup completed successfully!
echo.
echo 🚀 To start the bot, choose one of these options:
echo.
echo   1. 📱 Full Version (with ML):
echo      Double-click: start_bot.bat
echo.
echo   2. ⚡ Simplified Version (lightweight):
echo      Double-click: start_simple.bat
echo.
echo   3. 🧪 Test APIs first:
echo      Double-click: test_apis.bat
echo.
echo   4. 💻 Manual start:
echo      conda activate arbitrage-bot
echo      python main.py
echo.
echo 🌐 After starting, open: http://localhost:8000
echo 📚 API documentation: http://localhost:8000/docs
echo ═══════════════════════════════════════════════════════════════
echo.

set /p choice="❓ Start the bot now? (1=Full, 2=Simple, 3=Test, N=No): "

if "%choice%"=="1" (
    echo.
    echo 🚀 Starting full version...
    start_bot.bat
) else if "%choice%"=="2" (
    echo.
    echo 🚀 Starting simplified version...
    start_simple.bat
) else if "%choice%"=="3" (
    echo.
    echo 🧪 Testing APIs...
    test_apis.bat
) else (
    echo.
    echo 👋 Setup complete! Use the .bat files to start the bot.
)

pause
