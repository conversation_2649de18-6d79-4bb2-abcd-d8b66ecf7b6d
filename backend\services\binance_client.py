"""
Binance API client for CEX data
"""
from typing import Dict, Any, List, Optional
from backend.services.api_client import BaseAPIClient
from backend.config.settings import settings
import hashlib
import hmac
import time


class BinanceClient(BaseAPIClient):
    """Client for Binance API"""
    
    def __init__(self):
        super().__init__(
            base_url="https://api.binance.com/api/v3",
            api_key=settings.BINANCE_API_KEY,
            requests_per_minute=1200,  # Binance allows 1200 requests per minute
            timeout=10
        )
        self.secret_key = settings.BINANCE_SECRET_KEY
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get Binance authentication headers"""
        if self.api_key:
            return {"X-MBX-APIKEY": self.api_key}
        return {}
    
    def _generate_signature(self, params: Dict[str, Any]) -> str:
        """Generate signature for authenticated requests"""
        if not self.secret_key:
            return ""
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    async def get_exchange_info(self) -> Optional[Dict[str, Any]]:
        """Get exchange trading rules and symbol information"""
        return await self.get("/exchangeInfo", cache_ttl=3600)
    
    async def get_ticker_24hr(self, symbol: Optional[str] = None) -> Optional[List[Dict[str, Any]]]:
        """Get 24hr ticker price change statistics"""
        params = {}
        if symbol:
            params["symbol"] = symbol
        return await self.get("/ticker/24hr", params=params, cache_ttl=60)
    
    async def get_ticker_price(self, symbol: Optional[str] = None) -> Optional[List[Dict[str, Any]]]:
        """Get latest price for symbol(s)"""
        params = {}
        if symbol:
            params["symbol"] = symbol
        return await self.get("/ticker/price", params=params, cache_ttl=10)
    
    async def get_order_book(self, symbol: str, limit: int = 100) -> Optional[Dict[str, Any]]:
        """Get order book for a symbol"""
        params = {"symbol": symbol, "limit": limit}
        return await self.get("/depth", params=params, cache_ttl=5)
    
    async def get_recent_trades(self, symbol: str, limit: int = 500) -> Optional[List[Dict[str, Any]]]:
        """Get recent trades for a symbol"""
        params = {"symbol": symbol, "limit": limit}
        return await self.get("/trades", params=params, cache_ttl=10)
    
    async def get_klines(
        self, 
        symbol: str, 
        interval: str = "1m",
        start_time: Optional[int] = None,
        end_time: Optional[int] = None,
        limit: int = 500
    ) -> Optional[List[List]]:
        """Get kline/candlestick data"""
        params = {
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }
        if start_time:
            params["startTime"] = start_time
        if end_time:
            params["endTime"] = end_time
        
        return await self.get("/klines", params=params, cache_ttl=60)
    
    async def get_avg_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current average price for a symbol"""
        params = {"symbol": symbol}
        return await self.get("/avgPrice", params=params, cache_ttl=30)
    
    async def get_trading_symbols(self) -> Optional[List[str]]:
        """Get all trading symbols"""
        exchange_info = await self.get_exchange_info()
        if not exchange_info:
            return []
        
        symbols = []
        for symbol_info in exchange_info.get("symbols", []):
            if symbol_info.get("status") == "TRADING":
                symbols.append(symbol_info.get("symbol"))
        
        return symbols
    
    async def get_top_volume_symbols(self, limit: int = 100) -> Optional[List[Dict[str, Any]]]:
        """Get symbols with highest 24h volume"""
        ticker_data = await self.get_ticker_24hr()
        if not ticker_data:
            return []
        
        # Filter for USDT pairs and sort by volume
        usdt_pairs = [
            ticker for ticker in ticker_data
            if ticker.get("symbol", "").endswith("USDT") and
               float(ticker.get("quoteVolume", 0)) > 1000000  # Min $1M volume
        ]
        
        # Sort by quote volume (USD volume)
        usdt_pairs.sort(key=lambda x: float(x.get("quoteVolume", 0)), reverse=True)
        
        return usdt_pairs[:limit]
    
    async def get_price_for_arbitrage(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get current prices for arbitrage comparison"""
        price_data = {}
        
        # Get all prices at once
        all_prices = await self.get_ticker_price()
        if not all_prices:
            return price_data
        
        # Convert to dict for easy lookup
        price_dict = {item["symbol"]: item for item in all_prices}
        
        for symbol in symbols:
            if symbol in price_dict:
                price_info = price_dict[symbol]
                price_data[symbol] = {
                    "symbol": symbol,
                    "price": float(price_info["price"]),
                    "exchange": "binance",
                    "timestamp": int(time.time() * 1000)
                }
        
        return price_data
    
    async def get_market_depth_analysis(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Analyze market depth for liquidity assessment"""
        order_book = await self.get_order_book(symbol, limit=1000)
        if not order_book:
            return None
        
        bids = order_book.get("bids", [])
        asks = order_book.get("asks", [])
        
        if not bids or not asks:
            return None
        
        # Calculate depth metrics
        bid_depth = sum(float(price) * float(qty) for price, qty in bids[:10])  # Top 10 bids
        ask_depth = sum(float(price) * float(qty) for price, qty in asks[:10])  # Top 10 asks
        
        best_bid = float(bids[0][0]) if bids else 0
        best_ask = float(asks[0][0]) if asks else 0
        spread = best_ask - best_bid if best_bid and best_ask else 0
        spread_percentage = (spread / best_ask * 100) if best_ask else 0
        
        # Calculate slippage for different order sizes
        slippage_analysis = {}
        order_sizes = [1000, 5000, 10000, 50000]  # USD amounts
        
        for size in order_sizes:
            # Calculate buy slippage (market buy order)
            remaining_size = size
            total_cost = 0
            total_qty = 0
            
            for price, qty in asks:
                price_f = float(price)
                qty_f = float(qty)
                order_value = price_f * qty_f
                
                if remaining_size <= order_value:
                    needed_qty = remaining_size / price_f
                    total_cost += remaining_size
                    total_qty += needed_qty
                    break
                else:
                    total_cost += order_value
                    total_qty += qty_f
                    remaining_size -= order_value
            
            avg_buy_price = total_cost / total_qty if total_qty > 0 else 0
            buy_slippage = ((avg_buy_price - best_ask) / best_ask * 100) if best_ask else 0
            
            slippage_analysis[f"buy_{size}"] = {
                "average_price": avg_buy_price,
                "slippage_percentage": buy_slippage,
                "quantity": total_qty
            }
        
        return {
            "symbol": symbol,
            "best_bid": best_bid,
            "best_ask": best_ask,
            "spread": spread,
            "spread_percentage": spread_percentage,
            "bid_depth_usd": bid_depth,
            "ask_depth_usd": ask_depth,
            "total_depth_usd": bid_depth + ask_depth,
            "slippage_analysis": slippage_analysis,
            "timestamp": int(time.time() * 1000)
        }
    
    async def get_volume_profile(self, symbol: str, interval: str = "1h", limit: int = 24) -> Optional[Dict[str, Any]]:
        """Get volume profile for the last 24 hours"""
        klines = await self.get_klines(symbol, interval, limit=limit)
        if not klines:
            return None
        
        total_volume = 0
        volume_by_hour = []
        price_levels = {}
        
        for kline in klines:
            # Kline format: [open_time, open, high, low, close, volume, close_time, quote_volume, count, taker_buy_volume, taker_buy_quote_volume, ignore]
            open_price = float(kline[1])
            high_price = float(kline[2])
            low_price = float(kline[3])
            close_price = float(kline[4])
            volume = float(kline[5])
            quote_volume = float(kline[7])
            
            total_volume += quote_volume
            volume_by_hour.append({
                "timestamp": kline[0],
                "volume": quote_volume,
                "price_range": [low_price, high_price],
                "avg_price": (open_price + close_price) / 2
            })
            
            # Group volume by price levels (rounded to nearest cent)
            avg_price_rounded = round((open_price + close_price) / 2, 2)
            if avg_price_rounded not in price_levels:
                price_levels[avg_price_rounded] = 0
            price_levels[avg_price_rounded] += quote_volume
        
        # Find volume-weighted average price (VWAP)
        total_value = sum(hour["volume"] * hour["avg_price"] for hour in volume_by_hour)
        vwap = total_value / total_volume if total_volume > 0 else 0
        
        return {
            "symbol": symbol,
            "total_volume_24h": total_volume,
            "vwap_24h": vwap,
            "volume_by_hour": volume_by_hour,
            "price_levels": price_levels,
            "high_volume_price": max(price_levels.items(), key=lambda x: x[1])[0] if price_levels else 0
        }
