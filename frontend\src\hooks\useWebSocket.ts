import { useEffect, useRef, useState, useCallback } from 'react'
import { io, Socket } from 'socket.io-client'
import { WebSocketMessage } from '@types/index'
import toast from 'react-hot-toast'

interface UseWebSocketOptions {
  url?: string
  autoConnect?: boolean
  reconnectAttempts?: number
  reconnectDelay?: number
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Error) => void
  onMessage?: (message: WebSocketMessage) => void
}

interface UseWebSocketReturn {
  socket: Socket | null
  isConnected: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  connect: () => void
  disconnect: () => void
  subscribe: (topic: string) => void
  unsubscribe: (topic: string) => void
  sendMessage: (message: WebSocketMessage) => void
  lastMessage: WebSocketMessage | null
  subscriptions: string[]
}

export const useWebSocket = (options: UseWebSocketOptions = {}): UseWebSocketReturn => {
  const {
    url = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`,
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectDelay = 3000,
    onConnect,
    onDisconnect,
    onError,
    onMessage,
  } = options

  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null)
  const [subscriptions, setSubscriptions] = useState<string[]>([])

  const reconnectAttemptsRef = useRef(0)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>()

  const connect = useCallback(() => {
    if (socket?.connected) {
      return
    }

    setConnectionStatus('connecting')

    try {
      const newSocket = io(url, {
        transports: ['websocket'],
        upgrade: false,
        rememberUpgrade: false,
        timeout: 10000,
        forceNew: true,
      })

      newSocket.on('connect', () => {
        setIsConnected(true)
        setConnectionStatus('connected')
        reconnectAttemptsRef.current = 0
        
        toast.success('Connected to real-time updates', {
          id: 'websocket-connect',
          duration: 2000,
        })
        
        onConnect?.()
      })

      newSocket.on('disconnect', (reason) => {
        setIsConnected(false)
        setConnectionStatus('disconnected')
        
        console.log('WebSocket disconnected:', reason)
        
        if (reason === 'io server disconnect') {
          // Server initiated disconnect, don't reconnect automatically
          toast.error('Disconnected from server', {
            id: 'websocket-disconnect',
          })
        } else {
          // Client-side disconnect or network issue, attempt reconnect
          attemptReconnect()
        }
        
        onDisconnect?.()
      })

      newSocket.on('connect_error', (error) => {
        setConnectionStatus('error')
        console.error('WebSocket connection error:', error)
        
        toast.error('Connection failed', {
          id: 'websocket-error',
          duration: 3000,
        })
        
        onError?.(error)
        attemptReconnect()
      })

      // Handle incoming messages
      newSocket.onAny((eventName: string, data: any) => {
        const message: WebSocketMessage = {
          type: eventName,
          data,
          timestamp: new Date().toISOString(),
        }
        
        setLastMessage(message)
        onMessage?.(message)
      })

      setSocket(newSocket)
    } catch (error) {
      setConnectionStatus('error')
      console.error('Failed to create WebSocket connection:', error)
      onError?.(error as Error)
    }
  }, [url, onConnect, onDisconnect, onError, onMessage])

  const attemptReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current >= reconnectAttempts) {
      setConnectionStatus('error')
      toast.error('Failed to reconnect after multiple attempts', {
        id: 'websocket-reconnect-failed',
      })
      return
    }

    reconnectAttemptsRef.current += 1
    
    toast.loading(`Reconnecting... (${reconnectAttemptsRef.current}/${reconnectAttempts})`, {
      id: 'websocket-reconnecting',
    })

    reconnectTimeoutRef.current = setTimeout(() => {
      connect()
    }, reconnectDelay)
  }, [reconnectAttempts, reconnectDelay, connect])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
    
    if (socket) {
      socket.disconnect()
      setSocket(null)
    }
    
    setIsConnected(false)
    setConnectionStatus('disconnected')
    setSubscriptions([])
    
    toast.dismiss('websocket-reconnecting')
  }, [socket])

  const subscribe = useCallback((topic: string) => {
    if (!socket || !isConnected) {
      console.warn('Cannot subscribe: WebSocket not connected')
      return
    }

    socket.emit('subscribe', { topic })
    
    setSubscriptions(prev => {
      if (!prev.includes(topic)) {
        return [...prev, topic]
      }
      return prev
    })

    console.log(`Subscribed to topic: ${topic}`)
  }, [socket, isConnected])

  const unsubscribe = useCallback((topic: string) => {
    if (!socket || !isConnected) {
      console.warn('Cannot unsubscribe: WebSocket not connected')
      return
    }

    socket.emit('unsubscribe', { topic })
    
    setSubscriptions(prev => prev.filter(t => t !== topic))
    
    console.log(`Unsubscribed from topic: ${topic}`)
  }, [socket, isConnected])

  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (!socket || !isConnected) {
      console.warn('Cannot send message: WebSocket not connected')
      return
    }

    socket.emit(message.type, message.data || {})
  }, [socket, isConnected])

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [autoConnect, connect, disconnect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      disconnect()
    }
  }, [disconnect])

  return {
    socket,
    isConnected,
    connectionStatus,
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    sendMessage,
    lastMessage,
    subscriptions,
  }
}

// Hook for specific arbitrage updates
export const useArbitrageWebSocket = () => {
  const [opportunities, setOpportunities] = useState<any[]>([])
  const [scanStatus, setScanStatus] = useState<any>(null)
  const [stats, setStats] = useState<any>(null)

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'opportunities_update':
        setOpportunities(message.data?.opportunities || [])
        break
      
      case 'scan_status_update':
        setScanStatus(message.data)
        break
      
      case 'stats_update':
        setStats(message.data)
        break
      
      case 'opportunity_validated':
        setOpportunities(prev => 
          prev.map(opp => 
            opp.id === message.data?.opportunity_id 
              ? { ...opp, ...message.data?.updates }
              : opp
          )
        )
        break
      
      case 'scan_error':
        toast.error(`Scan error: ${message.data?.error}`, {
          duration: 5000,
        })
        break
      
      default:
        console.log('Unhandled WebSocket message:', message)
    }
  }, [])

  const webSocket = useWebSocket({
    onMessage: handleMessage,
    onConnect: () => {
      // Auto-subscribe to arbitrage updates
      webSocket.subscribe('arbitrage_updates')
      webSocket.subscribe('scan_status')
      webSocket.subscribe('stats_updates')
    },
  })

  useEffect(() => {
    if (webSocket.isConnected) {
      webSocket.subscribe('arbitrage_updates')
      webSocket.subscribe('scan_status')
      webSocket.subscribe('stats_updates')
    }
  }, [webSocket.isConnected])

  return {
    ...webSocket,
    opportunities,
    scanStatus,
    stats,
  }
}

export default useWebSocket
