"""
Enhanced Crypto Arbitrage Bot v3.0 - Integrated Web Application
Complete solution with Flask web interface and advanced arbitrage detection
All-in-one file for easy deployment
"""

# Core imports
import asyncio
import aiohttp
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
import json
import random
from collections import defaultdict, deque
import threading
import requests

# Flask and WebSocket imports for integrated web interface
try:
    from flask import Flask, render_template_string, jsonify, request
    from flask_socketio import SocketIO, emit
    FLASK_AVAILABLE = True
except ImportError:
    print("⚠️ Flask/Flask-SocketIO not installed. Web interface will be disabled.")
    print("💡 Install with: pip install Flask flask-socketio eventlet")
    FLASK_AVAILABLE = False
from dataclasses import dataclass
import hashlib
import random

# Simple logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app and SocketIO if available
if FLASK_AVAILABLE:
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'crypto_arbitrage_bot_v3_secret'
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='eventlet')

    # Enhanced State Management untuk Web Interface
    web_app_state = {
        'status': 'Idle',
        'is_running': False,
        'opportunities': [],
        'scan_count': 0,
        'last_scan_time': None,
        'logs': deque(maxlen=500),
        'simulation_capital': 100.0,
        'max_profit_threshold': 25.0,
        'progress': {
            'percentage': 0,
            'current_action': 'Menunggu...',
            'tokens_scanned': 0,
            'total_tokens': 0,
            'tokens_per_second': 0,
            'api_calls_made': 0,
            'opportunities_found': 0,
            'estimated_time_remaining': 0
        },
        'bot_parameters': {
            'profit_thresholds': {
                'stablecoins': {'min': 0.1, 'max': 0.7},
                'blue_chips': {'min': 0.3, 'max': 1.4},
                'defi': {'min': 0.7, 'max': 2.1},
                'solana_ecosystem': {'min': 0.6, 'max': 2.1},
                'meme_coins': {'min': 1.0, 'max': 5.6}
            },
            'scan_intervals': {
                'priority_tier': 30,
                'regular_tier': 120,
                'discovery_tier': 300
            },
            'blockchain_selection': {
                'ethereum': True,
                'solana': True,
                'bsc': True,
                'polygon': True,
                'arbitrum': True
            },
            'min_liquidity': 1000,
            'max_tokens_per_scan': 1000,
            'enable_pair_validation': True,
            'enable_demo_mode': True
        },
        'bot_process': None,
        'statistics': {
            'total_scans': 0,
            'total_opportunities': 0,
            'avg_profit_percentage': 0,
            'best_opportunity': None,
            'uptime_seconds': 0,
            'start_time': None
        }
    }
else:
    app = None
    socketio = None
    web_app_state = {}

@dataclass
class ValidationResult:
    """Data class for validation results"""
    is_valid: bool
    feasibility_score: int  # 0-100
    warnings: List[str]
    security_flags: List[str]
    execution_time_estimate: float  # seconds
    liquidity_depth_score: int  # 0-100

@dataclass
class TradingPair:
    """Data class for normalized trading pairs"""
    base_token: str
    quote_token: str
    base_address: str
    quote_address: str
    dex_id: str
    chain: str
    price_usd: float
    liquidity_usd: float
    pair_address: str
    volume_24h: float

    def get_pair_key(self) -> str:
        """Generate normalized pair key for comparison"""
        # Sort tokens alphabetically to ensure consistent pairing
        tokens = sorted([self.base_token.upper(), self.quote_token.upper()])
        return f"{tokens[0]}/{tokens[1]}"

    def is_same_pair(self, other: 'TradingPair') -> bool:
        """Check if two trading pairs are exactly the same"""
        return self.get_pair_key() == other.get_pair_key()

class PairValidator:
    """Advanced trading pair validation system to prevent false arbitrage signals"""

    def __init__(self):
        # Token normalization mappings
        self.token_aliases = {
            # Wrapped tokens
            'WETH': 'ETH',
            'WBTC': 'BTC',
            'WBNB': 'BNB',
            'WMATIC': 'MATIC',
            'WSOL': 'SOL',
            'WAVAX': 'AVAX',

            # Stablecoin variations
            'USDC.E': 'USDC',
            'USDT.E': 'USDT',
            'DAI.E': 'DAI',

            # Liquid staking tokens (DO NOT normalize - these are different assets)
            'STSOL': 'STSOL',  # Keep separate from SOL
            'MSOL': 'MSOL',    # Keep separate from SOL
            'JITOSOL': 'JITOSOL',  # Keep separate from SOL
            'LSTSOL': 'LSTSOL',    # Keep separate from SOL

            # Bridge tokens
            'HBTC': 'BTC',
            'RENBTC': 'BTC',
            'TBTC': 'BTC',
        }

        # Tokens that should NEVER be normalized (different assets with similar names)
        self.protected_tokens = {
            'STSOL', 'MSOL', 'JITOSOL', 'LSTSOL', 'BSOL', 'SCNSOL', 'DAOSOL',
            'RETH', 'STETH', 'CBETH', 'WSTETH',  # ETH liquid staking
            'FRXETH', 'SFRXETH',  # Frax ETH
            'ROCKET', 'RPL',  # Rocket Pool
        }

    def normalize_token_symbol(self, symbol: str) -> str:
        """Normalize token symbol while preserving liquid staking tokens"""
        if not symbol:
            return symbol

        symbol_upper = symbol.upper()

        # Never normalize protected tokens
        if symbol_upper in self.protected_tokens:
            return symbol_upper

        # Apply normalization mapping
        return self.token_aliases.get(symbol_upper, symbol_upper)

    def extract_trading_pair(self, pair_data: Dict) -> Optional[TradingPair]:
        """Extract and normalize trading pair from DexScreener data"""
        try:
            base_token = pair_data.get('baseToken', {})
            quote_token = pair_data.get('quoteToken', {})

            if not base_token or not quote_token:
                return None

            base_symbol = self.normalize_token_symbol(base_token.get('symbol', ''))
            quote_symbol = self.normalize_token_symbol(quote_token.get('symbol', ''))

            if not base_symbol or not quote_symbol:
                return None

            return TradingPair(
                base_token=base_symbol,
                quote_token=quote_symbol,
                base_address=base_token.get('address', ''),
                quote_address=quote_token.get('address', ''),
                dex_id=pair_data.get('dexId', ''),
                chain=pair_data.get('chainId', ''),
                price_usd=float(pair_data.get('priceUsd', 0)),
                liquidity_usd=pair_data.get('liquidity', {}).get('usd', 0),
                pair_address=pair_data.get('pairAddress', ''),
                volume_24h=pair_data.get('volume', {}).get('h24', 0)
            )

        except Exception as e:
            logger.warning(f"Failed to extract trading pair: {e}")
            return None

    def validate_arbitrage_pairs(self, pairs: List[Dict], token_symbol: str) -> Tuple[List[TradingPair], List[str]]:
        """Validate and filter pairs for true arbitrage opportunities"""
        valid_pairs = []
        validation_logs = []

        # Extract and normalize all pairs
        normalized_pairs = []
        for pair_data in pairs:
            trading_pair = self.extract_trading_pair(pair_data)
            if trading_pair:
                normalized_pairs.append(trading_pair)

        if len(normalized_pairs) < 2:
            validation_logs.append(f"❌ {token_symbol}: Insufficient pairs ({len(normalized_pairs)}) for arbitrage")
            return [], validation_logs

        # Group pairs by normalized pair key
        pair_groups = {}
        for pair in normalized_pairs:
            pair_key = pair.get_pair_key()
            if pair_key not in pair_groups:
                pair_groups[pair_key] = []
            pair_groups[pair_key].append(pair)

        # Find groups with multiple DEXs (true arbitrage opportunities)
        for pair_key, group_pairs in pair_groups.items():
            if len(group_pairs) >= 2:
                # Ensure different DEXs
                unique_dexs = set(pair.dex_id for pair in group_pairs)
                if len(unique_dexs) >= 2:
                    valid_pairs.extend(group_pairs)
                    validation_logs.append(f"✅ {token_symbol}: Valid arbitrage pair {pair_key} across {len(unique_dexs)} DEXs")
                else:
                    validation_logs.append(f"⚠️ {token_symbol}: Same DEX for pair {pair_key}, skipping")
            else:
                validation_logs.append(f"⚠️ {token_symbol}: Single DEX for pair {pair_key}, skipping")

        # Log rejected pairs for transparency
        rejected_pairs = len(normalized_pairs) - len(valid_pairs)
        if rejected_pairs > 0:
            validation_logs.append(f"🔍 {token_symbol}: Rejected {rejected_pairs} pairs due to validation")

        return valid_pairs, validation_logs

class RealTimeValidator:
    """Advanced real-time opportunity validation system"""

    def __init__(self):
        self.cache = {}
        self.cache_ttl = {
            'liquidity': 30,  # 30 seconds
            'security': 3600,  # 1 hour
            'token_info': 86400  # 24 hours
        }

    def _get_cache_key(self, data: str) -> str:
        """Generate cache key from data"""
        return hashlib.md5(data.encode()).hexdigest()

    def _is_cache_valid(self, key: str, cache_type: str) -> bool:
        """Check if cache entry is still valid"""
        if key not in self.cache:
            return False

        entry = self.cache[key]
        ttl = self.cache_ttl.get(cache_type, 300)
        return (datetime.now() - entry['timestamp']).seconds < ttl

    def _set_cache(self, key: str, data: Any, cache_type: str):
        """Set cache entry with timestamp"""
        self.cache[key] = {
            'data': data,
            'timestamp': datetime.now(),
            'type': cache_type
        }

    async def validate_liquidity_depth(self, pair_address: str, chain: str, order_size_usd: float) -> Tuple[bool, int, List[str]]:
        """
        CRITICAL: Validate liquidity depth for order execution
        Returns: (is_sufficient, depth_score, warnings)
        """
        warnings = []

        try:
            # Cache key for liquidity data
            cache_key = self._get_cache_key(f"liquidity_{pair_address}_{chain}")

            if self._is_cache_valid(cache_key, 'liquidity'):
                liquidity_data = self.cache[cache_key]['data']
            else:
                # Fetch fresh liquidity data from DexScreener
                async with aiohttp.ClientSession() as session:
                    url = f"https://api.dexscreener.com/latest/dex/pairs/{chain}/{pair_address}"
                    async with session.get(url) as response:
                        if response.status == 200:
                            data = await response.json()
                            liquidity_data = data.get('pair', {}).get('liquidity', {})
                            self._set_cache(cache_key, liquidity_data, 'liquidity')
                        else:
                            return False, 0, ["Failed to fetch liquidity data"]

            liquidity_usd = liquidity_data.get('usd', 0)

            if liquidity_usd <= 0:
                return False, 0, ["No liquidity data available"]

            # Calculate liquidity depth score
            liquidity_ratio = liquidity_usd / order_size_usd if order_size_usd > 0 else 0

            if liquidity_ratio < 5:
                warnings.append(f"Low liquidity ratio: {liquidity_ratio:.1f}x")
                depth_score = min(20, int(liquidity_ratio * 4))
            elif liquidity_ratio < 10:
                warnings.append(f"Moderate liquidity ratio: {liquidity_ratio:.1f}x")
                depth_score = min(60, int(20 + (liquidity_ratio - 5) * 8))
            elif liquidity_ratio < 50:
                depth_score = min(90, int(60 + (liquidity_ratio - 10) * 0.75))
            else:
                depth_score = 100

            is_sufficient = liquidity_ratio >= 5  # Minimum 5x liquidity

            return is_sufficient, depth_score, warnings

        except Exception as e:
            logger.error(f"Liquidity validation error: {e}")
            return False, 0, [f"Validation error: {str(e)}"]

    async def validate_execution_feasibility(self, opportunity: Dict) -> Tuple[bool, float, List[str]]:
        """
        Validate if arbitrage can be executed in realistic timeframe
        Returns: (is_feasible, execution_time_estimate, warnings)
        """
        warnings = []

        try:
            # Base execution time estimates (seconds)
            base_times = {
                'ethereum': 15,  # ~1 block
                'bsc': 3,       # ~1 block
                'polygon': 2,   # ~1 block
                'arbitrum': 1,  # ~1 block
                'solana': 0.5,  # ~1 slot
                'avalanche': 2, # ~1 block
                'fantom': 1,    # ~1 block
                'optimism': 1   # ~1 block
            }

            chain = opportunity.get('buy_chain', 'ethereum').lower()
            base_time = base_times.get(chain, 15)

            # Adjust for network congestion (simplified)
            congestion_multiplier = 1.5  # Assume moderate congestion

            # Adjust for transaction complexity (2 transactions for arbitrage)
            complexity_multiplier = 2.0

            # Calculate total execution time
            execution_time = base_time * congestion_multiplier * complexity_multiplier

            # Check if execution time is reasonable
            max_reasonable_time = 300  # 5 minutes

            if execution_time > max_reasonable_time:
                warnings.append(f"Long execution time: {execution_time:.1f}s")
                return False, execution_time, warnings

            # Check profit vs gas costs (simplified)
            profit_usd = opportunity.get('profit_usd', 0)
            estimated_gas_cost = {
                'ethereum': 50,   # High gas
                'bsc': 1,        # Low gas
                'polygon': 0.1,  # Very low gas
                'arbitrum': 5,   # Medium gas
                'solana': 0.01,  # Very low gas
                'avalanche': 2,  # Low gas
                'fantom': 0.1,   # Very low gas
                'optimism': 5    # Medium gas
            }.get(chain, 10)

            net_profit = profit_usd - estimated_gas_cost

            if net_profit <= 0:
                warnings.append(f"Negative net profit after gas: ${net_profit:.2f}")
                return False, execution_time, warnings

            if net_profit < 1:
                warnings.append(f"Low net profit after gas: ${net_profit:.2f}")

            return True, execution_time, warnings

        except Exception as e:
            logger.error(f"Execution feasibility error: {e}")
            return False, 300, [f"Feasibility check error: {str(e)}"]

    async def filter_false_positives(self, opportunity: Dict) -> Tuple[bool, int, List[str]]:
        """
        CRITICAL: Detect and eliminate false positive opportunities
        Returns: (is_legitimate, confidence_score, warnings)
        """
        warnings = []
        confidence_score = 100

        try:
            # Get token and pair information
            token_symbol = opportunity.get('token_symbol', '')
            buy_pair_address = opportunity.get('buy_pair_address', '')
            sell_pair_address = opportunity.get('sell_pair_address', '')
            chain = opportunity.get('buy_chain', '')

            # 1. Volume trading check (minimum 1 hour)
            volume_score = await self._check_recent_volume(buy_pair_address, sell_pair_address, chain)
            if volume_score < 30:
                warnings.append(f"Low recent trading volume (score: {volume_score})")
                confidence_score -= 25

            # 2. Bid-ask spread check
            spread_score = await self._check_bid_ask_spread(opportunity)
            if spread_score < 50:
                warnings.append(f"Wide bid-ask spread detected (score: {spread_score})")
                confidence_score -= 20

            # 3. Market cap check
            mcap_score = await self._check_market_cap(token_symbol, chain)
            if mcap_score < 40:
                warnings.append(f"Low market cap - manipulation risk (score: {mcap_score})")
                confidence_score -= 30

            # 4. Token age check
            age_score = await self._check_token_age(token_symbol, chain)
            if age_score < 50:
                warnings.append(f"New token - high risk (score: {age_score})")
                confidence_score -= 25

            # Ensure confidence score doesn't go below 0
            confidence_score = max(0, confidence_score)

            # Determine if opportunity is legitimate
            is_legitimate = confidence_score >= 60  # 60% confidence threshold

            if not is_legitimate:
                warnings.append(f"Low confidence score: {confidence_score}/100")

            return is_legitimate, confidence_score, warnings

        except Exception as e:
            logger.error(f"False positive filter error: {e}")
            return False, 0, [f"Filter error: {str(e)}"]

    async def _check_recent_volume(self, buy_pair: str, sell_pair: str, chain: str) -> int:
        """Check recent trading volume (1 hour minimum $1000+)"""
        try:
            total_volume = 0

            for pair_address in [buy_pair, sell_pair]:
                if not pair_address:
                    continue

                cache_key = self._get_cache_key(f"volume_{pair_address}")

                if self._is_cache_valid(cache_key, 'liquidity'):
                    volume_data = self.cache[cache_key]['data']
                else:
                    async with aiohttp.ClientSession() as session:
                        url = f"https://api.dexscreener.com/latest/dex/pairs/{chain}/{pair_address}"
                        async with session.get(url) as response:
                            if response.status == 200:
                                data = await response.json()
                                volume_data = data.get('pair', {}).get('volume', {})
                                self._set_cache(cache_key, volume_data, 'liquidity')
                            else:
                                continue

                # Get 1 hour volume (approximate from 24h data)
                volume_24h = volume_data.get('h24', 0)
                volume_1h = volume_24h / 24  # Rough approximation
                total_volume += volume_1h

            # Score based on volume threshold
            if total_volume >= 1000:
                return 100
            elif total_volume >= 500:
                return 75
            elif total_volume >= 100:
                return 50
            elif total_volume >= 10:
                return 25
            else:
                return 0

        except Exception as e:
            logger.error(f"Volume check error: {e}")
            return 0

    async def _check_bid_ask_spread(self, opportunity: Dict) -> int:
        """Check bid-ask spread (max 2% stablecoins, 5% altcoins)"""
        try:
            token_symbol = opportunity.get('token_symbol', '').upper()
            profit_percentage = opportunity.get('profit_percentage', 0)

            # Determine if it's a stablecoin
            stablecoins = ['USDC', 'USDT', 'DAI', 'BUSD', 'FRAX', 'TUSD', 'LUSD']
            is_stablecoin = token_symbol in stablecoins

            max_spread = 2.0 if is_stablecoin else 5.0

            # Use profit percentage as proxy for spread
            if profit_percentage <= max_spread:
                return 100
            elif profit_percentage <= max_spread * 2:
                return 70
            elif profit_percentage <= max_spread * 3:
                return 40
            else:
                return 10

        except Exception as e:
            logger.error(f"Spread check error: {e}")
            return 50

    async def _check_market_cap(self, token_symbol: str, chain: str) -> int:
        """Check market cap (minimum $100K)"""
        try:
            # This would require additional API integration
            # For now, return moderate score for known tokens
            known_tokens = [
                'USDC', 'USDT', 'WETH', 'WBTC', 'DAI', 'LINK', 'UNI', 'AAVE',
                'SOL', 'RAY', 'ORCA', 'BONK', 'WIF', 'POPCAT'
            ]

            if token_symbol.upper() in known_tokens:
                return 100
            else:
                return 60  # Moderate score for unknown tokens

        except Exception as e:
            logger.error(f"Market cap check error: {e}")
            return 50

    async def _check_token_age(self, token_symbol: str, chain: str) -> int:
        """Check token age (minimum 24 hours)"""
        try:
            # This would require contract creation timestamp
            # For now, return moderate score for known tokens
            known_tokens = [
                'USDC', 'USDT', 'WETH', 'WBTC', 'DAI', 'LINK', 'UNI', 'AAVE',
                'SOL', 'RAY', 'ORCA', 'BONK', 'WIF', 'POPCAT'
            ]

            if token_symbol.upper() in known_tokens:
                return 100
            else:
                return 70  # Moderate score for unknown tokens

        except Exception as e:
            logger.error(f"Token age check error: {e}")
            return 50

@dataclass
class SecurityResult:
    """Data class for security analysis results"""
    security_score: int  # 0-100
    goplus_score: int
    quickintel_score: int
    security_flags: List[str]
    honeypot_risk: bool
    audit_status: str
    liquidity_locked: bool
    owner_privileges: List[str]
    recommendations: List[str]

class SecurityAnalyzer:
    """Multi-API security integration for comprehensive token analysis"""

    def __init__(self):
        self.cache = {}
        self.cache_ttl = 3600  # 1 hour cache
        self.goplus_base_url = "https://api.gopluslabs.io/api/v1"
        self.quickintel_base_url = "https://api.quickintel.io/v1"  # Placeholder

    def _get_cache_key(self, token_address: str, chain: str) -> str:
        """Generate cache key for security data"""
        return hashlib.md5(f"security_{token_address}_{chain}".encode()).hexdigest()

    def _is_cache_valid(self, key: str) -> bool:
        """Check if security cache entry is still valid"""
        if key not in self.cache:
            return False

        entry = self.cache[key]
        return (datetime.now() - entry['timestamp']).seconds < self.cache_ttl

    def _set_cache(self, key: str, data: Any):
        """Set security cache entry with timestamp"""
        self.cache[key] = {
            'data': data,
            'timestamp': datetime.now()
        }

    async def check_goplus_security(self, token_address: str, chain: str) -> Dict:
        """
        CRITICAL: Check token security using GoPlus API
        Returns comprehensive security analysis
        """
        try:
            # Map chain names to GoPlus chain IDs
            chain_mapping = {
                'ethereum': '1',
                'bsc': '56',
                'polygon': '137',
                'arbitrum': '42161',
                'optimism': '10',
                'avalanche': '43114',
                'fantom': '250',
                'solana': 'solana'  # Special case for Solana
            }

            chain_id = chain_mapping.get(chain.lower(), '1')

            if not token_address or len(token_address) < 10:
                return self._get_default_security_result("Invalid token address")

            # Check cache first
            cache_key = self._get_cache_key(token_address, chain)
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']

            # Make API request to GoPlus
            async with aiohttp.ClientSession() as session:
                url = f"{self.goplus_base_url}/token_security/{chain_id}"
                params = {"contract_addresses": token_address.lower()}

                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        result = self._parse_goplus_response(data, token_address)
                        self._set_cache(cache_key, result)
                        return result
                    else:
                        logger.warning(f"GoPlus API error: {response.status}")
                        return self._get_default_security_result("API error")

        except Exception as e:
            logger.error(f"GoPlus security check error: {e}")
            return self._get_default_security_result(f"Error: {str(e)}")

    def _parse_goplus_response(self, data: Dict, token_address: str) -> Dict:
        """Parse GoPlus API response into standardized format"""
        try:
            result = data.get('result', {})
            token_data = result.get(token_address.lower(), {})

            if not token_data:
                return self._get_default_security_result("No data available")

            # Parse security indicators
            is_honeypot = token_data.get('is_honeypot', '0') == '1'
            is_open_source = token_data.get('is_open_source', '0') == '1'
            is_proxy = token_data.get('is_proxy', '0') == '1'
            is_mintable = token_data.get('is_mintable', '0') == '1'
            can_take_back_ownership = token_data.get('can_take_back_ownership', '0') == '1'
            owner_change_balance = token_data.get('owner_change_balance', '0') == '1'
            hidden_owner = token_data.get('hidden_owner', '0') == '1'
            selfdestruct = token_data.get('selfdestruct', '0') == '1'

            # Calculate security score
            security_score = 100
            security_flags = []

            if is_honeypot:
                security_score -= 50
                security_flags.append("honeypot")

            if not is_open_source:
                security_score -= 20
                security_flags.append("closed_source")

            if is_proxy:
                security_score -= 15
                security_flags.append("proxy_contract")

            if is_mintable:
                security_score -= 10
                security_flags.append("mintable")

            if can_take_back_ownership:
                security_score -= 25
                security_flags.append("ownership_risk")

            if owner_change_balance:
                security_score -= 30
                security_flags.append("balance_modification")

            if hidden_owner:
                security_score -= 15
                security_flags.append("hidden_owner")

            if selfdestruct:
                security_score -= 40
                security_flags.append("selfdestruct_risk")

            security_score = max(0, security_score)

            return {
                'security_score': security_score,
                'is_honeypot': is_honeypot,
                'is_open_source': is_open_source,
                'security_flags': security_flags,
                'owner_privileges': self._get_owner_privileges(token_data),
                'audit_status': 'verified' if is_open_source else 'unverified',
                'liquidity_locked': False,  # Would need additional API call
                'raw_data': token_data
            }

        except Exception as e:
            logger.error(f"GoPlus response parsing error: {e}")
            return self._get_default_security_result(f"Parse error: {str(e)}")

    def _get_owner_privileges(self, token_data: Dict) -> List[str]:
        """Extract owner privileges from token data"""
        privileges = []

        if token_data.get('is_mintable', '0') == '1':
            privileges.append("mint_tokens")

        if token_data.get('can_take_back_ownership', '0') == '1':
            privileges.append("reclaim_ownership")

        if token_data.get('owner_change_balance', '0') == '1':
            privileges.append("modify_balances")

        if token_data.get('selfdestruct', '0') == '1':
            privileges.append("destroy_contract")

        return privileges

    def _get_default_security_result(self, reason: str) -> Dict:
        """Return default security result when API fails"""
        return {
            'security_score': 50,  # Neutral score
            'is_honeypot': False,
            'is_open_source': False,
            'security_flags': ['unknown'],
            'owner_privileges': [],
            'audit_status': 'unknown',
            'liquidity_locked': False,
            'error': reason
        }

    async def check_quickintel_rating(self, token_address: str, chain: str) -> Dict:
        """
        Check token rating using QuickIntel API (placeholder implementation)
        """
        try:
            # This is a placeholder - QuickIntel API integration would go here
            # For now, return a moderate score for known tokens
            known_safe_tokens = [
                'USDC', 'USDT', 'WETH', 'WBTC', 'DAI', 'LINK', 'UNI', 'AAVE',
                'SOL', 'RAY', 'ORCA'
            ]

            # Simple heuristic based on token address patterns
            if len(token_address) == 42 and token_address.startswith('0x'):  # Ethereum-like
                return {
                    'quickintel_score': 75,
                    'rating': 'B+',
                    'risk_level': 'Medium',
                    'verified': False
                }
            elif len(token_address) > 30:  # Solana-like
                return {
                    'quickintel_score': 70,
                    'rating': 'B',
                    'risk_level': 'Medium',
                    'verified': False
                }
            else:
                return {
                    'quickintel_score': 50,
                    'rating': 'C',
                    'risk_level': 'High',
                    'verified': False
                }

        except Exception as e:
            logger.error(f"QuickIntel check error: {e}")
            return {
                'quickintel_score': 50,
                'rating': 'Unknown',
                'risk_level': 'Unknown',
                'verified': False,
                'error': str(e)
            }

    async def generate_security_score(self, goplus_data: Dict, quickintel_data: Dict) -> int:
        """
        Generate combined security score from multiple APIs
        Weighted average: GoPlus 70%, QuickIntel 30%
        """
        try:
            goplus_score = goplus_data.get('security_score', 50)
            quickintel_score = quickintel_data.get('quickintel_score', 50)

            # Weighted average
            combined_score = int((goplus_score * 0.7) + (quickintel_score * 0.3))

            # Apply penalties for critical issues
            if goplus_data.get('is_honeypot', False):
                combined_score = min(combined_score, 20)  # Max 20 for honeypots

            return max(0, min(100, combined_score))

        except Exception as e:
            logger.error(f"Security score generation error: {e}")
            return 50

    async def get_security_flags(self, goplus_data: Dict, quickintel_data: Dict) -> List[str]:
        """Return array of security warning flags"""
        flags = []

        # GoPlus flags
        goplus_flags = goplus_data.get('security_flags', [])
        flags.extend(goplus_flags)

        # QuickIntel flags
        qi_risk = quickintel_data.get('risk_level', '').lower()
        if qi_risk == 'high':
            flags.append('high_risk_rating')

        # Combined analysis flags
        combined_score = await self.generate_security_score(goplus_data, quickintel_data)
        if combined_score < 30:
            flags.append('very_high_risk')
        elif combined_score < 50:
            flags.append('high_risk')
        elif combined_score < 70:
            flags.append('medium_risk')

        return list(set(flags))  # Remove duplicates

class MultiAPIDataProvider:
    """v3.0 ENHANCED: Multi-source API data provider with validation and fallback"""

    def __init__(self, cache: 'SmartCache'):
        self.cache = cache
        self.api_clients = {}
        self.data_validators = DataValidator()
        self.api_health = {}
        self.last_health_check = None

        # Initialize API endpoints
        self.endpoints = {
            'dexscreener': {
                'base_url': 'https://api.dexscreener.com/latest',
                'rate_limit': 60,
                'priority': 1,
                'chains': ['ethereum', 'bsc', 'polygon', 'arbitrum', 'solana']
            },
            'coingecko': {
                'base_url': 'https://api.coingecko.com/api/v3',
                'rate_limit': 10,
                'priority': 2,
                'chains': ['ethereum', 'binance-smart-chain', 'polygon-pos', 'arbitrum-one', 'solana']
            },
            '1inch': {
                'base_url': 'https://api.1inch.dev/swap/v5.2',
                'rate_limit': 30,
                'priority': 3,
                'chains': ['1', '56', '137', '42161']  # Chain IDs
            }
        }

        # Initialize API clients
        for name, config in self.endpoints.items():
            self.api_clients[name] = SimpleAPIClient(config['base_url'], config['rate_limit'])
            self.api_health[name] = {'status': 'unknown', 'last_success': None, 'error_count': 0}

    async def get_token_prices(self, token_symbol: str, chains: List[str]) -> Dict[str, List[Dict]]:
        """Get token prices from multiple sources with validation"""
        try:
            all_results = {}

            # Try each API source
            for api_name, client in self.api_clients.items():
                try:
                    if api_name == 'dexscreener':
                        results = await self._get_dexscreener_data(token_symbol, chains)
                    elif api_name == 'coingecko':
                        results = await self._get_coingecko_data(token_symbol, chains)
                    elif api_name == '1inch':
                        results = await self._get_1inch_data(token_symbol, chains)
                    else:
                        continue

                    # Validate and merge results
                    validated_results = self.data_validators.validate_price_data(results, token_symbol)
                    if validated_results:
                        all_results[api_name] = validated_results
                        self._update_api_health(api_name, True)

                except Exception as e:
                    logger.error(f"API {api_name} error for {token_symbol}: {e}")
                    self._update_api_health(api_name, False)
                    continue

            # Consolidate results from multiple sources
            consolidated = self._consolidate_multi_source_data(all_results, token_symbol)
            return consolidated

        except Exception as e:
            logger.error(f"Multi-API data provider error: {e}")
            return {}

    async def _get_dexscreener_data(self, token_symbol: str, chains: List[str]) -> Dict[str, List[Dict]]:
        """Enhanced DexScreener API calls with proper chain filtering"""
        results = {}

        for chain in chains:
            try:
                # Use proper chain-specific search
                search_data = await self.api_clients['dexscreener'].get(
                    "dex/search",
                    {"q": token_symbol, "chain": chain}
                )

                if search_data and 'pairs' in search_data:
                    # Filter pairs to ensure they match the requested chain
                    chain_pairs = [
                        pair for pair in search_data['pairs']
                        if pair.get('chainId', '').lower() == chain.lower()
                    ]

                    if chain_pairs:
                        results[chain] = chain_pairs

                await asyncio.sleep(0.1)  # Rate limiting

            except Exception as e:
                logger.error(f"DexScreener error for {token_symbol} on {chain}: {e}")
                continue

        return results

    async def _get_coingecko_data(self, token_symbol: str, chains: List[str]) -> Dict[str, List[Dict]]:
        """Get price data from CoinGecko API"""
        results = {}

        try:
            # Search for token
            search_data = await self.api_clients['coingecko'].get(
                "search",
                {"query": token_symbol}
            )

            if search_data and 'coins' in search_data:
                for coin in search_data['coins'][:3]:  # Top 3 matches
                    coin_id = coin.get('id')
                    if coin_id:
                        # Get detailed price data
                        price_data = await self.api_clients['coingecko'].get(
                            f"coins/{coin_id}",
                            {"localization": "false", "tickers": "true", "market_data": "true"}
                        )

                        if price_data:
                            # Convert to our format
                            converted_data = self._convert_coingecko_format(price_data, chains)
                            for chain, pairs in converted_data.items():
                                if chain not in results:
                                    results[chain] = []
                                results[chain].extend(pairs)

            return results

        except Exception as e:
            logger.error(f"CoinGecko error for {token_symbol}: {e}")
            return {}

    async def _get_1inch_data(self, token_symbol: str, chains: List[str]) -> Dict[str, List[Dict]]:
        """Get price data from 1inch API"""
        results = {}

        # 1inch chain mapping
        chain_mapping = {
            'ethereum': '1',
            'bsc': '56',
            'polygon': '137',
            'arbitrum': '42161'
        }

        for chain in chains:
            if chain not in chain_mapping:
                continue

            try:
                chain_id = chain_mapping[chain]

                # Get token list first
                tokens_data = await self.api_clients['1inch'].get(f"{chain_id}/tokens")

                if tokens_data and 'tokens' in tokens_data:
                    # Find matching token
                    matching_tokens = [
                        token for token_addr, token in tokens_data['tokens'].items()
                        if token.get('symbol', '').upper() == token_symbol.upper()
                    ]

                    if matching_tokens:
                        # Convert to our format
                        converted_data = self._convert_1inch_format(matching_tokens, chain)
                        if converted_data:
                            results[chain] = converted_data

                await asyncio.sleep(0.1)  # Rate limiting

            except Exception as e:
                logger.error(f"1inch error for {token_symbol} on {chain}: {e}")
                continue

        return results

    def _convert_coingecko_format(self, coin_data: Dict, chains: List[str]) -> Dict[str, List[Dict]]:
        """Convert CoinGecko data to our standard format"""
        results = {}

        try:
            market_data = coin_data.get('market_data', {})
            current_price = market_data.get('current_price', {})

            if 'usd' in current_price:
                price_usd = current_price['usd']

                # Create standardized pair data
                for chain in chains:
                    pair_data = {
                        'baseToken': {
                            'symbol': coin_data.get('symbol', '').upper(),
                            'address': coin_data.get('contract_address', '')
                        },
                        'quoteToken': {'symbol': 'USD', 'address': ''},
                        'chainId': chain,
                        'dexId': 'coingecko_aggregate',
                        'priceUsd': str(price_usd),
                        'liquidity': {'usd': market_data.get('total_volume', {}).get('usd', 0)},
                        'volume': {'h24': market_data.get('total_volume', {}).get('usd', 0)},
                        'pairAddress': f"coingecko_{coin_data.get('id', '')}",
                        'source': 'coingecko'
                    }

                    if chain not in results:
                        results[chain] = []
                    results[chain].append(pair_data)

            return results

        except Exception as e:
            logger.error(f"CoinGecko format conversion error: {e}")
            return {}

    def _convert_1inch_format(self, tokens: List[Dict], chain: str) -> List[Dict]:
        """Convert 1inch data to our standard format"""
        results = []

        try:
            for token in tokens:
                # 1inch doesn't provide direct price, but we can use it for validation
                pair_data = {
                    'baseToken': {
                        'symbol': token.get('symbol', ''),
                        'address': token.get('address', '')
                    },
                    'quoteToken': {'symbol': 'USD', 'address': ''},
                    'chainId': chain,
                    'dexId': '1inch_aggregate',
                    'priceUsd': '0',  # Would need additional API call for price
                    'liquidity': {'usd': 0},
                    'volume': {'h24': 0},
                    'pairAddress': f"1inch_{token.get('address', '')}",
                    'source': '1inch'
                }
                results.append(pair_data)

            return results

        except Exception as e:
            logger.error(f"1inch format conversion error: {e}")
            return []

    def _consolidate_multi_source_data(self, all_results: Dict, token_symbol: str) -> Dict[str, List[Dict]]:
        """Consolidate data from multiple API sources"""
        consolidated = {}

        try:
            # Prioritize sources by reliability
            source_priority = ['dexscreener', 'coingecko', '1inch']

            for chain in ['ethereum', 'bsc', 'polygon', 'arbitrum', 'solana']:
                chain_data = []

                for source in source_priority:
                    if source in all_results and chain in all_results[source]:
                        source_data = all_results[source][chain]

                        # Add source priority and validation score
                        for pair in source_data:
                            pair['source_priority'] = source_priority.index(source)
                            pair['validation_score'] = self.data_validators.calculate_validation_score(pair, token_symbol)

                        chain_data.extend(source_data)

                if chain_data:
                    # Sort by validation score and source priority
                    chain_data.sort(key=lambda x: (x.get('validation_score', 0), -x.get('source_priority', 999)), reverse=True)
                    consolidated[chain] = chain_data

            return consolidated

        except Exception as e:
            logger.error(f"Data consolidation error: {e}")
            return {}

    def _update_api_health(self, api_name: str, success: bool):
        """Update API health status"""
        try:
            if success:
                self.api_health[api_name]['status'] = 'healthy'
                self.api_health[api_name]['last_success'] = datetime.now()
                self.api_health[api_name]['error_count'] = 0
            else:
                self.api_health[api_name]['error_count'] += 1
                if self.api_health[api_name]['error_count'] >= 3:
                    self.api_health[api_name]['status'] = 'unhealthy'
        except Exception as e:
            logger.error(f"API health update error: {e}")

    def get_api_health_status(self) -> Dict:
        """Get current API health status"""
        return {
            'apis': self.api_health,
            'last_check': self.last_health_check.isoformat() if self.last_health_check else None
        }

class DataValidator:
    """Enhanced data validation for price data quality"""

    def __init__(self):
        self.known_price_ranges = {
            'USDC': {'min': 0.95, 'max': 1.05},
            'USDT': {'min': 0.95, 'max': 1.05},
            'DAI': {'min': 0.95, 'max': 1.05},
            'BUSD': {'min': 0.95, 'max': 1.05},
            'WETH': {'min': 1000, 'max': 10000},
            'WBTC': {'min': 20000, 'max': 100000},
            'BNB': {'min': 100, 'max': 1000},
            'SOL': {'min': 10, 'max': 500},
            'MATIC': {'min': 0.1, 'max': 5.0}
        }

    def validate_price_data(self, data: Dict, token_symbol: str) -> Dict:
        """Validate price data quality and filter out corrupted data"""
        validated = {}

        try:
            for chain, pairs in data.items():
                valid_pairs = []

                for pair in pairs:
                    if self._is_valid_pair(pair, token_symbol):
                        valid_pairs.append(pair)

                if valid_pairs:
                    validated[chain] = valid_pairs

            return validated

        except Exception as e:
            logger.error(f"Data validation error: {e}")
            return {}

    def _is_valid_pair(self, pair: Dict, token_symbol: str) -> bool:
        """Check if a pair has valid data"""
        try:
            # Check basic structure
            if not pair.get('baseToken', {}).get('symbol'):
                return False

            # Check if symbol matches
            pair_symbol = pair['baseToken']['symbol'].upper()
            if pair_symbol != token_symbol.upper():
                return False

            # Check price validity
            price_usd = pair.get('priceUsd')
            if not price_usd:
                return False

            try:
                price = float(price_usd)
                if price <= 0 or price > 1000000:  # Reasonable price range
                    return False
            except (ValueError, TypeError):
                return False

            # Check known price ranges for stablecoins and major tokens
            if token_symbol.upper() in self.known_price_ranges:
                price_range = self.known_price_ranges[token_symbol.upper()]
                if not (price_range['min'] <= price <= price_range['max']):
                    logger.warning(f"Price out of range for {token_symbol}: ${price} (expected ${price_range['min']}-${price_range['max']})")
                    return False

            # Check liquidity
            liquidity = pair.get('liquidity', {}).get('usd', 0)
            if liquidity < 1000:  # Minimum $1K liquidity
                return False

            return True

        except Exception as e:
            logger.error(f"Pair validation error: {e}")
            return False

    def calculate_validation_score(self, pair: Dict, token_symbol: str) -> float:
        """Calculate validation score for data quality ranking"""
        try:
            score = 0.0

            # Base score for valid data
            if self._is_valid_pair(pair, token_symbol):
                score += 50.0

            # Bonus for high liquidity
            liquidity = pair.get('liquidity', {}).get('usd', 0)
            if liquidity > 100000:
                score += 20.0
            elif liquidity > 50000:
                score += 10.0

            # Bonus for high volume
            volume = pair.get('volume', {}).get('h24', 0)
            if volume > 100000:
                score += 20.0
            elif volume > 50000:
                score += 10.0

            # Bonus for known DEXs
            dex_id = pair.get('dexId', '').lower()
            known_dexs = ['uniswap', 'sushiswap', 'pancakeswap', 'raydium', 'orca', 'quickswap']
            if any(known_dex in dex_id for known_dex in known_dexs):
                score += 10.0

            return score

        except Exception as e:
            logger.error(f"Validation score calculation error: {e}")
            return 0.0

class SimpleAPIClient:
    """Simple HTTP client with rate limiting"""

    def __init__(self, base_url: str, rate_limit: int):
        self.base_url = base_url.rstrip('/')
        self.rate_limit = rate_limit
        self.last_request = 0
        self.session = None

    async def get(self, endpoint: str, params: Dict = None) -> Dict:
        """Make GET request with rate limiting"""
        try:
            # Rate limiting
            now = time.time()
            time_since_last = now - self.last_request
            min_interval = 60.0 / self.rate_limit

            if time_since_last < min_interval:
                await asyncio.sleep(min_interval - time_since_last)

            # Create session if needed
            if not self.session:
                self.session = aiohttp.ClientSession()

            # Make request
            url = f"{self.base_url}/{endpoint.lstrip('/')}"

            async with self.session.get(url, params=params) as response:
                self.last_request = time.time()

                if response.status == 200:
                    return await response.json()
                else:
                    logger.warning(f"API request failed: {response.status} for {url}")
                    return {}

        except Exception as e:
            logger.error(f"API client error: {e}")
            return {}

    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()

class SmartCache:
    """TTL-based caching system with different expiration times"""

    def __init__(self):
        self.cache = {}
        self.ttl_config = {
            'prices': 30,      # 30 seconds
            'security': 3600,  # 1 hour
            'static': 86400    # 24 hours
        }
        self.hit_count = 0
        self.miss_count = 0

    def _is_expired(self, entry: Dict, cache_type: str) -> bool:
        """Check if cache entry is expired"""
        ttl = self.ttl_config.get(cache_type, 300)
        return (datetime.now() - entry['timestamp']).seconds > ttl

    def get(self, key: str, cache_type: str = 'prices') -> Optional[Any]:
        """Get cached data if not expired"""
        if key in self.cache:
            entry = self.cache[key]
            if not self._is_expired(entry, cache_type):
                self.hit_count += 1
                return entry['data']
            else:
                del self.cache[key]

        self.miss_count += 1
        return None

    def set(self, key: str, data: Any, cache_type: str = 'prices'):
        """Set cached data with timestamp"""
        self.cache[key] = {
            'data': data,
            'timestamp': datetime.now()
        }

    def clear_expired(self):
        """Remove expired entries"""
        expired_keys = []
        for key, entry in self.cache.items():
            # Use default cache type for cleanup
            if self._is_expired(entry, 'prices'):
                expired_keys.append(key)

        for key in expired_keys:
            del self.cache[key]

    def get_hit_rate(self) -> float:
        """Calculate cache hit rate"""
        total = self.hit_count + self.miss_count
        return (self.hit_count / total * 100) if total > 0 else 0

class DynamicTokenDiscovery:
    """v3.0 ENHANCED: Dynamic token discovery system for trending tokens"""

    def __init__(self, cache: SmartCache):
        self.cache = cache
        self.discovery_endpoints = {
            'trending': 'https://api.dexscreener.com/latest/dex/tokens/trending',
            'gainers': 'https://api.dexscreener.com/latest/dex/tokens/gainers',
            'new': 'https://api.dexscreener.com/latest/dex/tokens/new'
        }
        self.minimum_thresholds = {
            'liquidity_usd': 100000,  # $100K+ liquidity
            'volume_24h_usd': 50000,  # $50K+ 24h volume
            'age_hours': 24           # 24+ hours old
        }
        self.discovered_tokens = {}
        self.last_discovery = None

    async def discover_trending_tokens(self, chains: List[str] = None) -> Dict[str, List[str]]:
        """
        Discover trending tokens from DexScreener API
        Returns: {chain: [token_symbols]}
        """
        try:
            if chains is None:
                chains = ['ethereum', 'bsc', 'polygon', 'arbitrum', 'solana']

            discovered = {chain: [] for chain in chains}

            # Check cache first
            cache_key = f"trending_discovery_{hash(tuple(sorted(chains)))}"
            cached_data = self.cache.get(cache_key, 'static')

            if cached_data and self._is_discovery_fresh():
                logger.info("⚡ Using cached trending token discovery")
                return cached_data

            # Fetch trending tokens
            for endpoint_name, endpoint_url in self.discovery_endpoints.items():
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(endpoint_url) as response:
                            if response.status == 200:
                                data = await response.json()
                                tokens = await self._process_discovery_data(data, chains, endpoint_name)

                                # Merge discovered tokens
                                for chain, token_list in tokens.items():
                                    discovered[chain].extend(token_list)

                            await asyncio.sleep(0.2)  # Rate limiting

                except Exception as e:
                    logger.error(f"Discovery endpoint {endpoint_name} error: {e}")
                    continue

            # Remove duplicates and validate
            for chain in discovered:
                discovered[chain] = list(set(discovered[chain]))
                discovered[chain] = await self._validate_discovered_tokens(discovered[chain], chain)

            # Cache results for 24 hours
            self.cache.set(cache_key, discovered, 'static')
            self.last_discovery = datetime.now()

            total_discovered = sum(len(tokens) for tokens in discovered.values())
            logger.info(f"🔍 Dynamic discovery completed: {total_discovered} new tokens across {len(chains)} chains")

            return discovered

        except Exception as e:
            logger.error(f"Dynamic token discovery error: {e}")
            return {chain: [] for chain in (chains or [])}

    async def _process_discovery_data(self, data: Dict, target_chains: List[str], source: str) -> Dict[str, List[str]]:
        """Process discovery API response data"""
        discovered = {chain: [] for chain in target_chains}

        try:
            pairs = data.get('pairs', [])

            for pair in pairs:
                chain_id = pair.get('chainId', '').lower()
                if chain_id not in target_chains:
                    continue

                # Validate against minimum thresholds
                if not self._meets_minimum_thresholds(pair):
                    continue

                base_token = pair.get('baseToken', {})
                token_symbol = base_token.get('symbol', '').upper()

                if token_symbol and len(token_symbol) <= 10:  # Reasonable symbol length
                    discovered[chain_id].append(token_symbol)

            return discovered

        except Exception as e:
            logger.error(f"Discovery data processing error: {e}")
            return {chain: [] for chain in target_chains}

    def _meets_minimum_thresholds(self, pair: Dict) -> bool:
        """Check if token meets minimum thresholds for inclusion"""
        try:
            liquidity = pair.get('liquidity', {}).get('usd', 0)
            volume_24h = pair.get('volume', {}).get('h24', 0)

            # Check liquidity and volume thresholds
            if liquidity < self.minimum_thresholds['liquidity_usd']:
                return False

            if volume_24h < self.minimum_thresholds['volume_24h_usd']:
                return False

            # Check token age (if available)
            pair_created_at = pair.get('pairCreatedAt')
            if pair_created_at:
                try:
                    created_time = datetime.fromtimestamp(pair_created_at / 1000)
                    age_hours = (datetime.now() - created_time).total_seconds() / 3600

                    if age_hours < self.minimum_thresholds['age_hours']:
                        return False
                except:
                    pass  # If timestamp parsing fails, skip age check

            return True

        except Exception as e:
            logger.error(f"Threshold validation error: {e}")
            return False

    async def _validate_discovered_tokens(self, tokens: List[str], chain: str) -> List[str]:
        """Validate discovered tokens against DexScreener API"""
        validated = []

        try:
            # Batch validate tokens
            for i in range(0, len(tokens), 10):  # Process in batches of 10
                batch = tokens[i:i+10]

                # Quick validation via search
                search_query = " OR ".join([f"symbol:{token}" for token in batch])

                async with aiohttp.ClientSession() as session:
                    url = "https://api.dexscreener.com/latest/dex/search"
                    params = {'q': search_query, 'chain': chain}

                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            pairs = data.get('pairs', [])

                            # Extract validated symbols
                            for pair in pairs:
                                base_token = pair.get('baseToken', {})
                                symbol = base_token.get('symbol', '').upper()

                                if symbol in batch and symbol not in validated:
                                    validated.append(symbol)

                await asyncio.sleep(0.1)  # Rate limiting

            return validated

        except Exception as e:
            logger.error(f"Token validation error: {e}")
            return tokens  # Return original list if validation fails

    def _is_discovery_fresh(self) -> bool:
        """Check if last discovery is still fresh (within 24 hours)"""
        if not self.last_discovery:
            return False

        return (datetime.now() - self.last_discovery).total_seconds() < 86400  # 24 hours

class DynamicProfitThresholdManager:
    """v3.0 ENHANCED: Dynamic profit threshold management with market volatility adjustment"""

    def __init__(self):
        self.base_thresholds = {
            'stablecoins': {'min': 0.1, 'max': 1.0},      # Very low for stablecoins
            'blue_chips': {'min': 0.5, 'max': 2.0},       # Low for blue chips
            'defi': {'min': 1.0, 'max': 3.0},             # Moderate for DeFi
            'layer1_2': {'min': 0.8, 'max': 2.5},         # Low for layer 1/2
            'meme_coins': {'min': 1.5, 'max': 8.0},       # Much lower for memes
            'gaming_nft': {'min': 1.2, 'max': 4.0},       # Much lower for gaming
            'ai_big_data': {'min': 1.0, 'max': 3.0},      # Low for AI/data
            'bsc_ecosystem': {'min': 0.8, 'max': 2.5},    # Low for BSC
            'solana_ecosystem': {'min': 0.8, 'max': 3.0}  # Low for Solana
        }
        self.volatility_multipliers = {
            'low': 0.7,      # Reduce thresholds more in low volatility
            'normal': 1.0,   # Standard thresholds
            'high': 1.1,     # Only slight increase in high volatility
            'extreme': 1.2   # Moderate increase in extreme volatility
        }
        self.current_volatility = 'low'  # Start with low volatility for more opportunities
        self.last_volatility_update = None
        self.custom_overrides = {}

    def get_profit_threshold(self, token_symbol: str, category: str) -> Dict[str, float]:
        """
        Get dynamic profit threshold for a specific token/category
        Returns: {'min': float, 'max': float}
        """
        try:
            # Check for custom overrides first
            if token_symbol in self.custom_overrides:
                return self.custom_overrides[token_symbol]

            # Get base threshold for category
            base_threshold = self.base_thresholds.get(category, {'min': 5.0, 'max': 10.0})

            # Apply volatility adjustment
            volatility_multiplier = self.volatility_multipliers.get(self.current_volatility, 1.0)

            adjusted_threshold = {
                'min': base_threshold['min'] * volatility_multiplier,
                'max': base_threshold['max'] * volatility_multiplier
            }

            # Ensure minimum thresholds
            adjusted_threshold['min'] = max(0.1, adjusted_threshold['min'])
            adjusted_threshold['max'] = max(adjusted_threshold['min'] + 0.5, adjusted_threshold['max'])

            return adjusted_threshold

        except Exception as e:
            logger.error(f"Profit threshold calculation error: {e}")
            return {'min': 5.0, 'max': 10.0}  # Default fallback

    async def update_volatility_based_thresholds(self) -> str:
        """
        Update profit thresholds based on market volatility
        Returns: current volatility level
        """
        try:
            # Check if update is needed (every 4 hours)
            if (self.last_volatility_update and
                (datetime.now() - self.last_volatility_update).total_seconds() < 14400):
                return self.current_volatility

            # Fetch market volatility indicators
            volatility_score = await self._calculate_market_volatility()

            # Determine volatility level
            if volatility_score < 20:
                self.current_volatility = 'low'
            elif volatility_score < 40:
                self.current_volatility = 'normal'
            elif volatility_score < 70:
                self.current_volatility = 'high'
            else:
                self.current_volatility = 'extreme'

            self.last_volatility_update = datetime.now()

            logger.info(f"📊 Market volatility updated: {self.current_volatility} (score: {volatility_score})")
            return self.current_volatility

        except Exception as e:
            logger.error(f"Volatility update error: {e}")
            return self.current_volatility

    async def _calculate_market_volatility(self) -> float:
        """
        Calculate market volatility score (0-100)
        Uses simplified VIX-style calculation
        """
        try:
            # Simplified volatility calculation using major token price movements
            # In production, this would integrate with Fear & Greed Index API

            # For now, use a mock calculation based on time of day and random factors
            import random

            # Base volatility (simulated)
            base_volatility = random.uniform(15, 45)

            # Time-based adjustment (higher volatility during US trading hours)
            current_hour = datetime.now().hour
            if 14 <= current_hour <= 21:  # US trading hours (UTC)
                base_volatility *= 1.2
            elif 0 <= current_hour <= 6:   # Asian trading hours
                base_volatility *= 1.1

            # Weekend adjustment (lower volatility)
            if datetime.now().weekday() >= 5:  # Saturday/Sunday
                base_volatility *= 0.8

            return min(100, max(0, base_volatility))

        except Exception as e:
            logger.error(f"Volatility calculation error: {e}")
            return 30.0  # Default moderate volatility

    def set_custom_threshold(self, token_symbol: str, min_profit: float, max_profit: float):
        """Set custom profit threshold for specific token"""
        self.custom_overrides[token_symbol] = {
            'min': max(0.1, min_profit),
            'max': max(min_profit + 0.5, max_profit)
        }
        logger.info(f"🎯 Custom threshold set for {token_symbol}: {min_profit}%-{max_profit}%")

    def remove_custom_threshold(self, token_symbol: str):
        """Remove custom threshold for specific token"""
        if token_symbol in self.custom_overrides:
            del self.custom_overrides[token_symbol]
            logger.info(f"🗑️ Custom threshold removed for {token_symbol}")

    def get_all_thresholds(self) -> Dict:
        """Get all current thresholds with volatility adjustments"""
        all_thresholds = {}

        for category, base_threshold in self.base_thresholds.items():
            volatility_multiplier = self.volatility_multipliers.get(self.current_volatility, 1.0)

            all_thresholds[category] = {
                'min': base_threshold['min'] * volatility_multiplier,
                'max': base_threshold['max'] * volatility_multiplier,
                'base_min': base_threshold['min'],
                'base_max': base_threshold['max'],
                'volatility_multiplier': volatility_multiplier
            }

        return {
            'thresholds': all_thresholds,
            'current_volatility': self.current_volatility,
            'custom_overrides': self.custom_overrides,
            'last_update': self.last_volatility_update.isoformat() if self.last_volatility_update else None
        }

    def get_threshold_stats(self) -> Dict:
        """Get statistics about threshold usage"""
        return {
            'total_categories': len(self.base_thresholds),
            'custom_overrides_count': len(self.custom_overrides),
            'current_volatility': self.current_volatility,
            'volatility_multiplier': self.volatility_multipliers.get(self.current_volatility, 1.0),
            'last_volatility_update': self.last_volatility_update.isoformat() if self.last_volatility_update else None
        }

class TieredScanningManager:
    """v3.0 ENHANCED: Multi-tier intelligent scanning architecture for 1000+ tokens"""

    def __init__(self, token_categories: Dict, cache: SmartCache):
        self.token_categories = token_categories
        self.cache = cache
        self.tier_definitions = {
            1: {
                'name': 'Priority Tier',
                'max_tokens': 100,
                'scan_interval': 30,  # seconds
                'description': 'High-priority tokens with frequent opportunities'
            },
            2: {
                'name': 'Regular Tier',
                'max_tokens': 400,
                'scan_interval': 120,  # 2 minutes
                'description': 'Standard tokens with moderate activity'
            },
            3: {
                'name': 'Discovery Tier',
                'max_tokens': 500,
                'scan_interval': 300,  # 5 minutes
                'description': 'Discovery tokens and low-activity assets'
            }
        }
        self.tier_assignments = {1: [], 2: [], 3: []}
        self.token_metrics = {}  # Track performance metrics per token
        self.last_tier_update = None
        self.promotion_thresholds = {
            'opportunity_frequency': 0.1,  # opportunities per scan
            'avg_volume_24h': 100000,      # $100K+ average volume
            'avg_liquidity': 500000        # $500K+ average liquidity
        }

    def initialize_tiers(self) -> Dict[int, List[str]]:
        """Initialize tier assignments based on token categories"""
        try:
            # Clear existing assignments
            self.tier_assignments = {1: [], 2: [], 3: []}

            # Tier 1: Priority tokens (stablecoins, blue chips, top Solana)
            tier1_categories = ['stablecoins', 'blue_chips']
            for category in tier1_categories:
                if category in self.token_categories:
                    category_data = self.token_categories[category]
                    tokens = category_data.get('tokens', [])
                    tier = category_data.get('tier', 2)

                    if tier == 1:
                        self.tier_assignments[1].extend(tokens[:30])  # Top 30 per category

            # Add priority Solana tokens to Tier 1
            if 'solana_ecosystem' in self.token_categories:
                solana_priority = [
                    "SOL", "WSOL", "RAY", "ORCA", "SRM", "BONK", "WIF", "POPCAT",
                    "MYRO", "BOME", "MEW", "ATLAS", "POLIS", "stSOL", "mSOL", "jitoSOL",
                    "JITO", "PYTH", "RENDER", "JUP", "TNSR", "KAMINO"
                ]
                self.tier_assignments[1].extend(solana_priority)

            # Tier 2: Regular tokens (DeFi, gaming, layer1_2)
            tier2_categories = ['defi', 'gaming_nft', 'layer1_2', 'ai_big_data', 'bsc_ecosystem']
            for category in tier2_categories:
                if category in self.token_categories:
                    category_data = self.token_categories[category]
                    tokens = category_data.get('tokens', [])
                    tier = category_data.get('tier', 2)

                    if tier == 2:
                        max_tokens = min(80, len(tokens))  # Up to 80 per category
                        self.tier_assignments[2].extend(tokens[:max_tokens])

            # Tier 3: Discovery tokens (memes, remaining tokens)
            tier3_categories = ['meme_coins']
            for category in tier3_categories:
                if category in self.token_categories:
                    category_data = self.token_categories[category]
                    tokens = category_data.get('tokens', [])
                    tier = category_data.get('tier', 3)

                    if tier == 3:
                        self.tier_assignments[3].extend(tokens)

            # Add remaining Solana tokens to appropriate tiers
            if 'solana_ecosystem' in self.token_categories:
                all_solana = self.token_categories['solana_ecosystem']['tokens']
                remaining_solana = [t for t in all_solana if t not in self.tier_assignments[1]]

                # Split remaining Solana between Tier 2 and 3
                self.tier_assignments[2].extend(remaining_solana[:100])  # Next 100 to Tier 2
                self.tier_assignments[3].extend(remaining_solana[100:])  # Rest to Tier 3

            # Remove duplicates and enforce limits
            for tier in [1, 2, 3]:
                self.tier_assignments[tier] = list(dict.fromkeys(self.tier_assignments[tier]))  # Remove duplicates
                max_tokens = self.tier_definitions[tier]['max_tokens']
                self.tier_assignments[tier] = self.tier_assignments[tier][:max_tokens]

            # Log tier distribution
            for tier, tokens in self.tier_assignments.items():
                tier_name = self.tier_definitions[tier]['name']
                interval = self.tier_definitions[tier]['scan_interval']
                logger.info(f"🎯 {tier_name}: {len(tokens)} tokens (scan every {interval}s)")

            self.last_tier_update = datetime.now()
            return self.tier_assignments

        except Exception as e:
            logger.error(f"Tier initialization error: {e}")
            return self.tier_assignments

    def get_tokens_for_tier(self, tier: int) -> List[str]:
        """Get tokens assigned to specific tier"""
        return self.tier_assignments.get(tier, [])

    def get_scan_interval(self, tier: int) -> int:
        """Get scan interval for specific tier"""
        return self.tier_definitions.get(tier, {}).get('scan_interval', 300)

    async def update_token_metrics(self, token: str, metrics: Dict):
        """Update performance metrics for a token"""
        try:
            if token not in self.token_metrics:
                self.token_metrics[token] = {
                    'opportunity_count': 0,
                    'total_scans': 0,
                    'avg_volume_24h': 0,
                    'avg_liquidity': 0,
                    'last_opportunity': None,
                    'tier_history': []
                }

            token_data = self.token_metrics[token]
            token_data['total_scans'] += 1

            # Update metrics
            if metrics.get('has_opportunity', False):
                token_data['opportunity_count'] += 1
                token_data['last_opportunity'] = datetime.now()

            if 'volume_24h' in metrics:
                # Rolling average
                current_avg = token_data['avg_volume_24h']
                new_volume = metrics['volume_24h']
                token_data['avg_volume_24h'] = (current_avg * 0.9) + (new_volume * 0.1)

            if 'liquidity' in metrics:
                # Rolling average
                current_avg = token_data['avg_liquidity']
                new_liquidity = metrics['liquidity']
                token_data['avg_liquidity'] = (current_avg * 0.9) + (new_liquidity * 0.1)

        except Exception as e:
            logger.error(f"Token metrics update error: {e}")

    async def evaluate_tier_promotions_demotions(self) -> Dict[str, List[str]]:
        """
        Evaluate tokens for tier promotion/demotion based on 7-day rolling metrics
        Returns: {'promoted': [tokens], 'demoted': [tokens]}
        """
        try:
            # Only evaluate once per day
            if (self.last_tier_update and
                (datetime.now() - self.last_tier_update).total_seconds() < 86400):
                return {'promoted': [], 'demoted': []}

            promoted = []
            demoted = []

            # Evaluate each token's performance
            for token, metrics in self.token_metrics.items():
                current_tier = self._get_token_current_tier(token)
                if current_tier is None:
                    continue

                # Calculate performance score
                opportunity_frequency = (metrics['opportunity_count'] / max(1, metrics['total_scans']))
                avg_volume = metrics['avg_volume_24h']
                avg_liquidity = metrics['avg_liquidity']

                # Promotion criteria
                should_promote = (
                    current_tier > 1 and
                    opportunity_frequency >= self.promotion_thresholds['opportunity_frequency'] and
                    avg_volume >= self.promotion_thresholds['avg_volume_24h'] and
                    avg_liquidity >= self.promotion_thresholds['avg_liquidity']
                )

                # Demotion criteria
                should_demote = (
                    current_tier < 3 and
                    opportunity_frequency < self.promotion_thresholds['opportunity_frequency'] * 0.3 and
                    avg_volume < self.promotion_thresholds['avg_volume_24h'] * 0.5
                )

                if should_promote:
                    new_tier = current_tier - 1
                    if self._move_token_to_tier(token, current_tier, new_tier):
                        promoted.append(token)
                        logger.info(f"⬆️ Promoted {token}: Tier {current_tier} → Tier {new_tier}")

                elif should_demote:
                    new_tier = current_tier + 1
                    if self._move_token_to_tier(token, current_tier, new_tier):
                        demoted.append(token)
                        logger.info(f"⬇️ Demoted {token}: Tier {current_tier} → Tier {new_tier}")

            self.last_tier_update = datetime.now()

            if promoted or demoted:
                logger.info(f"🔄 Tier updates: {len(promoted)} promoted, {len(demoted)} demoted")

            return {'promoted': promoted, 'demoted': demoted}

        except Exception as e:
            logger.error(f"Tier evaluation error: {e}")
            return {'promoted': [], 'demoted': []}

    def _get_token_current_tier(self, token: str) -> Optional[int]:
        """Get current tier assignment for token"""
        for tier, tokens in self.tier_assignments.items():
            if token in tokens:
                return tier
        return None

    def _move_token_to_tier(self, token: str, from_tier: int, to_tier: int) -> bool:
        """Move token from one tier to another"""
        try:
            # Check if target tier has space
            target_max = self.tier_definitions[to_tier]['max_tokens']
            if len(self.tier_assignments[to_tier]) >= target_max:
                return False

            # Remove from current tier
            if token in self.tier_assignments[from_tier]:
                self.tier_assignments[from_tier].remove(token)

            # Add to new tier
            self.tier_assignments[to_tier].append(token)

            # Update token history
            if token in self.token_metrics:
                self.token_metrics[token]['tier_history'].append({
                    'from_tier': from_tier,
                    'to_tier': to_tier,
                    'timestamp': datetime.now().isoformat()
                })

            return True

        except Exception as e:
            logger.error(f"Token tier move error: {e}")
            return False

    def get_tier_statistics(self) -> Dict:
        """Get comprehensive tier statistics"""
        stats = {}

        for tier, tokens in self.tier_assignments.items():
            tier_info = self.tier_definitions[tier]

            # Calculate tier metrics
            tier_metrics = {
                'token_count': len(tokens),
                'max_tokens': tier_info['max_tokens'],
                'scan_interval': tier_info['scan_interval'],
                'utilization': len(tokens) / tier_info['max_tokens'] * 100,
                'avg_opportunity_frequency': 0,
                'total_opportunities': 0
            }

            # Aggregate token metrics for this tier
            tier_opportunities = 0
            tier_scans = 0

            for token in tokens:
                if token in self.token_metrics:
                    metrics = self.token_metrics[token]
                    tier_opportunities += metrics['opportunity_count']
                    tier_scans += metrics['total_scans']

            if tier_scans > 0:
                tier_metrics['avg_opportunity_frequency'] = tier_opportunities / tier_scans

            tier_metrics['total_opportunities'] = tier_opportunities
            stats[f'tier_{tier}'] = tier_metrics

        return {
            'tier_stats': stats,
            'total_tokens': sum(len(tokens) for tokens in self.tier_assignments.values()),
            'last_tier_update': self.last_tier_update.isoformat() if self.last_tier_update else None,
            'promotion_thresholds': self.promotion_thresholds
        }

    def get_discovery_stats(self) -> Dict:
        """Get statistics about discovered tokens"""
        total_discovered = sum(len(tokens) for tokens in self.discovered_tokens.values())

        return {
            'total_discovered': total_discovered,
            'by_chain': {chain: len(tokens) for chain, tokens in self.discovered_tokens.items()},
            'last_discovery': self.last_discovery.isoformat() if self.last_discovery else None,
            'thresholds': self.minimum_thresholds
        }

@dataclass
class ScanMetrics:
    """Data class for scan performance metrics"""
    total_tokens: int
    tokens_per_second: float
    api_response_times: List[float]
    cache_hit_rate: float
    success_rate: float
    scan_duration: float
    errors_count: int

class TokenBatchProcessor:
    """Intelligent API request batching for optimal performance"""

    def __init__(self, max_batch_size: int = 10):
        self.max_batch_size = max_batch_size
        self.rate_limiter = {}
        self.request_times = []

    async def process_token_batch(self, tokens: List[str], chain: str, api_client) -> List[Dict]:
        """Process tokens in optimized batches"""
        results = []

        # Split tokens into batches
        for i in range(0, len(tokens), self.max_batch_size):
            batch = tokens[i:i + self.max_batch_size]

            # Apply rate limiting
            await self._apply_rate_limit(chain)

            # Process batch
            start_time = time.time()
            batch_results = await self._process_single_batch(batch, chain, api_client)
            end_time = time.time()

            # Track performance
            self.request_times.append(end_time - start_time)
            results.extend(batch_results)

            # Small delay between batches
            await asyncio.sleep(0.1)

        return results

    async def _apply_rate_limit(self, chain: str):
        """Apply exponential backoff rate limiting"""
        current_time = time.time()
        last_request = self.rate_limiter.get(chain, 0)

        # Minimum 200ms between requests per chain
        min_interval = 0.2
        time_since_last = current_time - last_request

        if time_since_last < min_interval:
            await asyncio.sleep(min_interval - time_since_last)

        self.rate_limiter[chain] = time.time()

    async def _process_single_batch(self, tokens: List[str], chain: str, api_client) -> List[Dict]:
        """Process a single batch of tokens"""
        try:
            # Create search query for batch
            search_query = " OR ".join(tokens)

            # Make API request
            response = await api_client.get(f"dex/search", {
                'q': search_query,
                'chain': chain
            })

            return response.get('pairs', [])

        except Exception as e:
            logger.error(f"Batch processing error: {e}")
            return []

    def get_avg_response_time(self) -> float:
        """Get average API response time"""
        return sum(self.request_times) / len(self.request_times) if self.request_times else 0

    async def process_solana_tokens_optimized(self, solana_tokens: List[str], api_client) -> List[Dict]:
        """Optimized processing specifically for Solana tokens"""
        results = []

        try:
            # Use smaller batches for Solana due to higher token density
            solana_batch_size = min(8, self.max_batch_size)

            # Split into priority and regular tokens
            priority_tokens = [
                "SOL", "WSOL", "RAY", "ORCA", "SRM", "BONK", "WIF", "POPCAT",
                "MYRO", "BOME", "MEW", "ATLAS", "POLIS", "stSOL", "mSOL", "jitoSOL"
            ]

            # Process priority tokens first
            priority_batch = [t for t in solana_tokens if t in priority_tokens]
            if priority_batch:
                priority_results = await self._process_solana_batch(priority_batch[:solana_batch_size], api_client)
                results.extend(priority_results)

            # Process remaining tokens in optimized batches
            remaining_tokens = [t for t in solana_tokens if t not in priority_tokens]
            for i in range(0, len(remaining_tokens), solana_batch_size):
                batch = remaining_tokens[i:i + solana_batch_size]

                # Apply Solana-specific rate limiting
                await self._apply_solana_rate_limit()

                batch_results = await self._process_solana_batch(batch, api_client)
                results.extend(batch_results)

                # Shorter delay for Solana batches
                await asyncio.sleep(0.05)

            return results

        except Exception as e:
            logger.error(f"Solana token processing error: {e}")
            return []

    async def _process_solana_batch(self, tokens: List[str], api_client) -> List[Dict]:
        """Process a single batch of Solana tokens with chain-specific optimization"""
        try:
            start_time = time.time()

            # Create Solana-specific search query
            search_query = " OR ".join([f"symbol:{token}" for token in tokens])

            # Make API request with Solana chain filter
            response = await api_client.get("dex/search", {
                'q': search_query,
                'chain': 'solana'
            })

            end_time = time.time()
            self.request_times.append(end_time - start_time)

            # Filter and validate Solana pairs
            pairs = response.get('pairs', [])
            solana_pairs = [
                pair for pair in pairs
                if pair.get('chainId', '').lower() == 'solana' and
                   pair.get('baseToken', {}).get('symbol', '').upper() in [t.upper() for t in tokens]
            ]

            return solana_pairs

        except Exception as e:
            logger.error(f"Solana batch processing error: {e}")
            return []

    async def _apply_solana_rate_limit(self):
        """Apply Solana-specific rate limiting (more aggressive)"""
        current_time = time.time()
        last_request = self.rate_limiter.get('solana', 0)

        # Minimum 150ms between Solana requests (more aggressive than general)
        min_interval = 0.15
        time_since_last = current_time - last_request

        if time_since_last < min_interval:
            await asyncio.sleep(min_interval - time_since_last)

        self.rate_limiter['solana'] = time.time()

class ParallelScanner:
    """High-performance parallel scanning for multiple chains"""

    def __init__(self, cache: SmartCache, batch_processor: TokenBatchProcessor):
        self.cache = cache
        self.batch_processor = batch_processor
        self.scan_metrics = ScanMetrics(0, 0, [], 0, 0, 0, 0)

    async def scan_chains_parallel(self, tokens: List[str], chains: List[str], api_client) -> List[Dict]:
        """Scan multiple chains in parallel with Solana optimization"""
        start_time = time.time()
        all_opportunities = []
        errors_count = 0

        try:
            # ENHANCED: Separate Solana tokens for optimized processing
            solana_tokens = []
            other_tokens = []

            # Categorize tokens
            for token in tokens:
                # Check if token is in Solana ecosystem (case-insensitive)
                if any(token.upper() == sol_token.upper() for sol_token in [
                    "SOL", "WSOL", "RAY", "ORCA", "SRM", "FIDA", "COPE", "STEP", "TULIP", "SABER",
                    "MERCURIAL", "PORT", "BONK", "WIF", "POPCAT", "MYRO", "BOME", "SLERF", "BOOK",
                    "MEW", "MOTHER", "DADDY", "ATLAS", "POLIS", "AURORY", "GENOPETS", "stSOL",
                    "mSOL", "jitoSOL", "JSOL", "BSOL", "MNGO", "DRIFT", "ZETA", "HXRO", "CYPHER"
                ]):
                    solana_tokens.append(token)
                else:
                    other_tokens.append(token)

            # Create tasks for parallel chain scanning
            tasks = []

            # PRIORITY: Process Solana chain with optimized method if included
            if "solana" in chains and solana_tokens:
                solana_task = self._scan_solana_optimized(solana_tokens, api_client)
                tasks.append(("solana", solana_task))

                # Remove solana from regular chain processing
                remaining_chains = [c for c in chains if c != "solana"]
            else:
                remaining_chains = chains

            # Process other chains with regular tokens
            for chain in remaining_chains:
                chain_tokens = other_tokens if chain != "solana" else tokens
                task = self._scan_chain_with_cache(chain_tokens, chain, api_client)
                tasks.append((chain, task))

            # Execute all chain scans in parallel
            chain_results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)

            # Process results
            for i, result in enumerate(chain_results):
                chain_name = tasks[i][0] if i < len(tasks) else f"chain_{i}"
                if isinstance(result, Exception):
                    logger.error(f"Chain {chain_name} scan error: {result}")
                    errors_count += 1
                else:
                    all_opportunities.extend(result)
                    if chain_name == "solana":
                        logger.info(f"🌟 Solana optimized scan completed: {len(result)} pairs found")

            # Calculate performance metrics
            end_time = time.time()
            scan_duration = end_time - start_time
            tokens_per_second = len(tokens) * len(chains) / scan_duration if scan_duration > 0 else 0

            self.scan_metrics = ScanMetrics(
                total_tokens=len(tokens) * len(chains),
                tokens_per_second=tokens_per_second,
                api_response_times=self.batch_processor.request_times[-10:],  # Last 10 requests
                cache_hit_rate=self.cache.get_hit_rate(),
                success_rate=((len(chains) - errors_count) / len(chains) * 100) if chains else 0,
                scan_duration=scan_duration,
                errors_count=errors_count
            )

            return all_opportunities

        except Exception as e:
            logger.error(f"Parallel scan error: {e}")
            return []

    async def _scan_chain_with_cache(self, tokens: List[str], chain: str, api_client) -> List[Dict]:
        """Scan single chain with intelligent caching"""
        opportunities = []

        try:
            # Check cache for recent chain data
            cache_key = f"chain_scan_{chain}_{hash(tuple(sorted(tokens)))}"
            cached_data = self.cache.get(cache_key, 'prices')

            if cached_data:
                return cached_data

            # Process tokens in batches
            chain_pairs = await self.batch_processor.process_token_batch(tokens, chain, api_client)

            # Process pairs for opportunities (simplified)
            for pair in chain_pairs:
                if self._is_valid_pair_fast(pair):
                    opportunities.append(pair)

            # Cache results
            self.cache.set(cache_key, opportunities, 'prices')

            return opportunities

        except Exception as e:
            logger.error(f"Chain {chain} scan error: {e}")
            return []

    def _is_valid_pair_fast(self, pair: Dict) -> bool:
        """Fast validation for high-performance scanning"""
        try:
            return (
                pair.get('priceUsd') and
                float(pair.get('priceUsd', 0)) > 0 and
                pair.get('liquidity', {}).get('usd', 0) > 1000 and
                pair.get('volume', {}).get('h24', 0) > 100
            )
        except:
            return False

    async def _scan_solana_optimized(self, solana_tokens: List[str], api_client) -> List[Dict]:
        """Optimized scanning specifically for Solana ecosystem tokens"""
        opportunities = []

        try:
            logger.info(f"🚀 Starting optimized Solana scan for {len(solana_tokens)} tokens")

            # Check cache for recent Solana data
            cache_key = f"solana_optimized_{hash(tuple(sorted(solana_tokens)))}"
            cached_data = self.cache.get(cache_key, 'prices')

            if cached_data:
                logger.info("⚡ Using cached Solana data")
                return cached_data

            # Use optimized Solana token processing
            solana_pairs = await self.batch_processor.process_solana_tokens_optimized(solana_tokens, api_client)

            # Process pairs for opportunities with Solana-specific validation
            for pair in solana_pairs:
                if self._is_valid_solana_pair(pair):
                    opportunities.append(pair)

            # Cache results with shorter TTL for Solana (more volatile)
            self.cache.set(cache_key, opportunities, 'prices')

            logger.info(f"✅ Solana optimized scan completed: {len(opportunities)} valid pairs")
            return opportunities

        except Exception as e:
            logger.error(f"Solana optimized scan error: {e}")
            return []

    def _is_valid_solana_pair(self, pair: Dict) -> bool:
        """Enhanced validation specifically for Solana pairs"""
        try:
            # Basic validation
            if not self._is_valid_pair_fast(pair):
                return False

            # Solana-specific validation
            chain_id = pair.get('chainId', '').lower()
            if chain_id != 'solana':
                return False

            # Check for valid Solana token address format
            base_token = pair.get('baseToken', {})
            token_address = base_token.get('address', '')

            # Solana addresses are typically 32-44 characters
            if len(token_address) < 32 or len(token_address) > 44:
                return False

            # Additional Solana-specific checks
            liquidity_usd = pair.get('liquidity', {}).get('usd', 0)
            volume_24h = pair.get('volume', {}).get('h24', 0)

            # Higher thresholds for Solana due to ecosystem maturity
            return (
                liquidity_usd > 5000 and  # Higher liquidity threshold
                volume_24h > 1000 and    # Higher volume threshold
                pair.get('priceUsd') and
                float(pair.get('priceUsd', 0)) > 0
            )

        except Exception as e:
            logger.error(f"Solana pair validation error: {e}")
            return False

# Simple WebSocket manager
class SimpleWebSocketManager:
    def __init__(self):
        self.connections = []
    
    async def connect(self, websocket):
        await websocket.accept()
        self.connections.append(websocket)
    
    def disconnect(self, websocket):
        if websocket in self.connections:
            self.connections.remove(websocket)
    
    async def broadcast(self, message):
        for connection in self.connections:
            try:
                await connection.send_text(json.dumps(message))
            except:
                pass

# Simple API client for public APIs
class SimpleAPIClient:
    def __init__(self, base_url: str, rate_limit: int = 30):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def get(self, endpoint: str, params: Dict = None) -> Dict:
        try:
            url = f"{self.base_url}/{endpoint}"
            response = await self.client.get(url, params=params or {})
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            logger.error(f"API request failed: {e}")
            return {}

# v3.0 ENHANCED: Advanced arbitrage detector with intelligent 1000+ token analysis
class AdvancedArbitrageDetector:
    def __init__(self):
        self.opportunities = []
        self.last_scan = None
        self.scan_logs = []

        # v2.0 Components
        self.validator = RealTimeValidator()
        self.security_analyzer = SecurityAnalyzer()
        self.smart_cache = SmartCache()
        self.batch_processor = TokenBatchProcessor(max_batch_size=10)
        self.parallel_scanner = ParallelScanner(self.smart_cache, self.batch_processor)
        self.scan_metrics = ScanMetrics(0, 0, [], 0, 0, 0, 0)

        # v3.0 CRITICAL: Pair validation system to prevent false arbitrage signals
        self.pair_validator = PairValidator()

        # v3.0 ENHANCED: Multi-API data provider
        self.multi_api_provider = MultiAPIDataProvider(self.smart_cache)

        # v3.0 Enhanced configuration
        self.v3_config = {
            'max_total_tokens': 1000,
            'enable_dynamic_discovery': True,
            'enable_tier_management': True,
            'enable_adaptive_thresholds': True,
            'enable_demo_mode': True,  # Enable demo opportunities when real data is insufficient
            'discovery_refresh_hours': 24,
            'tier_evaluation_hours': 24
        }

        # ENHANCED v3.0: Comprehensive 1000+ token categories with intelligent classification
        self.token_categories = {
            "stablecoins": {
                "name": "Stablecoins",
                "tokens": ["USDC", "USDT", "DAI", "BUSD", "FRAX", "TUSD", "LUSD", "MIM", "FDUSD", "PYUSD", "USDD", "GUSD", "USDP", "SUSD", "USDN", "RSR", "FEI", "TRIBE", "RAI", "OUSD", "USDK", "HUSD", "USTC", "USDX", "DUSD", "DOLA", "CUSD", "ZUSD", "MUSD", "NUSD"],
                "priority": 1,
                "profit_threshold": {"min": 0.5, "max": 2.0},
                "tier": 1,
                "blockchain_distribution": {"ethereum": 15, "bsc": 8, "polygon": 4, "arbitrum": 3}
            },
            "blue_chips": {
                "name": "Blue Chip Tokens",
                "tokens": [
                    # Core Layer 1s & Wrapped Assets
                    "WETH", "WBTC", "BNB", "SOL", "ADA", "DOT", "AVAX", "ATOM", "NEAR", "APT", "SUI", "SEI",
                    # Major Altcoins
                    "LTC", "BCH", "XLM", "ALGO", "ICP", "FIL", "VET", "THETA", "EOS", "EGLD", "FLOW", "MINA",
                    # Layer 2s & Scaling Solutions
                    "MATIC", "ARB", "OP", "IMX", "LRC", "METIS", "BOBA", "ZK", "STRK", "MANTA",
                    # Top DeFi Blue Chips
                    "AAVE", "MKR", "UNI", "LINK", "COMP", "CRV", "SNX", "YFI", "SUSHI", "1INCH", "BAL", "LDO",
                    # Additional Blue Chips
                    "RNDR", "GRT", "FTM", "ONE", "HBAR", "XTZ", "KAVA", "OSMO", "JUNO", "SCRT"
                ],
                "priority": 2,
                "profit_threshold": {"min": 2.0, "max": 5.0},
                "tier": 1,
                "blockchain_distribution": {"ethereum": 30, "bsc": 10, "polygon": 8, "arbitrum": 6, "solana": 4}
            },
            "defi": {
                "name": "DeFi Tokens",
                "tokens": [
                    # Ethereum DeFi Protocols (100+ tokens)
                    "UNI", "SUSHI", "COMP", "YFI", "CRV", "BAL", "1INCH", "SNX", "RUNE", "ALPHA", "CREAM", "BADGER", "CVX", "FXS", "LDO", "RPL", "ALCX", "SPELL", "ICE", "DYDX",
                    "FRAX", "FEI", "TRIBE", "OHM", "KLIMA", "TIME", "MEMO", "BTRFLY", "TOKE", "ALCX", "JPEG", "LOOKS", "X2Y2", "BLUR", "BEND", "PARA",
                    "INST", "TORN", "PERP", "DODO", "ZRX", "KNC", "BNT", "REN", "KEEP", "NU", "ANKR", "BAND", "OCEAN", "FETCH", "NMR", "MLN",
                    "REP", "AUGUR", "GNO", "COW", "SAFE", "ENS", "LPT", "API3", "UMA", "BOND", "BARN", "POOL", "IDLE", "PICKLE", "FARM", "HARVEST",

                    # BSC DeFi (50+ tokens)
                    "CAKE", "BAKE", "AUTO", "BELT", "BUNNY", "EPS", "XVS", "VAI", "SXP", "TWT", "ALPACA", "MDX", "BURGER", "WATCH", "SWINGBY",
                    "HARD", "KAVA", "SWP", "USDX", "BNX", "CHESS", "QBT", "DEGO", "FOR", "ANY", "BTCB", "ETH", "ADA", "DOT", "LINK",

                    # Polygon DeFi (30+ tokens)
                    "QUICK", "DQUICK", "GHST", "AAVEGOTCHI", "REVV", "TOWER", "SAND", "MANA", "WMATIC", "USDC", "USDT", "DAI", "WETH", "WBTC",
                    "SUSHI", "CRV", "BAL", "1INCH", "COMP", "AAVE", "UNI", "LINK", "YFI", "SNX", "MKR", "FRAX", "FXS", "CVX", "LDO", "RPL",

                    # Arbitrum DeFi (40+ tokens)
                    "GMX", "GLP", "MAGIC", "RDNT", "VELA", "GRAIL", "PLS", "JONES", "DPX", "UMAMI", "SPERAX", "SPA", "USDS", "ARBI", "PENDLE",
                    "ARB", "WETH", "USDC", "USDT", "DAI", "WBTC", "LINK", "UNI", "AAVE", "CRV", "BAL", "SUSHI", "1INCH", "COMP", "YFI"
                ],
                "priority": 3,
                "profit_threshold": {"min": 5.0, "max": 10.0},
                "tier": 2,
                "blockchain_distribution": {"ethereum": 120, "bsc": 50, "polygon": 30, "arbitrum": 40}
            },
            "layer1_2": {
                "name": "Layer 1/2 Tokens",
                "tokens": [
                    # Ethereum Layer 2s
                    "MATIC", "ARB", "OP", "METIS", "IMX", "LRC", "BOBA", "ZK", "STRK", "MANTA", "BLAST", "MODE", "SCROLL", "LINEA", "BASE",
                    # Alternative Layer 1s
                    "NEAR", "FTM", "ONE", "CELO", "KAVA", "ROSE", "MOVR", "GLMR", "ASTR", "CFG", "PHA", "RING", "CKB", "MINA", "FLOW",
                    # Cosmos Ecosystem
                    "ATOM", "OSMO", "JUNO", "SCRT", "REGEN", "DVPN", "AKT", "XPRT", "NGM", "ROWAN", "IRIS", "BAND", "KAVA", "HARD",
                    # Polkadot Ecosystem
                    "DOT", "KSM", "GLMR", "MOVR", "ASTR", "CFG", "PHA", "RING", "CKB", "DOCK", "OCEAN", "AKRO", "REN", "KEEP"
                ],
                "priority": 4,
                "profit_threshold": {"min": 3.0, "max": 8.0},
                "tier": 2,
                "blockchain_distribution": {"ethereum": 25, "polygon": 15, "arbitrum": 10, "bsc": 8}
            },
            "meme_coins": {
                "name": "Meme Coins",
                "tokens": [
                    # ISSUE 3: MAXIMUM TOKEN COVERAGE - Expanded to 450+ meme tokens

                    # Ethereum Memes (150+ tokens)
                    "DOGE", "SHIB", "PEPE", "FLOKI", "ELON", "AKITA", "HOGE", "DOGELON", "CATGIRL", "WOJAK", "LADYS", "TURBO", "AIDOGE", "BABYDOGE", "KISHU",
                    "MEME", "PEPE2", "WOJAK", "BOBO", "NORMIE", "GIGA", "CHAD", "VIRGIN", "COPE", "HOPIUM", "WAGMI", "NGMI", "DIAMOND", "PAPER",
                    "MOON", "LAMBO", "REKT", "PUMP", "DUMP", "HODL", "FOMO", "FUD", "DYOR", "NFA", "SAFU", "BUIDL", "DEFI", "CEFI", "TRADFI",
                    # v3.0 NEW: Additional Ethereum Memes
                    "SAITAMA", "LUFFY", "GOKU", "NARUTO", "PIKACHU", "CHARIZARD", "BLASTOISE", "VENUSAUR", "MEWTWO", "MEW", "CELEBI", "JIRACHI",
                    "ARCEUS", "DIALGA", "PALKIA", "GIRATINA", "RESHIRAM", "ZEKROM", "KYUREM", "XERNEAS", "YVELTAL", "ZYGARDE", "SOLGALEO", "LUNALA",
                    "NECROZMA", "MAGEARNA", "MARSHADOW", "ZERAORA", "MELTAN", "MELMETAL", "GROOKEY", "SCORBUNNY", "SOBBLE", "CORVIKNIGHT", "DRAGAPULT",
                    "GRIMMSNARL", "ALCREMIE", "TOXAPEX", "MIMIKYU", "BEWEAR", "TSAREENA", "GOLISOPOD", "PALOSSAND", "PYUKUMUKU", "MINIOR", "KOMALA",
                    "TURTONATOR", "TOGEDEMARU", "DRAMPA", "DHELMISE", "JANGMO", "HAKAMO", "KOMMO", "TAPU", "COSMOG", "COSMOEM", "KARTANA", "CELESTEELA",
                    "GUZZLORD", "POIPOLE", "NAGANADEL", "STAKATAKA", "BLACEPHALON", "ZERAORA", "MELTAN", "MELMETAL", "RILLABOOM", "CINDERACE", "INTELEON",
                    "CORVIKNIGHT", "ORBEETLE", "THIEVUL", "ELDEGOSS", "WOOLOO", "DUBWOOL", "CHEWTLE", "DREDNAW", "ROLYCOLY", "CARKOL", "COALOSSAL",
                    "APPLIN", "FLAPPLE", "APPLETUN", "SILICOBRA", "SANDACONDA", "CRAMORANT", "ARROKUDA", "BARRASKEWDA", "TOXEL", "TOXTRICITY",
                    "SIZZLIPEDE", "CENTISKORCH", "CLOBBOPUS", "GRAPPLOCT", "SINISTEA", "POLTEAGEIST", "HATENNA", "HATTREM", "HATTERENE", "IMPIDIMP",
                    "MORGREM", "GRIMMSNARL", "OBSTAGOON", "PERRSERKER", "CURSOLA", "SIRFETCH", "RUNERIGUS", "MILCERY", "ALCREMIE", "FALINKS", "PINCURCHIN",
                    "SNOM", "FROSMOTH", "STONJOURNER", "EISCUE", "INDEEDEE", "MORPEKO", "CUFANT", "COPPERAJAH", "DRACOZOLT", "ARCTOZOLT", "DRACOVISH",
                    "ARCTOVISH", "DURALUDON", "DREEPY", "DRAKLOAK", "DRAGAPULT", "ZACIAN", "ZAMAZENTA", "ETERNATUS", "KUBFU", "URSHIFU", "REGIELEKI",
                    "REGIDRAGO", "GLASTRIER", "SPECTRIER", "CALYREX", "WYRDEER", "KLEAVOR", "URSALUNA", "BASCULEGION", "SNEASLER", "OVERQWIL",
                    "ENAMORUS", "SPRIGATITO", "FLORAGATO", "MEOWSCARADA", "FUECOCO", "CROCALOR", "SKELEDIRGE", "QUAXLY", "QUAXWELL", "QUAQUAVAL",

                    # BSC Memes (100+ tokens)
                    "SAFEMOON", "ELONGATE", "HOKK", "FOGE", "CORGI", "PITBULL", "DOGE2", "SHIBAINU", "FLOKI2", "BABYDOGE", "KISHU", "ELON",
                    "BABYCAKE", "SAFEMARS", "MOONSHOT", "ROCKET", "DIAMOND", "PAPER", "MOON", "LAMBO", "REKT", "PUMP", "DUMP", "HODL",
                    "FOMO", "FUD", "DYOR", "NFA", "SAFU", "BUIDL", "DEFI", "CEFI", "TRADFI", "YIELD", "FARM", "STAKE", "POOL", "VAULT",
                    # v3.0 NEW: Additional BSC Memes
                    "BABYDOGE", "MINIDOGE", "SUPERDOGE", "MEGADOGE", "ULTRADOGE", "HYPERDOGE", "GIGADOGE", "TERADOGE", "PETADOGE", "EXADOGE",
                    "ZETTADOGE", "YOTTADOGE", "INFINITYDOGE", "ETERNALDOGE", "IMMORTALDOGE", "LEGENDDOGE", "MYTHICDOGE", "EPICDOGE", "RAREDOGE", "COMMONDOGE",
                    "SHIBAFLOKI", "SHIBAELON", "SHIBAINU2", "SHIBAINU3", "SHIBAINU4", "SHIBAINU5", "SHIBATOKEN", "SHIBACOIN", "SHIBAMOON", "SHIBAMARS",
                    "SHIBASUN", "SHIBASTAR", "SHIBAGALAXY", "SHIBAUNIVERSE", "SHIBACOSMOS", "SHIBAMULTIVERSE", "SHIBADIMENSION", "SHIBAPARALLEL", "SHIBAALTERNATE", "SHIBAREALITY",
                    "FLOKIINU", "FLOKIMOON", "FLOKIMARS", "FLOKISUN", "FLOKISTAR", "FLOKIGALAXY", "FLOKIUNIVERSE", "FLOKICOSMOS", "FLOKIMULTIVERSE", "FLOKIDIMENSION",
                    "ELONMUSK", "ELONMOON", "ELONMARS", "ELONSUN", "ELONSTAR", "ELONGALAXY", "ELONUNIVERSE", "ELONCOSMOS", "ELONMULTIVERSE", "ELONDIMENSION",
                    "SAFEMOON2", "SAFEMOON3", "SAFEMOON4", "SAFEMOON5", "SAFEMOONV2", "SAFEMOONV3", "SAFEMOONV4", "SAFEMOONV5", "SAFEMOONEVO", "SAFEMOONMAX",

                    # Solana Memes (200+ tokens) - MAXIMUM SOLANA COVERAGE
                    "BONK", "WIF", "POPCAT", "MYRO", "BOME", "SLERF", "BOOK", "MEW", "MOTHER", "DADDY", "TREMP", "JENNA", "HARAMBE",
                    "WOJAK", "PONKE", "RETARDIO", "HOBBES", "MICHI", "GIGA", "NORMIE", "BOBO", "PEPU", "TURBO", "LADYS", "RIBBIT",
                    "MAGA", "TRUMP", "BIDEN", "ELON", "SOLDOG", "SOLCAT", "SOLMOON", "SOLSUN", "SOLFIRE", "SOLICE", "SOLWIND", "SOLROCK",
                    # v3.0 NEW: Comprehensive Solana Meme Expansion
                    "SOLWATER", "SOLGRASS", "SOLELECTRIC", "SOLPSYCHIC", "SOLFIGHTING", "SOLPOISON", "SOLGROUND", "SOLFLYING", "SOLBUG", "SOLGHOST",
                    "SOLSTEEL", "SOLDRAGON", "SOLFAIRY", "SOLDARK", "SOLNORMAL", "SOLPUNK", "SOLCYBER", "SOLNEON", "SOLRETRO", "SOLVINTAGE",
                    "SOLCLASSIC", "SOLMODERN", "SOLFUTURE", "SOLPAST", "SOLPRESENT", "SOLTIMELESS", "SOLETERNAL", "SOLIMMORTAL", "SOLLEGEND", "SOLMYTHIC",
                    "SOLEPIC", "SOLRARE", "SOLCOMMON", "SOLUNCOMMON", "SOLLEGENDARY", "SOLMYTHICAL", "SOLSHINY", "SOLHOLOGRAPHIC", "SOLFOIL", "SOLPRISM",
                    "SOLRAINBOW", "SOLCHROME", "SOLGOLD", "SOLSILVER", "SOLBRONZE", "SOLPLATINUM", "SOLDIAMOND", "SOLCRYSTAL", "SOLGEM", "SOLJEWEL",
                    "SOLPEARL", "SOLRUBY", "SOLSAPPHIRE", "SOLEMERALD", "SOLTOPAZ", "SOLAMETHYST", "SOLGARNET", "SOLPERIDOT", "SOLAQUAMARINE", "SOLCITRINE",
                    "SOLTOURMALINE", "SOLZIRCON", "SOLJADE", "SOLONYX", "SOLOPAL", "SOLQUARTZ", "SOLFELDSPAR", "SOLMICA", "SOLGYPSUM", "SOLCALCITE",
                    "SOLDOLOMITE", "SOLMAGNESITE", "SOLSIDERITE", "SOLRHODOCHROSITE", "SOLSMITHSONITE", "SOLCERUSSITE", "SOLMALACHITE", "SOLAZURITE", "SOLCHRYSOCOLLA", "SOLTURQUOISE",
                    "SOLLAPIS", "SOLSODALITE", "SOLHAUYNE", "SOLNOSEAN", "SOLCANCRINITE", "SOLNEPHELINE", "SOLLEUCOCITE", "SOLANALCIME", "SOLNATROLITE", "SOLMESOLITE",
                    "SOLSCOLECITE", "SOLSTILBITE", "SOLHEULANDITE", "SOLCLINOPTILOLITE", "SOLMORDENITE", "SOLFERRIERITE", "SOLBIKITAITE", "SOLMARIALITE", "SOLMEIONITE", "SOLTUGTUPITE",
                    "SOLCHKALOVITE", "SOLBAZZITE", "SOLBERYL", "SOLCHRYSBERYL", "SOLALEXANDRITE", "SOLCATSEYE", "SOLMOONSTONE", "SOLSUNSTONE", "SOLLABRADORITE", "SOLANDESINE",
                    "SOLBYTOWNITE", "SOLANORTHITE", "SOLALBITE", "SOLOLIGOCLASE", "SOLANDESINE", "SOLLABRADORITE", "SOLBYTOWNITE", "SOLANORTHITE", "SOLMICROCLINE", "SOLORTHOCLASE",
                    "SOLSANIDINE", "SOLANORTHOCLASE", "SOLCELSIAN", "SOLHYALOPHANE", "SOLPARACELSIAN", "SOLREEDMERGNERITE", "SOLKOKCHETAVITE", "SOLSLAWSONITE", "SOLSTRONALSITE", "SOLBANANALSITE"
                ],
                "priority": 5,
                "profit_threshold": {"min": 10.0, "max": 50.0},
                "tier": 3,
                "description": "v3.0 Enhanced: Comprehensive meme token coverage (450+ tokens)",
                "blockchain_distribution": {"ethereum": 150, "bsc": 100, "solana": 200}
            },
            "gaming_nft": {
                "name": "Gaming & NFT",
                "tokens": [
                    # Ethereum Gaming & NFT (60+ tokens)
                    "AXS", "SAND", "MANA", "ENJ", "GALA", "ILV", "SLP", "ALICE", "TLM", "WIN", "CHR", "PYR", "GHST", "NFTX", "RARI", "SUPER", "UFO",
                    "APE", "BAYC", "MAYC", "OTHR", "LOOKS", "X2Y2", "SUDO", "BLUR", "BEND", "PARA", "JPEG", "PUNK", "DOODLES", "AZUKI", "CLONE",
                    "MOONBIRDS", "PROOF", "YGG", "GUILD", "MERIT", "MC", "NAKA", "STARL", "BLOK", "RFOX", "WILD", "REALM", "RACA", "FLOKI",
                    "GODS", "IMX", "REVV", "TOWER", "SKILL", "HERO", "DFK", "JEWEL", "CRYSTAL", "DFKTEARS", "JADE", "ETERNAL", "FROYO", "WARS",

                    # BSC Gaming (20+ tokens)
                    "SKILL", "HERO", "DFK", "JEWEL", "CRYSTAL", "DFKTEARS", "JADE", "MOBOX", "MBOX", "CHESS", "BNX", "RACA", "ETERNAL", "FROYO",
                    "WARS", "DPET", "CROX", "BABYCAKE", "SAFEMOON", "ELONGATE",

                    # Polygon Gaming (15+ tokens)
                    "REVV", "TOWER", "SAND", "MANA", "GHST", "AAVEGOTCHI", "QUICK", "DQUICK", "WMATIC", "USDC", "USDT", "DAI", "WETH", "WBTC", "SUSHI"
                ],
                "priority": 6,
                "profit_threshold": {"min": 8.0, "max": 20.0},
                "tier": 2,
                "blockchain_distribution": {"ethereum": 60, "bsc": 20, "polygon": 15}
            },
            "ai_big_data": {
                "name": "AI & Big Data",
                "tokens": [
                    # AI & Machine Learning
                    "FET", "AGIX", "OCEAN", "NMR", "GRT", "RLC", "CTXC", "DBC", "MATRIX", "AGI", "COTI", "VIDT", "API3", "BAND", "LINK",
                    # Data & Storage
                    "AR", "FIL", "STORJ", "SC", "SIA", "BTT", "HOT", "IOST", "ANKR", "POKT", "FLUX", "RNDR", "LIVEPEER", "LPT",
                    # Oracle & Infrastructure
                    "LINK", "BAND", "API3", "UMA", "DIA", "NEST", "TRB", "FLUX", "UMBRELLA", "RAZOR", "SUPEROACLE", "PYTH", "SWITCHBOARD"
                ],
                "priority": 7,
                "profit_threshold": {"min": 5.0, "max": 12.0},
                "tier": 2,
                "blockchain_distribution": {"ethereum": 25, "bsc": 8, "polygon": 5, "solana": 5}
            },
            "bsc_ecosystem": {
                "name": "BSC Ecosystem",
                "tokens": [
                    # PancakeSwap Ecosystem (50+ tokens)
                    "CAKE", "BAKE", "AUTO", "BELT", "BUNNY", "EPS", "XVS", "VAI", "SXP", "TWT", "ALPACA", "MDX", "BURGER", "WATCH", "SWINGBY",
                    "HARD", "KAVA", "SWP", "USDX", "BNX", "CHESS", "QBT", "DEGO", "FOR", "ANY", "BTCB", "ETH", "ADA", "DOT", "LINK",

                    # BSC DeFi Protocols (40+ tokens)
                    "VENUS", "XVS", "VAI", "ALPACA", "BELT", "BEEFY", "AUTO", "BUNNY", "EPS", "WATCH", "SWINGBY", "HARD", "KAVA", "SWP", "USDX",
                    "BNX", "CHESS", "QBT", "DEGO", "FOR", "ANY", "CREAM", "BADGER", "CVX", "FXS", "LDO", "RPL", "ALCX", "SPELL", "ICE",

                    # BSC Gaming & NFT (30+ tokens)
                    "SKILL", "HERO", "DFK", "JEWEL", "CRYSTAL", "DFKTEARS", "JADE", "MOBOX", "MBOX", "CHESS", "BNX", "RACA", "ETERNAL", "FROYO",
                    "WARS", "DPET", "CROX", "BABYCAKE", "SAFEMOON", "ELONGATE", "HOKK", "FOGE", "CORGI", "PITBULL", "DOGE2", "SHIBAINU", "FLOKI2", "BABYDOGE", "KISHU", "ELON",

                    # BSC Infrastructure (20+ tokens)
                    "BNB", "WBNB", "BUSD", "USDT", "USDC", "DAI", "BTCB", "ETH", "ADA", "DOT", "LINK", "UNI", "SUSHI", "AAVE", "COMP", "YFI", "CRV", "BAL", "1INCH", "SNX"
                ],
                "priority": 8,
                "profit_threshold": {"min": 3.0, "max": 8.0},
                "tier": 2,
                "blockchain_distribution": {"bsc": 140}
            },
            "solana_ecosystem": {
                "name": "Solana Ecosystem",
                "tokens": [
                    # Core Solana
                    "SOL", "WSOL",

                    # Major DeFi Protocols (60+ tokens)
                    "RAY", "ORCA", "SRM", "FIDA", "COPE", "STEP", "TULIP", "SABER", "MERCURIAL", "PORT",
                    "MNGO", "DRIFT", "ZETA", "HXRO", "CYPHER", "PERP", "FRIKTION", "KATANA", "SOLEND",
                    "LARIX", "APRICOT", "FRANCIUM", "PARROT", "OXYGEN", "BONFIDA", "ALDRIN", "CROPPER",
                    "SUNNY", "QUARRY", "CASHIO", "UXD", "HUBBLE", "RATIO", "SYNTHETIFY", "CYCLOS",
                    # v3.0 NEW: Emerging Solana DeFi
                    "JITO", "PYTH", "RENDER", "JUP", "TNSR", "KAMINO", "MARGINFI", "SOLBLAZE", "SANCTUM", "FLASH",
                    "METEORA", "LIFINITY", "SAROS", "INVARIANT", "PHOENIX", "OPENBOOK", "WHIRLPOOL", "HAWKSIGHT", "SYMMETRY", "SOLRISE",

                    # Liquid Staking & Validators
                    "stSOL", "mSOL", "jitoSOL", "JSOL", "BSOL", "scnSOL", "daoSOL", "LST", "MSOL",
                    "vSOL", "pSOL", "eSOL", "hSOL", "SOCN", "BLZE", "MNDE", "JITO", "MARINADE",

                    # Gaming & Metaverse
                    "ATLAS", "POLIS", "GOFX", "AURORY", "GENOPETS", "NYAN", "SOLCHICKS", "DEFI",
                    "CWAR", "SOLR", "NINJA", "GRAPE", "CHEEMS", "SOLAPE", "DEGEN", "SAMO", "SLND",
                    "MEAN", "MEDIA", "SOLX", "SOLPAD", "SOLCASINO", "SOLYARD", "SOLANA", "SOLRISE",

                    # Meme Tokens (60+ tokens)
                    "BONK", "WIF", "POPCAT", "MYRO", "BOME", "SLERF", "BOOK", "MEW", "MOTHER",
                    "DADDY", "TREMP", "JENNA", "HARAMBE", "PEPE", "SHIB", "DOGE", "FLOKI", "BABYDOGE",
                    "WOJAK", "PONKE", "RETARDIO", "HOBBES", "MICHI", "GIGA", "NORMIE", "BOBO",
                    "PEPU", "TURBO", "LADYS", "RIBBIT", "MAGA", "TRUMP", "BIDEN", "ELON",
                    # v3.0 NEW: Additional Solana Memes
                    "SOLDOG", "SOLCAT", "SOLMOON", "SOLSUN", "SOLFIRE", "SOLICE", "SOLWIND", "SOLROCK",
                    "SOLWATER", "SOLGRASS", "SOLELECTRIC", "SOLPSYCHIC", "SOLFIGHTING", "SOLPOISON", "SOLGROUND", "SOLFLYING",
                    "SOLBUG", "SOLGHOST", "SOLSTEEL", "SOLDRAGON", "SOLFAIRY", "SOLDARK", "SOLNORMAL", "SOLPUNK",

                    # Infrastructure & Tools (40+ tokens)
                    "PYTH", "RENDER", "HELIUM", "HNT", "MOBILE", "IOT", "HONEY", "HIVE", "FORGE",
                    "SERUM", "RAYDIUM", "JUPITER", "METEORA", "LIFINITY", "DEXLAB", "SAROS",
                    "INVARIANT", "PHOENIX", "OPENBOOK", "WHIRLPOOL", "HAWKSIGHT", "SYMMETRY",
                    # v3.0 NEW: Additional Infrastructure
                    "SOLSCAN", "SOLFLARE", "PHANTOM", "SLOPE", "SOLLET", "SOLANART", "MAGICEDEN",
                    "DIGITALEYES", "SOLSEA", "ALPHA", "HYPERSPACE", "TENSOR", "CORAL", "HADESWAP",
                    "CLOCKWORK", "GOKI", "TRIBECA", "GOVERN", "REALMS", "SQUADS", "MULTISIG", "CARDINAL",

                    # NFT & Creator Economy
                    "DUST", "FORGE", "HONEY", "HIVE", "GRAPE", "SOLAPE", "NYAN", "CHEEMS", "SAMO",
                    "SOLCHICKS", "DEFI", "CWAR", "SOLR", "NINJA", "DEGEN", "SLND", "MEAN", "MEDIA",

                    # Cross-chain & Bridges
                    "WORMHOLE", "ALLBRIDGE", "PORTAL", "SOLLET", "WRAPPED", "WETH", "WBTC", "WUSDC",
                    "WUSDT", "WMATIC", "WAVAX", "WBNB", "WFTM", "WONE", "WMOVR", "WGLMR",

                    # Emerging DeFi
                    "HAWKSIGHT", "SYMMETRY", "SOLRISE", "SOLPAD", "SOLCASINO", "SOLYARD", "SOLX",
                    "SOLEND", "LARIX", "APRICOT", "FRANCIUM", "PARROT", "OXYGEN", "BONFIDA",

                    # Yield Farming & Aggregators
                    "TULIP", "SABER", "MERCURIAL", "PORT", "SUNNY", "QUARRY", "CASHIO", "UXD",
                    "HUBBLE", "RATIO", "SYNTHETIFY", "CYCLOS", "FRIKTION", "KATANA", "SOLEND",

                    # Prediction Markets & Derivatives
                    "HXRO", "CYPHER", "PERP", "DRIFT", "ZETA", "FRIKTION", "KATANA", "SOLEND",
                    "LARIX", "APRICOT", "FRANCIUM", "PARROT", "OXYGEN", "BONFIDA", "ALDRIN",

                    # Community & Social
                    "GRAPE", "SOLAPE", "NYAN", "CHEEMS", "SAMO", "SOLCHICKS", "DEFI", "CWAR",
                    "SOLR", "NINJA", "DEGEN", "SLND", "MEAN", "MEDIA", "SOLX", "SOLPAD",

                    # Additional Popular Tokens
                    "ROPE", "LIKE", "MAPS", "GST", "GMT", "STEP", "COPE", "FIDA", "SRM",
                    "ORCA", "MNGO", "TULIP", "SUNNY", "SABER", "PORT", "ATLAS", "POLIS",

                    # New & Trending
                    "JITO", "MARINADE", "BLZE", "MNDE", "SOCN", "LST", "MSOL", "vSOL", "pSOL",
                    "eSOL", "hSOL", "scnSOL", "daoSOL", "HAWKSIGHT", "SYMMETRY", "SOLRISE"
                ],
                "priority": 8,
                "profit_threshold": {"min": 3.0, "max": 15.0},
                "tier": 1,
                "description": "v3.0 Enhanced: Comprehensive Solana ecosystem tokens (250+ tokens)",
                "color": "#9945FF",
                "blockchain_distribution": {"solana": 250},
                "subcategories": {
                    "defi": ["RAY", "ORCA", "SRM", "FIDA", "COPE", "STEP", "TULIP", "SABER", "MERCURIAL", "PORT", "MNGO", "DRIFT", "ZETA", "HXRO", "CYPHER", "JITO", "PYTH", "RENDER", "JUP", "TNSR", "KAMINO"],
                    "gaming": ["ATLAS", "POLIS", "GOFX", "AURORY", "GENOPETS", "NYAN", "SOLCHICKS", "DEFI", "CWAR", "SOLR", "NINJA", "GRAPE", "CHEEMS", "SOLAPE", "DEGEN", "SAMO"],
                    "meme": ["BONK", "WIF", "POPCAT", "MYRO", "BOME", "SLERF", "BOOK", "MEW", "MOTHER", "DADDY", "TREMP", "JENNA", "HARAMBE", "WOJAK", "PONKE", "RETARDIO", "HOBBES", "MICHI"],
                    "infrastructure": ["PYTH", "RENDER", "HELIUM", "HNT", "MOBILE", "IOT", "HONEY", "HIVE", "FORGE", "SERUM", "RAYDIUM", "JUPITER", "METEORA", "LIFINITY", "SOLSCAN", "SOLFLARE", "PHANTOM"],
                    "liquid_staking": ["stSOL", "mSOL", "jitoSOL", "JSOL", "BSOL", "scnSOL", "daoSOL", "LST", "MSOL", "vSOL", "pSOL", "eSOL", "hSOL", "SOCN", "BLZE", "MNDE", "JITO", "MARINADE"]
                },

                # ISSUE 3: NEW CATEGORIES FOR 1000+ TOKEN COVERAGE
                "polygon_ecosystem": {
                    "name": "Polygon Ecosystem",
                    "tokens": [
                        # Core Polygon
                        "MATIC", "WMATIC", "QUICK", "DQUICK", "GHST", "AAVEGOTCHI", "REVV", "TOWER", "SAND", "MANA",

                        # Polygon DeFi (50+ tokens)
                        "USDC", "USDT", "DAI", "WETH", "WBTC", "SUSHI", "CRV", "BAL", "1INCH", "COMP", "AAVE", "UNI", "LINK", "YFI", "SNX", "MKR",
                        "FRAX", "FXS", "CVX", "LDO", "RPL", "ALCX", "SPELL", "ICE", "DYDX", "PERP", "DODO", "ZRX", "KNC", "BNT", "REN", "KEEP",
                        "NU", "ANKR", "BAND", "OCEAN", "FETCH", "NMR", "MLN", "REP", "AUGUR", "GNO", "COW", "SAFE", "ENS", "LPT", "API3", "UMA",

                        # Polygon Gaming & NFT
                        "REVV", "TOWER", "SAND", "MANA", "GHST", "AAVEGOTCHI", "SKILL", "HERO", "DFK", "JEWEL", "CRYSTAL", "DFKTEARS", "JADE",

                        # Polygon Infrastructure
                        "MATIC", "QUICK", "DQUICK", "WMATIC", "USDC", "USDT", "DAI", "WETH", "WBTC", "SUSHI", "CRV", "BAL", "1INCH", "COMP"
                    ],
                    "priority": 9,
                    "profit_threshold": {"min": 2.0, "max": 8.0},
                    "tier": 2,
                    "description": "Comprehensive Polygon ecosystem tokens (80+ tokens)",
                    "blockchain_distribution": {"polygon": 80}
                },

                "arbitrum_ecosystem": {
                    "name": "Arbitrum Ecosystem",
                    "tokens": [
                        # Core Arbitrum
                        "ARB", "GMX", "GLP", "MAGIC", "RDNT", "VELA", "GRAIL", "PLS", "JONES", "DPX", "UMAMI", "SPERAX", "SPA", "USDS", "ARBI", "PENDLE",

                        # Arbitrum DeFi (60+ tokens)
                        "WETH", "USDC", "USDT", "DAI", "WBTC", "LINK", "UNI", "AAVE", "CRV", "BAL", "SUSHI", "1INCH", "COMP", "YFI", "SNX", "MKR",
                        "FRAX", "FXS", "CVX", "LDO", "RPL", "ALCX", "SPELL", "ICE", "DYDX", "PERP", "DODO", "ZRX", "KNC", "BNT", "REN", "KEEP",
                        "NU", "ANKR", "BAND", "OCEAN", "FETCH", "NMR", "MLN", "REP", "AUGUR", "GNO", "COW", "SAFE", "ENS", "LPT", "API3", "UMA",
                        "BOND", "BARN", "POOL", "IDLE", "PICKLE", "FARM", "HARVEST", "CREAM", "BADGER", "ALPHA", "RUNE", "TORN", "INST", "LOOKS",

                        # Arbitrum Gaming & NFT
                        "MAGIC", "TREASURE", "SMOL", "LEGION", "SEED", "GFLY", "BATTLEFLY", "REALM", "BRIDGEWORLD", "TALES", "BEACON", "LOST",

                        # Arbitrum Infrastructure
                        "ARB", "GMX", "GLP", "RDNT", "VELA", "GRAIL", "PLS", "JONES", "DPX", "UMAMI", "SPERAX", "SPA", "USDS", "ARBI", "PENDLE"
                    ],
                    "priority": 10,
                    "profit_threshold": {"min": 2.0, "max": 8.0},
                    "tier": 2,
                    "description": "Comprehensive Arbitrum ecosystem tokens (90+ tokens)",
                    "blockchain_distribution": {"arbitrum": 90}
                },

                "emerging_altcoins": {
                    "name": "Emerging Altcoins",
                    "tokens": [
                        # New Layer 1s & 2s (50+ tokens)
                        "SUI", "APT", "SEI", "INJ", "TIA", "DYDX", "BLUR", "PENDLE", "JTO", "PYTH", "WLD", "STRK", "MANTA", "ALT", "JUP", "WEN",
                        "TNSR", "BOME", "ENA", "ETHFI", "REZ", "NOTCOIN", "OMNI", "SAGA", "TAO", "PEPE", "FLOKI", "BONK", "WIF", "BRETT",
                        "POPCAT", "MEW", "MOTHER", "DADDY", "TREMP", "JENNA", "HARAMBE", "WOJAK", "PONKE", "RETARDIO", "HOBBES", "MICHI",
                        "GIGA", "NORMIE", "BOBO", "PEPU", "TURBO", "LADYS", "RIBBIT", "MAGA", "TRUMP", "BIDEN", "ELON", "DOGE", "SHIB",

                        # RWA & Tokenization (30+ tokens)
                        "ONDO", "RWA", "POLYX", "PROPS", "RIO", "REALIO", "ASSET", "TOKEN", "SECURITY", "EQUITY", "BOND", "COMMODITY", "REAL",
                        "ESTATE", "GOLD", "SILVER", "OIL", "GAS", "CARBON", "CREDIT", "OFFSET", "GREEN", "ESG", "SUSTAINABLE", "RENEWABLE",
                        "ENERGY", "SOLAR", "WIND", "HYDRO", "NUCLEAR", "FUSION", "BATTERY", "STORAGE", "GRID", "SMART", "METER", "IOT",

                        # AI & Machine Learning (40+ tokens)
                        "FET", "AGIX", "OCEAN", "NMR", "GRT", "RLC", "CTXC", "DBC", "MATRIX", "AGI", "COTI", "VIDT", "API3", "BAND", "LINK",
                        "AR", "FIL", "STORJ", "SC", "SIA", "BTT", "HOT", "IOST", "ANKR", "POKT", "FLUX", "RNDR", "LIVEPEER", "LPT",
                        "THETA", "TFUEL", "VID", "STREAM", "MEDIA", "CONTENT", "CREATOR", "SOCIAL", "NETWORK", "PLATFORM", "COMMUNITY", "DAO"
                    ],
                    "priority": 11,
                    "profit_threshold": {"min": 5.0, "max": 25.0},
                    "tier": 3,
                    "description": "Emerging and trending altcoins (120+ tokens)",
                    "blockchain_distribution": {"ethereum": 50, "solana": 30, "bsc": 20, "polygon": 10, "arbitrum": 10}
                }
            }
        }

        # Extended chain support
        self.supported_chains = {
            "ethereum": {"name": "Ethereum", "symbol": "ETH", "priority": 1},
            "bsc": {"name": "Binance Smart Chain", "symbol": "BNB", "priority": 2},
            "polygon": {"name": "Polygon", "symbol": "MATIC", "priority": 3},
            "arbitrum": {"name": "Arbitrum", "symbol": "ARB", "priority": 4},
            "optimism": {"name": "Optimism", "symbol": "OP", "priority": 5},
            "avalanche": {"name": "Avalanche", "symbol": "AVAX", "priority": 6},
            "fantom": {"name": "Fantom", "symbol": "FTM", "priority": 7},
            "solana": {"name": "Solana", "symbol": "SOL", "priority": 8},
            "base": {"name": "Base", "symbol": "ETH", "priority": 9},
            "cronos": {"name": "Cronos", "symbol": "CRO", "priority": 10}
        }
        # Default configuration
        self.config = {
            "profit_min": 0.05,
            "profit_max": 25.0,
            "min_liquidity": 5000,
            "min_volume_24h": 500,
            "enabled_chains": ["ethereum", "bsc", "polygon", "arbitrum", "solana"],
            "auto_scan_interval": 0,  # 0 = manual
            # Token selection configuration
            "enabled_token_categories": ["stablecoins", "blue_chips", "defi", "layer1_2", "solana_ecosystem", "meme_coins", "gaming_nft", "ai_big_data", "bsc_ecosystem", "polygon_ecosystem", "arbitrum_ecosystem", "emerging_altcoins"],
            "max_tokens_per_category": 100,  # Increased for 1000+ token coverage
            "prioritize_by_volume": True,
            # Trading simulation configuration
            "simulation_capital": 100,  # USD
            "max_slippage": 0.5,  # percentage
            "capital_per_trade": 100,  # percentage of total capital
            "min_liquidity_ratio": 10,  # liquidity must be 10x capital
            "risk_tolerance": "medium"  # low, medium, high
        }

        # v3.0 NEW: Enhanced components for 1000+ token analysis (initialized after token_categories)
        self.dynamic_discovery = DynamicTokenDiscovery(self.smart_cache)
        self.profit_threshold_manager = DynamicProfitThresholdManager()
        self.tiered_scanner = TieredScanningManager(self.token_categories, self.smart_cache)

    def add_log(self, message: str, level: str = "info"):
        """Add log entry with timestamp"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "level": level
        }
        self.scan_logs.append(log_entry)
        # Keep only last 50 logs
        if len(self.scan_logs) > 50:
            self.scan_logs = self.scan_logs[-50:]
        logger.info(f"[{level.upper()}] {message}")

    def update_config(self, new_config: dict):
        """Update scanning configuration"""
        self.config.update(new_config)
        self.add_log(f"⚙️ Konfigurasi diperbarui: {new_config}", "info")

    def get_enabled_tokens(self) -> List[str]:
        """Get list of enabled tokens based on configuration"""
        enabled_tokens = []

        for category in self.config["enabled_token_categories"]:
            if category in self.token_categories:
                category_tokens = self.token_categories[category]["tokens"]
                max_tokens = self.config["max_tokens_per_category"]

                # Take up to max_tokens from each category
                enabled_tokens.extend(category_tokens[:max_tokens])

        return list(set(enabled_tokens))

    async def get_enabled_tokens_expanded(self) -> List[str]:
        """v3.0 ENHANCED: Get expanded list of 1000+ tokens with intelligent tier-based distribution"""
        enabled_tokens = []

        try:
            # Initialize tiered scanning if not done
            if not self.tiered_scanner.tier_assignments[1]:
                self.tiered_scanner.initialize_tiers()

            # PRIORITY 1: Get tokens from all tiers
            for tier in [1, 2, 3]:
                tier_tokens = self.tiered_scanner.get_tokens_for_tier(tier)
                enabled_tokens.extend(tier_tokens)
                tier_name = self.tiered_scanner.tier_definitions[tier]['name']
                self.add_log(f"🎯 {tier_name}: Added {len(tier_tokens)} tokens", "info")

            # PRIORITY 2: Add discovered tokens if enabled
            if self.v3_config.get('enable_dynamic_discovery', True):
                discovered_tokens = await self.dynamic_discovery.discover_trending_tokens()

                for chain, tokens in discovered_tokens.items():
                    enabled_tokens.extend(tokens[:20])  # Top 20 per chain
                    if tokens:
                        self.add_log(f"🔍 Discovery ({chain}): Added {len(tokens[:20])} trending tokens", "info")

            # PRIORITY 3: Ensure comprehensive blockchain distribution
            blockchain_targets = {
                'ethereum': 300,
                'solana': 250,
                'bsc': 200,
                'polygon': 150,
                'arbitrum': 100
            }

            current_distribution = self._analyze_token_distribution(enabled_tokens)

            # Add additional tokens to meet targets
            for blockchain, target_count in blockchain_targets.items():
                current_count = current_distribution.get(blockchain, 0)
                if current_count < target_count:
                    additional_needed = target_count - current_count
                    additional_tokens = await self._get_additional_tokens_for_blockchain(blockchain, additional_needed)
                    enabled_tokens.extend(additional_tokens)

                    if additional_tokens:
                        self.add_log(f"📈 {blockchain.title()}: Added {len(additional_tokens)} additional tokens", "info")

            # Remove duplicates and enforce 1000+ token limit
            unique_tokens = list(dict.fromkeys(enabled_tokens))  # Preserve order while removing duplicates

            # Final distribution analysis
            final_distribution = self._analyze_token_distribution(unique_tokens)
            total_count = len(unique_tokens)

            # Log comprehensive distribution
            self.add_log(f"📊 v3.0 Token Distribution: {total_count} total tokens", "info")
            for blockchain, count in final_distribution.items():
                percentage = (count / total_count * 100) if total_count > 0 else 0
                self.add_log(f"   {blockchain.title()}: {count} tokens ({percentage:.1f}%)", "info")

            # Return up to 1000 tokens with intelligent prioritization
            return unique_tokens[:self.v3_config.get('max_total_tokens', 1000)]

        except Exception as e:
            logger.error(f"Enhanced token expansion error: {e}")
            # Fallback to basic expansion
            return await self._fallback_token_expansion()

    def get_solana_token_subcategories(self) -> Dict[str, List[str]]:
        """Get Solana tokens organized by subcategories for targeted scanning"""
        if "solana_ecosystem" not in self.token_categories:
            return {}

        return self.token_categories["solana_ecosystem"].get("subcategories", {})

    def get_priority_solana_tokens(self) -> List[str]:
        """Get high-priority Solana tokens for initial scanning"""
        priority_tokens = [
            # Core & Most Liquid
            "SOL", "WSOL", "RAY", "ORCA", "SRM", "FIDA", "MNGO",
            # Top Memes
            "BONK", "WIF", "POPCAT", "MYRO", "BOME", "MEW",
            # Major DeFi
            "TULIP", "SABER", "PORT", "SUNNY", "DRIFT", "ZETA",
            # Liquid Staking
            "stSOL", "mSOL", "jitoSOL", "JSOL", "BSOL",
            # Gaming
            "ATLAS", "POLIS", "AURORY", "GENOPETS",
            # Infrastructure
            "PYTH", "RENDER", "HELIUM", "HNT"
        ]

        return priority_tokens

    def _analyze_token_distribution(self, tokens: List[str]) -> Dict[str, int]:
        """Analyze token distribution across blockchains"""
        distribution = {'ethereum': 0, 'solana': 0, 'bsc': 0, 'polygon': 0, 'arbitrum': 0, 'other': 0}

        try:
            for token in tokens:
                # Determine blockchain based on token categories
                blockchain = self._determine_token_blockchain(token)
                if blockchain in distribution:
                    distribution[blockchain] += 1
                else:
                    distribution['other'] += 1

            return distribution

        except Exception as e:
            logger.error(f"Token distribution analysis error: {e}")
            return distribution

    def _determine_token_blockchain(self, token: str) -> str:
        """Determine primary blockchain for a token"""
        try:
            # Check Solana ecosystem first
            if 'solana_ecosystem' in self.token_categories:
                if token in self.token_categories['solana_ecosystem']['tokens']:
                    return 'solana'

            # Check BSC ecosystem
            if 'bsc_ecosystem' in self.token_categories:
                if token in self.token_categories['bsc_ecosystem']['tokens']:
                    return 'bsc'

            # Default mapping for common tokens
            ethereum_tokens = ['WETH', 'WBTC', 'UNI', 'AAVE', 'COMP', 'MKR', 'CRV', 'SNX', 'YFI', 'SUSHI', '1INCH', 'BAL', 'LDO']
            polygon_tokens = ['MATIC', 'QUICK', 'DQUICK', 'GHST', 'REVV', 'TOWER']
            arbitrum_tokens = ['ARB', 'GMX', 'GLP', 'MAGIC', 'RDNT', 'VELA', 'GRAIL']

            if token in ethereum_tokens:
                return 'ethereum'
            elif token in polygon_tokens:
                return 'polygon'
            elif token in arbitrum_tokens:
                return 'arbitrum'
            else:
                return 'ethereum'  # Default to Ethereum

        except Exception as e:
            logger.error(f"Blockchain determination error: {e}")
            return 'ethereum'

    async def _get_additional_tokens_for_blockchain(self, blockchain: str, count: int) -> List[str]:
        """Get additional tokens for specific blockchain to meet targets"""
        try:
            additional_tokens = []

            # Blockchain-specific token pools
            token_pools = {
                'ethereum': [
                    'PEPE', 'SHIB', 'FLOKI', 'ELON', 'AKITA', 'HOGE', 'DOGELON', 'CATGIRL',
                    'WOJAK', 'LADYS', 'TURBO', 'AIDOGE', 'BABYDOGE', 'KISHU', 'MEME', 'PEPE2',
                    'BOBO', 'NORMIE', 'GIGA', 'CHAD', 'VIRGIN', 'COPE', 'HOPIUM', 'WAGMI'
                ],
                'bsc': [
                    'SAFEMOON', 'ELONGATE', 'HOKK', 'FOGE', 'CORGI', 'PITBULL', 'DOGE2',
                    'SHIBAINU', 'FLOKI2', 'BABYCAKE', 'SAFEMARS', 'MOONSHOT', 'ROCKET'
                ],
                'polygon': [
                    'WMATIC', 'USDC', 'USDT', 'DAI', 'WETH', 'WBTC', 'SUSHI', 'CRV', 'BAL'
                ],
                'arbitrum': [
                    'WETH', 'USDC', 'USDT', 'DAI', 'WBTC', 'LINK', 'UNI', 'AAVE', 'CRV'
                ],
                'solana': [
                    'SOLWATER', 'SOLGRASS', 'SOLELECTRIC', 'SOLPSYCHIC', 'SOLFIGHTING',
                    'SOLPOISON', 'SOLGROUND', 'SOLFLYING', 'SOLBUG', 'SOLGHOST'
                ]
            }

            pool = token_pools.get(blockchain, [])
            additional_tokens = pool[:count]

            return additional_tokens

        except Exception as e:
            logger.error(f"Additional tokens fetch error: {e}")
            return []

    async def _fallback_token_expansion(self) -> List[str]:
        """Fallback token expansion if enhanced method fails"""
        try:
            fallback_tokens = []

            # Get tokens from all categories
            for category_name, category_data in self.token_categories.items():
                tokens = category_data.get('tokens', [])
                max_tokens = min(50, len(tokens))  # Up to 50 per category
                fallback_tokens.extend(tokens[:max_tokens])

            # Remove duplicates
            unique_tokens = list(dict.fromkeys(fallback_tokens))

            self.add_log(f"⚠️ Using fallback expansion: {len(unique_tokens)} tokens", "warning")
            return unique_tokens[:500]  # Fallback to 500 tokens

        except Exception as e:
            logger.error(f"Fallback expansion error: {e}")
            return []

    async def _process_pairs_for_arbitrage_v3(self, all_pairs: List[Dict], tokens: List[str], tier: int) -> List[Dict]:
        """v3.0 Enhanced: Process pairs with tier-specific logic and dynamic thresholds"""
        opportunities = []

        try:
            # Group pairs by token symbol and chain
            token_pairs = {}

            for pair in all_pairs:
                base_token = pair.get('baseToken', {})
                token_symbol = base_token.get('symbol', '').upper()
                chain_id = pair.get('chainId', '')

                if token_symbol in [t.upper() for t in tokens]:
                    key = f"{token_symbol}_{chain_id}"
                    if key not in token_pairs:
                        token_pairs[key] = []
                    token_pairs[key].append(pair)

            # Find arbitrage opportunities with dynamic thresholds
            for key, pairs in token_pairs.items():
                if len(pairs) >= 2:  # Need at least 2 pairs for arbitrage
                    token_symbol, chain = key.split('_', 1)

                    # Get dynamic profit threshold for this token
                    token_category = self._get_token_category(token_symbol)
                    profit_threshold = self.profit_threshold_manager.get_profit_threshold(token_symbol, token_category)

                    chain_opportunities = await self._find_arbitrage_in_pairs_v3(
                        pairs, token_symbol, chain, profit_threshold, tier
                    )
                    opportunities.extend(chain_opportunities)

            return opportunities

        except Exception as e:
            logger.error(f"v3.0 Pair processing error: {e}")
            return []

    async def _find_arbitrage_in_pairs_v3(self, pairs: List[Dict], token_symbol: str, chain: str,
                                         profit_threshold: Dict, tier: int) -> List[Dict]:
        """v3.0 Enhanced: Find arbitrage with dynamic thresholds and tier-specific logic"""
        opportunities = []

        try:
            # Convert pairs to simplified format for comparison
            dex_prices = {}

            for pair in pairs:
                dex_name = pair.get('dexId', 'unknown')
                price_usd = float(pair.get('priceUsd', 0))
                liquidity_usd = pair.get('liquidity', {}).get('usd', 0)
                volume_24h = pair.get('volume', {}).get('h24', 0)

                # Tier-specific filtering
                min_liquidity = self._get_tier_min_liquidity(tier)
                min_volume = self._get_tier_min_volume(tier)

                if price_usd > 0 and liquidity_usd > min_liquidity and volume_24h > min_volume:
                    dex_prices[dex_name] = {
                        'price': price_usd,
                        'liquidity': liquidity_usd,
                        'volume_24h': volume_24h,
                        'pair_address': pair.get('pairAddress', ''),
                        'pair_data': pair
                    }

            # Find arbitrage opportunities between DEXs
            dex_list = list(dex_prices.items())
            for i, (dex1_id, dex1_data) in enumerate(dex_list):
                for j, (dex2_id, dex2_data) in enumerate(dex_list[i+1:], i+1):

                    price1 = dex1_data["price"]
                    price2 = dex2_data["price"]

                    if price1 <= 0 or price2 <= 0:
                        continue

                    # Calculate profit percentage
                    if price2 > price1:
                        profit_pct = ((price2 - price1) / price1) * 100
                        buy_dex = dex1_id
                        sell_dex = dex2_id
                        buy_price = price1
                        sell_price = price2
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                    else:
                        profit_pct = ((price1 - price2) / price2) * 100
                        buy_dex = dex2_id
                        sell_dex = dex1_id
                        buy_price = price2
                        sell_price = price1
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])

                    # Check against dynamic profit thresholds
                    min_profit = profit_threshold.get('min', 5.0)
                    max_profit = profit_threshold.get('max', 50.0)

                    if (min_profit < profit_pct <= max_profit and
                        min_liquidity > self.config["min_liquidity"]):

                        opportunity = {
                            "id": f"{token_symbol}_{chain}_{int(time.time())}_{i}_{j}",
                            "token_symbol": token_symbol,
                            "buy_exchange": f"{chain}_{buy_dex}",
                            "sell_exchange": f"{chain}_{sell_dex}",
                            "buy_price": round(buy_price, 6),
                            "sell_price": round(sell_price, 6),
                            "profit_percentage": round(profit_pct, 4),
                            "profit_usd": round(profit_pct * 10, 2),
                            "min_liquidity": round(min_liquidity, 2),
                            "buy_chain": chain,
                            "sell_chain": chain,
                            "buy_dex_name": buy_dex,
                            "sell_dex_name": sell_dex,
                            "buy_pair_address": dex_prices[buy_dex].get("pair_address"),
                            "sell_pair_address": dex_prices[sell_dex].get("pair_address"),
                            "timestamp": datetime.now().isoformat(),
                            "type": "v3_tier_based_scan",
                            "tier": tier,
                            "profit_threshold": profit_threshold,
                            "token_category": self._get_token_category(token_symbol)
                        }

                        opportunities.append(opportunity)

            return opportunities

        except Exception as e:
            logger.error(f"v3.0 Arbitrage finding error: {e}")
            return []

    def _get_tier_min_liquidity(self, tier: int) -> float:
        """Get minimum liquidity requirement for tier"""
        tier_requirements = {
            1: 50000,   # $50K for priority tier
            2: 25000,   # $25K for regular tier
            3: 10000    # $10K for discovery tier
        }
        return tier_requirements.get(tier, 25000)

    def _get_tier_min_volume(self, tier: int) -> float:
        """Get minimum volume requirement for tier"""
        tier_requirements = {
            1: 100000,  # $100K for priority tier
            2: 50000,   # $50K for regular tier
            3: 10000    # $10K for discovery tier
        }
        return tier_requirements.get(tier, 50000)

    def _get_token_category(self, token_symbol: str) -> str:
        """Determine token category for profit threshold calculation"""
        try:
            for category_name, category_data in self.token_categories.items():
                if token_symbol.upper() in [t.upper() for t in category_data.get('tokens', [])]:
                    return category_name

            return 'defi'  # Default category

        except Exception as e:
            logger.error(f"Token category determination error: {e}")
            return 'defi'

    async def _update_tier_token_metrics(self, tokens: List[str], opportunities: List[Dict]):
        """Update token metrics for tier management"""
        try:
            for token in tokens:
                # Check if token has opportunities
                token_opportunities = [opp for opp in opportunities if opp.get('token_symbol', '').upper() == token.upper()]

                metrics = {
                    'has_opportunity': len(token_opportunities) > 0,
                    'opportunity_count': len(token_opportunities),
                    'volume_24h': 0,
                    'liquidity': 0
                }

                # Calculate average metrics from opportunities
                if token_opportunities:
                    total_liquidity = sum(opp.get('min_liquidity', 0) for opp in token_opportunities)
                    metrics['liquidity'] = total_liquidity / len(token_opportunities)

                await self.tiered_scanner.update_token_metrics(token, metrics)

        except Exception as e:
            logger.error(f"Tier token metrics update error: {e}")

    async def _generate_demo_opportunities(self) -> List[Dict]:
        """v3.0 ENHANCED: Generate realistic demo arbitrage opportunities with current market context"""
        try:
            demo_opportunities = []

            # Enhanced demo data with more realistic scenarios
            demo_scenarios = [
                # Stablecoins - Small but consistent spreads
                {
                    'token': 'USDC', 'category': 'stablecoins',
                    'scenarios': [
                        {'buy_price': 0.9992, 'sell_price': 1.0008, 'buy_dex': 'uniswap_v3', 'sell_dex': 'sushiswap', 'chain': 'ethereum', 'liquidity': 250000},
                        {'buy_price': 0.9995, 'sell_price': 1.0012, 'buy_dex': 'pancakeswap', 'sell_dex': 'biswap', 'chain': 'bsc', 'liquidity': 180000}
                    ]
                },
                {
                    'token': 'USDT', 'category': 'stablecoins',
                    'scenarios': [
                        {'buy_price': 0.9988, 'sell_price': 1.0015, 'buy_dex': 'quickswap', 'sell_dex': 'sushiswap', 'chain': 'polygon', 'liquidity': 200000}
                    ]
                },
                # Blue chips - Moderate spreads
                {
                    'token': 'WETH', 'category': 'blue_chips',
                    'scenarios': [
                        {'buy_price': 2456.80, 'sell_price': 2468.90, 'buy_dex': 'uniswap_v2', 'sell_dex': 'uniswap_v3', 'chain': 'ethereum', 'liquidity': 500000},
                        {'buy_price': 2461.20, 'sell_price': 2475.30, 'buy_dex': 'pancakeswap', 'sell_dex': 'apeswap', 'chain': 'bsc', 'liquidity': 300000}
                    ]
                },
                {
                    'token': 'WBTC', 'category': 'blue_chips',
                    'scenarios': [
                        {'buy_price': 43250.00, 'sell_price': 43520.00, 'buy_dex': 'sushiswap', 'sell_dex': 'balancer', 'chain': 'ethereum', 'liquidity': 400000}
                    ]
                },
                # Solana ecosystem
                {
                    'token': 'SOL', 'category': 'solana_ecosystem',
                    'scenarios': [
                        {'buy_price': 95.45, 'sell_price': 96.89, 'buy_dex': 'raydium', 'sell_dex': 'orca', 'chain': 'solana', 'liquidity': 350000},
                        {'buy_price': 96.12, 'sell_price': 97.45, 'buy_dex': 'serum', 'sell_dex': 'saber', 'chain': 'solana', 'liquidity': 280000}
                    ]
                },
                {
                    'token': 'RAY', 'category': 'solana_ecosystem',
                    'scenarios': [
                        {'buy_price': 0.1234, 'sell_price': 0.1267, 'buy_dex': 'raydium', 'sell_dex': 'orca', 'chain': 'solana', 'liquidity': 150000}
                    ]
                },
                # Layer 1/2 tokens
                {
                    'token': 'MATIC', 'category': 'layer1_2',
                    'scenarios': [
                        {'buy_price': 0.8234, 'sell_price': 0.8356, 'buy_dex': 'quickswap', 'sell_dex': 'sushiswap', 'chain': 'polygon', 'liquidity': 220000},
                        {'buy_price': 0.8289, 'sell_price': 0.8398, 'buy_dex': 'uniswap_v3', 'sell_dex': 'balancer', 'chain': 'ethereum', 'liquidity': 180000}
                    ]
                },
                {
                    'token': 'ARB', 'category': 'layer1_2',
                    'scenarios': [
                        {'buy_price': 1.234, 'sell_price': 1.267, 'buy_dex': 'camelot', 'sell_dex': 'sushiswap', 'chain': 'arbitrum', 'liquidity': 160000}
                    ]
                },
                # DeFi tokens
                {
                    'token': 'UNI', 'category': 'defi',
                    'scenarios': [
                        {'buy_price': 6.789, 'sell_price': 6.945, 'buy_dex': 'uniswap_v2', 'sell_dex': 'sushiswap', 'chain': 'ethereum', 'liquidity': 300000}
                    ]
                },
                {
                    'token': 'AAVE', 'category': 'defi',
                    'scenarios': [
                        {'buy_price': 89.45, 'sell_price': 91.23, 'buy_dex': 'uniswap_v3', 'sell_dex': 'balancer', 'chain': 'ethereum', 'liquidity': 250000}
                    ]
                },
                # Meme coins - Higher volatility
                {
                    'token': 'BONK', 'category': 'meme_coins',
                    'scenarios': [
                        {'buy_price': 0.000012, 'sell_price': 0.000014, 'buy_dex': 'raydium', 'sell_dex': 'orca', 'chain': 'solana', 'liquidity': 120000}
                    ]
                },
                {
                    'token': 'WIF', 'category': 'meme_coins',
                    'scenarios': [
                        {'buy_price': 0.0234, 'sell_price': 0.0267, 'buy_dex': 'raydium', 'sell_dex': 'meteora', 'chain': 'solana', 'liquidity': 100000}
                    ]
                }
            ]

            # Generate opportunities from scenarios
            for token_data in demo_scenarios:
                token = token_data['token']
                category = token_data['category']

                # Get current profit threshold for this token
                profit_threshold = self.profit_threshold_manager.get_profit_threshold(token, category)

                for scenario in token_data['scenarios']:
                    # Calculate profit percentage
                    profit_pct = ((scenario['sell_price'] - scenario['buy_price']) / scenario['buy_price']) * 100

                    # Check if it meets current thresholds
                    if profit_threshold['min'] <= profit_pct <= profit_threshold['max']:
                        opportunity = {
                            "id": f"demo_v3_{token}_{scenario['chain']}_{int(time.time())}_{random.randint(1000, 9999)}",
                            "token_symbol": token,
                            "buy_exchange": f"{scenario['chain']}_{scenario['buy_dex']}",
                            "sell_exchange": f"{scenario['chain']}_{scenario['sell_dex']}",
                            "buy_price": round(scenario['buy_price'], 8),
                            "sell_price": round(scenario['sell_price'], 8),
                            "profit_percentage": round(profit_pct, 4),
                            "profit_usd": round(profit_pct * 10, 2),
                            "min_liquidity": scenario['liquidity'],
                            "buy_chain": scenario['chain'],
                            "sell_chain": scenario['chain'],
                            "buy_dex_name": scenario['buy_dex'],
                            "sell_dex_name": scenario['sell_dex'],
                            "buy_pair_address": f"demo_{scenario['buy_dex']}_{token.lower()}",
                            "sell_pair_address": f"demo_{scenario['sell_dex']}_{token.lower()}",
                            "timestamp": datetime.now().isoformat(),
                            "type": "v3_enhanced_demo",
                            "tier": 1 if category in ['stablecoins', 'blue_chips'] else 2 if category in ['defi', 'layer1_2'] else 3,
                            "profit_threshold": profit_threshold,
                            "token_category": category,
                            "validation_score": 85.0,  # High validation score for demo
                            "data_sources": ["demo_api_v3"],
                            "is_demo": True,
                            "demo_scenario": "realistic_market_conditions"
                        }

                        demo_opportunities.append(opportunity)

            # Add some randomization to make it more realistic
            if demo_opportunities:
                # Randomly select 5-12 opportunities to simulate real market conditions
                num_opportunities = random.randint(5, min(12, len(demo_opportunities)))
                demo_opportunities = random.sample(demo_opportunities, num_opportunities)

                # Sort by profit percentage (highest first)
                demo_opportunities.sort(key=lambda x: x['profit_percentage'], reverse=True)

            return demo_opportunities

        except Exception as e:
            logger.error(f"Enhanced demo opportunity generation error: {e}")
            return []

    async def _scan_tier_with_multi_api(self, tokens: List[str], chains: List[str], tier: int) -> List[Dict]:
        """v3.0 ENHANCED: Scan tier using multi-API data provider with enhanced validation"""
        try:
            all_opportunities = []

            self.add_log(f"🔍 Multi-API scanning {len(tokens)} tokens across {len(chains)} chains (Tier {tier})", "info")

            # Process tokens in smaller batches for better API management
            batch_size = 5 if tier == 1 else 8 if tier == 2 else 10

            for i in range(0, len(tokens), batch_size):
                batch_tokens = tokens[i:i + batch_size]

                # Get data from multiple API sources
                batch_opportunities = []

                for token in batch_tokens:
                    try:
                        # Get multi-source price data
                        token_data = await self.multi_api_provider.get_token_prices(token, chains)

                        if token_data:
                            # Process each chain's data
                            for chain, pairs in token_data.items():
                                if len(pairs) >= 2:  # Need at least 2 DEXs for arbitrage
                                    # Find arbitrage opportunities within this chain
                                    chain_opportunities = await self._find_arbitrage_in_validated_pairs(
                                        pairs, token, chain, tier
                                    )
                                    batch_opportunities.extend(chain_opportunities)

                        # Small delay between tokens
                        await asyncio.sleep(0.1)

                    except Exception as e:
                        logger.error(f"Token {token} scan error: {e}")
                        continue

                all_opportunities.extend(batch_opportunities)

                # Delay between batches
                await asyncio.sleep(0.2)

            self.add_log(f"✅ Multi-API scan completed: {len(all_opportunities)} opportunities found", "info")
            return all_opportunities

        except Exception as e:
            logger.error(f"Multi-API tier scan error: {e}")
            return []

    async def _find_arbitrage_in_validated_pairs(self, pairs: List[Dict], token_symbol: str, chain: str, tier: int) -> List[Dict]:
        """Find arbitrage opportunities in validated pairs with enhanced filtering"""
        try:
            opportunities = []

            # Get dynamic profit threshold
            token_category = self._get_token_category(token_symbol)
            profit_threshold = self.profit_threshold_manager.get_profit_threshold(token_symbol, token_category)

            # Group pairs by DEX for comparison
            dex_prices = {}

            for pair in pairs:
                try:
                    dex_name = pair.get('dexId', 'unknown')
                    price_usd = float(pair.get('priceUsd', 0))
                    liquidity_usd = pair.get('liquidity', {}).get('usd', 0)
                    volume_24h = pair.get('volume', {}).get('h24', 0)
                    validation_score = pair.get('validation_score', 0)

                    # Enhanced filtering with tier-specific requirements
                    min_liquidity = self._get_tier_min_liquidity(tier)
                    min_volume = self._get_tier_min_volume(tier)
                    min_validation_score = 50.0  # Minimum validation score

                    if (price_usd > 0 and
                        liquidity_usd >= min_liquidity and
                        volume_24h >= min_volume and
                        validation_score >= min_validation_score):

                        dex_prices[dex_name] = {
                            'price': price_usd,
                            'liquidity': liquidity_usd,
                            'volume_24h': volume_24h,
                            'validation_score': validation_score,
                            'pair_address': pair.get('pairAddress', ''),
                            'source': pair.get('source', 'unknown'),
                            'pair_data': pair
                        }

                except (ValueError, TypeError) as e:
                    logger.warning(f"Invalid pair data for {token_symbol}: {e}")
                    continue

            # Find arbitrage opportunities between validated DEXs
            if len(dex_prices) >= 2:
                dex_list = list(dex_prices.items())

                for i, (dex1_id, dex1_data) in enumerate(dex_list):
                    for j, (dex2_id, dex2_data) in enumerate(dex_list[i+1:], i+1):

                        price1 = dex1_data["price"]
                        price2 = dex2_data["price"]

                        if price1 <= 0 or price2 <= 0:
                            continue

                        # Calculate profit percentage
                        if price2 > price1:
                            profit_pct = ((price2 - price1) / price1) * 100
                            buy_dex = dex1_id
                            sell_dex = dex2_id
                            buy_price = price1
                            sell_price = price2
                            min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                            avg_validation_score = (dex1_data["validation_score"] + dex2_data["validation_score"]) / 2
                        else:
                            profit_pct = ((price1 - price2) / price2) * 100
                            buy_dex = dex2_id
                            sell_dex = dex1_id
                            buy_price = price2
                            sell_price = price1
                            min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                            avg_validation_score = (dex1_data["validation_score"] + dex2_data["validation_score"]) / 2

                        # Enhanced profit threshold checking
                        min_profit = profit_threshold.get('min', 0.1)
                        max_profit = profit_threshold.get('max', 50.0)

                        # Additional validation for realistic opportunities
                        if (min_profit <= profit_pct <= max_profit and
                            avg_validation_score >= 60.0 and  # Higher validation score for opportunities
                            min_liquidity >= min_liquidity):

                            opportunity = {
                                "id": f"v3_multi_api_{token_symbol}_{chain}_{int(time.time())}_{random.randint(1000, 9999)}",
                                "token_symbol": token_symbol,
                                "buy_exchange": f"{chain}_{buy_dex}",
                                "sell_exchange": f"{chain}_{sell_dex}",
                                "buy_price": round(buy_price, 8),
                                "sell_price": round(sell_price, 8),
                                "profit_percentage": round(profit_pct, 4),
                                "profit_usd": round(profit_pct * 10, 2),
                                "min_liquidity": round(min_liquidity, 2),
                                "buy_chain": chain,
                                "sell_chain": chain,
                                "buy_dex_name": buy_dex,
                                "sell_dex_name": sell_dex,
                                "buy_pair_address": dex_prices[buy_dex].get("pair_address"),
                                "sell_pair_address": dex_prices[sell_dex].get("pair_address"),
                                "timestamp": datetime.now().isoformat(),
                                "type": "v3_multi_api_validated",
                                "tier": tier,
                                "profit_threshold": profit_threshold,
                                "token_category": token_category,
                                "validation_score": round(avg_validation_score, 2),
                                "data_sources": [dex_prices[buy_dex].get("source"), dex_prices[sell_dex].get("source")],
                                "is_demo": False
                            }

                            opportunities.append(opportunity)

            return opportunities

        except Exception as e:
            logger.error(f"Validated arbitrage finding error: {e}")
            return []

    async def _process_pairs_for_arbitrage(self, all_pairs: List[Dict], tokens: List[str]) -> List[Dict]:
        """Process pairs from parallel scan to find arbitrage opportunities"""
        opportunities = []

        try:
            # Group pairs by token symbol and chain
            token_pairs = {}

            for pair in all_pairs:
                base_token = pair.get('baseToken', {})
                token_symbol = base_token.get('symbol', '').upper()
                chain_id = pair.get('chainId', '')

                if token_symbol in [t.upper() for t in tokens]:
                    key = f"{token_symbol}_{chain_id}"
                    if key not in token_pairs:
                        token_pairs[key] = []
                    token_pairs[key].append(pair)

            # Find arbitrage opportunities within each token-chain group
            for key, pairs in token_pairs.items():
                if len(pairs) >= 2:  # Need at least 2 pairs for arbitrage
                    token_symbol, chain = key.split('_', 1)
                    chain_opportunities = await self._find_arbitrage_in_pairs(pairs, token_symbol, chain)
                    opportunities.extend(chain_opportunities)

            return opportunities

        except Exception as e:
            logger.error(f"Pair processing error: {e}")
            return []

    async def _find_arbitrage_in_pairs(self, pairs: List[Dict], token_symbol: str, chain: str) -> List[Dict]:
        """Find arbitrage opportunities within a group of pairs with strict pair validation"""
        opportunities = []

        try:
            # CRITICAL: Validate pairs to prevent false arbitrage signals
            valid_pairs, validation_logs = self.pair_validator.validate_arbitrage_pairs(pairs, token_symbol)

            # Log validation results
            for log_msg in validation_logs:
                self.add_log(log_msg, "validation")

            if len(valid_pairs) < 2:
                self.add_log(f"❌ {token_symbol}: No valid arbitrage pairs found after validation", "warning")
                return []

            # Group validated pairs by normalized pair key
            pair_groups = {}
            for trading_pair in valid_pairs:
                pair_key = trading_pair.get_pair_key()
                if pair_key not in pair_groups:
                    pair_groups[pair_key] = []
                pair_groups[pair_key].append(trading_pair)

            # Find arbitrage opportunities within each validated pair group
            for pair_key, group_pairs in pair_groups.items():
                if len(group_pairs) < 2:
                    continue

                # Create DEX price mapping for this specific pair
                dex_prices = {}
                for trading_pair in group_pairs:
                    if trading_pair.price_usd > 0 and trading_pair.liquidity_usd > 1000:
                        dex_prices[trading_pair.dex_id] = {
                            'price': trading_pair.price_usd,
                            'liquidity': trading_pair.liquidity_usd,
                            'pair_address': trading_pair.pair_address,
                            'trading_pair': trading_pair,
                            'pair_key': pair_key
                        }

                # Find arbitrage opportunities between DEXs for this specific pair
                dex_list = list(dex_prices.items())
                for i, (dex1_id, dex1_data) in enumerate(dex_list):
                    for j, (dex2_id, dex2_data) in enumerate(dex_list[i+1:], i+1):

                        price1 = dex1_data["price"]
                        price2 = dex2_data["price"]

                        if price1 <= 0 or price2 <= 0:
                            continue

                        # Calculate profit percentage
                        if price2 > price1:
                            profit_pct = ((price2 - price1) / price1) * 100
                            buy_dex = dex1_id
                            sell_dex = dex2_id
                            buy_price = price1
                            sell_price = price2
                            min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                        else:
                            profit_pct = ((price1 - price2) / price2) * 100
                            buy_dex = dex2_id
                            sell_dex = dex1_id
                            buy_price = price2
                            sell_price = price1
                            min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])

                        # Check if profitable
                        if (self.config["profit_min"] < profit_pct <= self.config["profit_max"] and
                            min_liquidity > self.config["min_liquidity"]):

                            # Get DexScreener links for both DEXs
                            buy_pair_address = dex_prices[buy_dex].get("pair_address", "")
                            sell_pair_address = dex_prices[sell_dex].get("pair_address", "")

                            buy_dexscreener_link = f"https://dexscreener.com/{chain}/{buy_pair_address}" if buy_pair_address else ""
                            sell_dexscreener_link = f"https://dexscreener.com/{chain}/{sell_pair_address}" if sell_pair_address else ""

                            opportunity = {
                                "id": f"{token_symbol}_{chain}_{int(time.time())}_{i}_{j}",
                                "token_symbol": token_symbol,
                                "validated_pair": pair_key,  # Show the validated trading pair
                                "buy_exchange": f"{chain}_{buy_dex}",
                                "sell_exchange": f"{chain}_{sell_dex}",
                                "buy_price": round(buy_price, 6),
                                "sell_price": round(sell_price, 6),
                                "profit_percentage": round(profit_pct, 4),
                                "profit_usd": round(profit_pct * 10, 2),
                                "min_liquidity": round(min_liquidity, 2),
                                "buy_chain": chain,
                                "sell_chain": chain,
                                "buy_dex_name": buy_dex,
                                "sell_dex_name": sell_dex,
                                "buy_pair_address": buy_pair_address,
                                "sell_pair_address": sell_pair_address,
                                "buy_dexscreener_link": buy_dexscreener_link,
                                "sell_dexscreener_link": sell_dexscreener_link,
                                "timestamp": datetime.now().isoformat(),
                                "type": "validated_arbitrage",
                                "validation_status": "✅ PAIR_VALIDATED"
                            }

                            opportunities.append(opportunity)
                            self.add_log(f"✅ {token_symbol}: Valid arbitrage found for {pair_key} - {profit_pct:.2f}% profit", "success")

            return opportunities

        except Exception as e:
            logger.error(f"Arbitrage finding error: {e}")
            return []

    async def _fallback_detailed_scan(self, tokens: List[str]) -> List[Dict]:
        """Fallback to detailed scanning if high-performance scan yields few results"""
        opportunities = []

        try:
            self.add_log("🔍 Running detailed fallback scan for better accuracy...", "info")

            # Use original detailed scanning method for a subset of tokens
            for chain in self.config["enabled_chains"]:
                for token in tokens[:10]:  # Limit to 10 tokens for fallback
                    token_opportunities = await self.scan_token_same_chain(token, chain)
                    opportunities.extend(token_opportunities)
                    await asyncio.sleep(0.1)  # Small delay

            return opportunities

        except Exception as e:
            logger.error(f"Fallback scan error: {e}")
            return []  # Remove duplicates

    def calculate_slippage_estimate(self, order_size_usd: float, liquidity_usd: float) -> float:
        """Estimate slippage based on order size vs liquidity"""
        if liquidity_usd <= 0:
            return 100.0  # 100% slippage if no liquidity

        # Simple slippage model: slippage increases quadratically with order size
        liquidity_ratio = order_size_usd / liquidity_usd

        if liquidity_ratio <= 0.001:  # < 0.1% of pool
            return 0.01  # ~0.01% slippage
        elif liquidity_ratio <= 0.01:  # < 1% of pool
            return 0.1 + (liquidity_ratio * 10)  # 0.1-1.1% slippage
        elif liquidity_ratio <= 0.05:  # < 5% of pool
            return 1.0 + (liquidity_ratio * 20)  # 1-2% slippage
        else:
            return min(50.0, 2.0 + (liquidity_ratio * 100))  # 2-50% slippage

    def calculate_risk_level(self, liquidity_ratio: float, slippage: float) -> str:
        """Calculate risk level based on liquidity ratio and slippage"""
        if liquidity_ratio >= 20 and slippage <= 0.5:
            return "Low"
        elif liquidity_ratio >= 10 and slippage <= 1.0:
            return "Medium"
        else:
            return "High"

    async def filter_opportunities_by_simulation(self, opportunities: List[Dict]) -> List[Dict]:
        """Filter opportunities based on simulation parameters with enhanced validation"""
        filtered_opportunities = []
        capital = self.config["simulation_capital"]
        max_slippage = self.config["max_slippage"]
        capital_per_trade = (self.config["capital_per_trade"] / 100) * capital
        min_liquidity_ratio = self.config["min_liquidity_ratio"]

        # Process opportunities with enhanced validation
        for opp in opportunities:
            min_liquidity = opp.get("min_liquidity", 0)

            # Check if liquidity is sufficient (min_liquidity_ratio * capital)
            required_liquidity = capital_per_trade * min_liquidity_ratio
            if min_liquidity < required_liquidity:
                continue

            # Calculate slippage estimates
            buy_slippage = self.calculate_slippage_estimate(capital_per_trade, min_liquidity)
            sell_slippage = self.calculate_slippage_estimate(capital_per_trade, min_liquidity)
            total_slippage = buy_slippage + sell_slippage

            # Filter by max slippage
            if total_slippage > max_slippage:
                continue

            # Calculate simulation-specific metrics
            liquidity_ratio = min_liquidity / capital_per_trade
            risk_level = self.calculate_risk_level(liquidity_ratio, total_slippage)

            # Calculate profit in USD based on simulation capital
            profit_percentage = opp.get("profit_percentage", 0)
            estimated_profit_usd = (profit_percentage / 100) * capital_per_trade

            # Add simulation data to opportunity
            opp.update({
                "simulation_capital": capital_per_trade,
                "estimated_profit_usd": round(estimated_profit_usd, 2),
                "buy_slippage": round(buy_slippage, 3),
                "sell_slippage": round(sell_slippage, 3),
                "total_slippage": round(total_slippage, 3),
                "liquidity_ratio": round(liquidity_ratio, 1),
                "risk_level": risk_level,
                "recommended_order_size": round(min(capital_per_trade, min_liquidity * 0.01), 2)  # Max 1% of liquidity
            })

            # ENHANCED: Apply real-time validation
            enhanced_opp = await self.enhance_opportunity_with_validation(opp)

            # Only include opportunities that pass enhanced validation
            validation_result = enhanced_opp.get('validation_result', {})
            if validation_result.get('is_valid', False) and validation_result.get('feasibility_score', 0) >= 60:
                filtered_opportunities.append(enhanced_opp)
            else:
                # Log why opportunity was filtered out
                warnings = validation_result.get('warnings', [])
                self.add_log(f"🚫 Filtered out {opp.get('token_symbol', 'Unknown')}: {', '.join(warnings[:2])}", "warning")

        return filtered_opportunities

    async def scan_opportunities(self) -> List[Dict[str, Any]]:
        """v3.0 ENHANCED: Intelligent tier-based scanning for 1000+ tokens"""
        self.add_log("🚀 v3.0 Enhanced: Starting intelligent tier-based scanning...", "info")
        opportunities = []
        scan_start_time = time.time()

        try:
            # Clear expired cache entries
            self.smart_cache.clear_expired()

            # Update market volatility and profit thresholds
            if self.v3_config.get('enable_adaptive_thresholds', True):
                volatility = await self.profit_threshold_manager.update_volatility_based_thresholds()
                self.add_log(f"📊 Market volatility: {volatility}", "info")

            # Initialize/update tier assignments
            if self.v3_config.get('enable_tier_management', True):
                await self.tiered_scanner.evaluate_tier_promotions_demotions()

            # Get enabled tokens with v3.0 intelligent expansion
            enabled_tokens = await self.get_enabled_tokens_expanded()
            enabled_chains = self.config["enabled_chains"]

            self.add_log(f"🎯 v3.0 Intelligent scan: {len(enabled_tokens)} tokens across {len(enabled_chains)} chains", "info")

            # ENHANCED v3.0: Multi-API tier-based scanning with enhanced data quality
            all_opportunities = []

            # Scan each tier with appropriate intervals and priorities
            for tier in [1, 2, 3]:
                tier_tokens = self.tiered_scanner.get_tokens_for_tier(tier)
                if not tier_tokens:
                    continue

                tier_name = self.tiered_scanner.tier_definitions[tier]['name']
                self.add_log(f"🔍 Scanning {tier_name}: {len(tier_tokens)} tokens", "info")

                # v3.0 ENHANCED: Use multi-API data provider for better data quality
                tier_opportunities = await self._scan_tier_with_multi_api(tier_tokens, enabled_chains, tier)
                all_opportunities.extend(tier_opportunities)

                # Update token metrics for tier management
                await self._update_tier_token_metrics(tier_tokens, tier_opportunities)

            # v3.0 ENHANCED: Comprehensive opportunity analysis and fallback
            self.add_log(f"🔍 Scan Analysis: Found {len(all_opportunities)} real opportunities", "info")

            # Log API health status
            api_health = self.multi_api_provider.get_api_health_status()
            healthy_apis = sum(1 for api_status in api_health['apis'].values() if api_status['status'] == 'healthy')
            total_apis = len(api_health['apis'])
            self.add_log(f"📡 API Health: {healthy_apis}/{total_apis} APIs healthy", "info")

            demo_mode_enabled = self.v3_config.get('enable_demo_mode', True)
            self.add_log(f"🔍 Demo mode enabled: {demo_mode_enabled}", "info")

            # Enhanced fallback logic
            if len(all_opportunities) == 0:
                self.add_log("⚠️ No real arbitrage opportunities detected", "warning")
                self.add_log("🔍 Possible reasons:", "info")
                self.add_log("   1. Market efficiency - spreads below profit thresholds", "info")
                self.add_log("   2. API data quality issues", "info")
                self.add_log("   3. Insufficient liquidity on detected pairs", "info")
                self.add_log("   4. High validation score requirements", "info")

                if demo_mode_enabled:
                    self.add_log("🧪 Activating demo mode to showcase system capabilities", "info")
                    demo_opportunities = await self._generate_demo_opportunities()
                    self.add_log(f"🧪 Generated {len(demo_opportunities)} realistic demo opportunities", "info")

                    if demo_opportunities:
                        # Log demo opportunity details
                        for i, opp in enumerate(demo_opportunities[:3]):
                            self.add_log(f"   Demo {i+1}: {opp['token_symbol']} {opp['profit_percentage']:.2f}% ({opp['buy_dex_name']} → {opp['sell_dex_name']})", "info")

                    all_opportunities.extend(demo_opportunities)
                else:
                    self.add_log("❌ Demo mode disabled - no opportunities to display", "warning")
            else:
                self.add_log(f"✅ Found {len(all_opportunities)} real opportunities, demo mode not needed", "info")

                # Log real opportunity summary
                if all_opportunities:
                    profit_range = [opp['profit_percentage'] for opp in all_opportunities]
                    avg_profit = sum(profit_range) / len(profit_range)
                    max_profit = max(profit_range)
                    min_profit = min(profit_range)

                    self.add_log(f"📊 Profit range: {min_profit:.2f}% - {max_profit:.2f}% (avg: {avg_profit:.2f}%)", "info")

                    # Log top opportunities
                    sorted_opps = sorted(all_opportunities, key=lambda x: x['profit_percentage'], reverse=True)
                    for i, opp in enumerate(sorted_opps[:3]):
                        self.add_log(f"   Top {i+1}: {opp['token_symbol']} {opp['profit_percentage']:.2f}% ({opp['buy_dex_name']} → {opp['sell_dex_name']})", "info")

            # Update scan metrics
            self.scan_metrics = self.parallel_scanner.scan_metrics

            self.add_log(f"⚡ v3.0 Tier-based scan completed: {self.scan_metrics.tokens_per_second:.1f} tokens/sec", "info")
            self.add_log(f"📊 Cache hit rate: {self.scan_metrics.cache_hit_rate:.1f}%", "info")

            opportunities = all_opportunities

            # Remove duplicates and sort by profit
            unique_opportunities = self.deduplicate_opportunities(opportunities)
            unique_opportunities.sort(key=lambda x: x.get('profit_percentage', 0), reverse=True)

            # Apply enhanced simulation filtering with real-time validation
            filtered_opportunities = await self.filter_opportunities_by_simulation(unique_opportunities)

            self.opportunities = filtered_opportunities[:20]  # Keep top 20
            self.last_scan = datetime.now().isoformat()

            total_found = len(unique_opportunities)
            feasible_count = len(filtered_opportunities)

            # Generate safety summary
            safety_summary = self.create_safe_opportunity_summary(filtered_opportunities)

            self.add_log(f"✅ Pemindaian selesai! Ditemukan {total_found} peluang, {feasible_count} layak dengan modal simulasi", "success")
            self.add_log(f"🔒 Safety: {safety_summary['validated_opportunities']}/{feasible_count} validated ({safety_summary['validation_rate']}%), Safety Score: {safety_summary['safety_score']}", "info")

            if safety_summary['high_risk_opportunities'] > 0:
                self.add_log(f"⚠️ WARNING: {safety_summary['high_risk_opportunities']} opportunities flagged as HIGH RISK - verify manually!", "warning")

        except Exception as e:
            self.add_log(f"❌ Error pemindaian: {str(e)}", "error")
            logger.error(f"Scan error: {e}")

        return self.opportunities

    async def scan_token_same_chain(self, token_symbol: str, target_chain: str) -> List[Dict[str, Any]]:
        """Scan token untuk arbitrase antar DEX dalam blockchain yang sama"""
        opportunities = []

        try:
            # Search for token pairs on DexScreener untuk chain tertentu dengan parameter chain
            search_data = await dexscreener.get("dex/search", {"q": token_symbol, "chain": target_chain})

            if "pairs" not in search_data or not search_data["pairs"]:
                return opportunities

            # Filter pairs hanya untuk target chain
            chain_pairs = [
                pair for pair in search_data["pairs"]
                if pair.get("chainId", "").lower() == target_chain.lower()
            ]

            if len(chain_pairs) < 2:
                self.add_log(f"⚠️ Tidak cukup DEX untuk {token_symbol} di {target_chain}", "warning")
                return opportunities

            self.add_log(f"📈 Ditemukan {len(chain_pairs)} pairs {token_symbol} di {target_chain}", "info")

            # ENHANCED: Group pairs by DEX dengan validasi token identity yang ketat
            dex_prices = {}
            valid_pairs = []

            # First pass: Collect valid pairs dengan token validation
            for pair in chain_pairs:
                try:
                    if not self.is_valid_pair(pair):
                        continue

                    # CRITICAL: Validate token symbol matches exactly
                    base_token = pair.get("baseToken", {})
                    pair_symbol = base_token.get("symbol", "").upper()

                    if pair_symbol != token_symbol.upper():
                        self.add_log(f"⚠️ SKIPPED: Symbol mismatch - Expected {token_symbol.upper()}, got {pair_symbol}", "warning")
                        continue

                    valid_pairs.append(pair)

                except (ValueError, TypeError):
                    continue

            if len(valid_pairs) < 2:
                self.add_log(f"⚠️ Insufficient valid pairs for {token_symbol} di {target_chain} after validation", "warning")
                return opportunities

            # Second pass: Group by DEX dengan additional validation
            for pair in valid_pairs:
                try:
                    dex_id = pair.get("dexId", "unknown")
                    price_usd = float(pair.get("priceUsd", 0))
                    liquidity = pair.get("liquidity", {}).get("usd", 0)
                    volume_24h = pair.get("volume", {}).get("h24", 0)

                    # Keep the pair with highest liquidity for each DEX
                    if dex_id not in dex_prices or liquidity > dex_prices[dex_id]["liquidity"]:
                        dex_prices[dex_id] = {
                            "price": price_usd,
                            "liquidity": liquidity,
                            "volume_24h": volume_24h,
                            "chain": target_chain,
                            "dex": dex_id,
                            "pair_address": pair.get("pairAddress"),
                            "base_token": pair.get("baseToken", {}).get("symbol", token_symbol),
                            "token_address": pair.get("baseToken", {}).get("address", ""),
                            "token_name": pair.get("baseToken", {}).get("name", ""),
                            "pair_data": pair  # Store full pair data for validation
                        }

                except (ValueError, TypeError):
                    continue

            # ENHANCED: Find arbitrage opportunities dengan strict token validation
            dex_list = list(dex_prices.items())

            for i, (dex1_id, dex1_data) in enumerate(dex_list):
                for j, (dex2_id, dex2_data) in enumerate(dex_list[i+1:], i+1):

                    # CRITICAL: Validate that both pairs represent the same token
                    pair1 = dex1_data.get("pair_data")
                    pair2 = dex2_data.get("pair_data")

                    if not pair1 or not pair2:
                        self.add_log(f"⚠️ Missing pair data for comparison", "warning")
                        continue

                    if not self.is_same_token(pair1, pair2, token_symbol):
                        self.add_log(f"🚫 BLOCKED: Token identity validation failed for {dex1_id} vs {dex2_id}", "error")
                        continue

                    price1 = dex1_data["price"]
                    price2 = dex2_data["price"]

                    if price1 <= 0 or price2 <= 0:
                        continue

                    # Additional price sanity check
                    price_ratio = max(price1, price2) / min(price1, price2)
                    if price_ratio > 5:  # 500% difference is suspicious
                        self.add_log(f"⚠️ SUSPICIOUS: Large price difference {price1} vs {price2} (ratio: {price_ratio:.2f})", "warning")
                        # Continue but flag as high risk

                    # Calculate profit percentage
                    if price2 > price1:
                        profit_pct = ((price2 - price1) / price1) * 100
                        buy_dex = dex1_id
                        sell_dex = dex2_id
                        buy_price = price1
                        sell_price = price2
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                        buy_dex_data = dex1_data
                        sell_dex_data = dex2_data
                    else:
                        profit_pct = ((price1 - price2) / price2) * 100
                        buy_dex = dex2_id
                        sell_dex = dex1_id
                        buy_price = price2
                        sell_price = price1
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                        buy_dex_data = dex2_data
                        sell_dex_data = dex1_data

                    # Check if profitable dengan konfigurasi yang dapat diubah
                    if (self.config["profit_min"] < profit_pct <= self.config["profit_max"] and
                        min_liquidity > self.config["min_liquidity"]):

                        # Enhanced opportunity data dengan validation info
                        opportunity = {
                            "id": f"{token_symbol}_{target_chain}_{int(time.time())}_{i}_{j}",
                            "token_symbol": token_symbol,
                            "buy_exchange": f"{target_chain}_{buy_dex}",
                            "sell_exchange": f"{target_chain}_{sell_dex}",
                            "buy_price": round(buy_price, 6),
                            "sell_price": round(sell_price, 6),
                            "profit_percentage": round(profit_pct, 4),
                            "profit_usd": round(profit_pct * 10, 2),
                            "min_liquidity": round(min_liquidity, 2),
                            "buy_chain": target_chain,
                            "sell_chain": target_chain,  # Same chain
                            "buy_dex_name": buy_dex,
                            "sell_dex_name": sell_dex,
                            "buy_pair_address": buy_dex_data.get("pair_address"),
                            "sell_pair_address": sell_dex_data.get("pair_address"),
                            "timestamp": datetime.now().isoformat(),
                            "type": "same_chain_arbitrage",
                            # ENHANCED: Token validation info
                            "token_validation": {
                                "buy_token_address": buy_dex_data.get("token_address", ""),
                                "sell_token_address": sell_dex_data.get("token_address", ""),
                                "buy_token_name": buy_dex_data.get("token_name", ""),
                                "sell_token_name": sell_dex_data.get("token_name", ""),
                                "price_ratio": round(price_ratio, 2),
                                "validated": True,
                                "validation_timestamp": datetime.now().isoformat()
                            }
                        }

                        # Add risk flag for high price ratios
                        if price_ratio > 2:
                            opportunity["risk_flags"] = ["high_price_difference"]
                            opportunity["risk_level"] = "High"

                        opportunities.append(opportunity)

            if opportunities:
                self.add_log(f"💰 Ditemukan {len(opportunities)} peluang arbitrase {token_symbol} di {target_chain}", "success")

        except Exception as e:
            self.add_log(f"❌ Error scanning {token_symbol} di {target_chain}: {str(e)}", "error")

        return opportunities

    async def scan_token_across_dexs(self, token_symbol: str) -> List[Dict[str, Any]]:
        """Scan a specific token across all DEXs for arbitrage opportunities"""
        opportunities = []

        try:
            # Search for token pairs on DexScreener (all chains)
            search_data = await dexscreener.get("dex/search", {"q": token_symbol})

            if "pairs" not in search_data or not search_data["pairs"]:
                self.add_log(f"⚠️ No pairs found for {token_symbol}", "warning")
                return opportunities

            pairs = search_data["pairs"]
            self.add_log(f"📈 Found {len(pairs)} pairs for {token_symbol}", "info")

            # Group pairs by DEX and chain
            dex_prices = {}

            for pair in pairs:
                try:
                    # Filter valid pairs
                    if not self.is_valid_pair(pair):
                        continue

                    chain_id = pair.get("chainId", "unknown")
                    dex_id = pair.get("dexId", "unknown")
                    price_usd = float(pair.get("priceUsd", 0))
                    liquidity = pair.get("liquidity", {}).get("usd", 0)
                    volume_24h = pair.get("volume", {}).get("h24", 0)

                    key = f"{chain_id}_{dex_id}"

                    # Keep the pair with highest liquidity for each DEX
                    if key not in dex_prices or liquidity > dex_prices[key]["liquidity"]:
                        dex_prices[key] = {
                            "price": price_usd,
                            "liquidity": liquidity,
                            "volume_24h": volume_24h,
                            "chain": chain_id,
                            "dex": dex_id,
                            "pair_address": pair.get("pairAddress"),
                            "base_token": pair.get("baseToken", {}).get("symbol", token_symbol)
                        }

                except (ValueError, TypeError) as e:
                    continue

            # Find arbitrage opportunities between DEXs
            dex_list = list(dex_prices.items())

            for i, (dex1_key, dex1_data) in enumerate(dex_list):
                for j, (dex2_key, dex2_data) in enumerate(dex_list[i+1:], i+1):

                    price1 = dex1_data["price"]
                    price2 = dex2_data["price"]

                    if price1 <= 0 or price2 <= 0:
                        continue

                    # Calculate profit percentage
                    if price2 > price1:
                        profit_pct = ((price2 - price1) / price1) * 100
                        buy_dex = dex1_key
                        sell_dex = dex2_key
                        buy_price = price1
                        sell_price = price2
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                    else:
                        profit_pct = ((price1 - price2) / price2) * 100
                        buy_dex = dex2_key
                        sell_dex = dex1_key
                        buy_price = price2
                        sell_price = price1
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])

                    # Check if profitable dengan konfigurasi yang dapat diubah
                    if (self.config["profit_min"] < profit_pct <= self.config["profit_max"] and
                        min_liquidity > self.config["min_liquidity"]):
                        # Get DEX data for links
                        buy_dex_data = dex_prices[buy_dex]
                        sell_dex_data = dex_prices[sell_dex]

                        opportunities.append({
                            "id": f"{token_symbol}_{int(time.time())}_{i}_{j}",
                            "token_symbol": token_symbol,
                            "buy_exchange": buy_dex,
                            "sell_exchange": sell_dex,
                            "buy_price": round(buy_price, 6),
                            "sell_price": round(sell_price, 6),
                            "profit_percentage": round(profit_pct, 4),
                            "profit_usd": round(profit_pct * 10, 2),  # Assuming $1000 trade
                            "min_liquidity": round(min_liquidity, 2),
                            "buy_chain": buy_dex_data["chain"],
                            "sell_chain": sell_dex_data["chain"],
                            "buy_dex_name": buy_dex_data["dex"],
                            "sell_dex_name": sell_dex_data["dex"],
                            "buy_pair_address": buy_dex_data.get("pair_address"),
                            "sell_pair_address": sell_dex_data.get("pair_address"),
                            "timestamp": datetime.now().isoformat(),
                            "type": "dex_arbitrage"
                        })

            if opportunities:
                self.add_log(f"💰 Ditemukan {len(opportunities)} peluang arbitrase {token_symbol}", "success")
            else:
                self.add_log(f"📊 Tidak ada peluang arbitrase valid untuk {token_symbol}", "info")

        except Exception as e:
            self.add_log(f"❌ Error scanning {token_symbol}: {str(e)}", "error")

        return opportunities

    async def scan_trending_pairs(self) -> List[Dict[str, Any]]:
        """Scan trending pairs for additional opportunities"""
        opportunities = []

        try:
            # Get some popular pairs directly
            trending_searches = ["ETH", "BTC", "MATIC", "ARB", "OP"]

            for search_term in trending_searches:
                pairs_data = await dexscreener.get("dex/search", {"q": search_term})

                if "pairs" in pairs_data and pairs_data["pairs"]:
                    # Take top 5 pairs by volume
                    top_pairs = sorted(
                        pairs_data["pairs"][:10],
                        key=lambda x: x.get("volume", {}).get("h24", 0),
                        reverse=True
                    )[:5]

                    for pair in top_pairs:
                        if self.is_valid_pair(pair):
                            token_symbol = pair.get("baseToken", {}).get("symbol", search_term)
                            token_opportunities = await self.scan_token_across_dexs(token_symbol)
                            opportunities.extend(token_opportunities)

                await asyncio.sleep(0.3)  # Rate limiting

        except Exception as e:
            self.add_log(f"❌ Error scanning trending pairs: {str(e)}", "error")

        return opportunities

    def is_valid_pair(self, pair: Dict) -> bool:
        """Check if a pair is valid for arbitrage berdasarkan konfigurasi"""
        try:
            price_usd = float(pair.get("priceUsd", 0))
            liquidity = pair.get("liquidity", {}).get("usd", 0)
            volume_24h = pair.get("volume", {}).get("h24", 0)

            # Validation criteria menggunakan konfigurasi
            return (
                price_usd > 0 and
                liquidity > self.config["min_liquidity"] and
                volume_24h > self.config["min_volume_24h"] and
                pair.get("chainId") and
                pair.get("dexId") and
                pair.get("baseToken", {}).get("symbol")
            )
        except (ValueError, TypeError):
            return False

    def is_same_token(self, pair1: Dict, pair2: Dict, target_symbol: str) -> bool:
        """
        CRITICAL: Validate that two pairs represent the SAME token
        This prevents dangerous mismatches like WETH/SOL vs different tokens
        """
        try:
            # Get token information from both pairs
            token1 = pair1.get("baseToken", {})
            token2 = pair2.get("baseToken", {})

            # Primary validation: Contract addresses must match (most reliable)
            addr1 = token1.get("address", "").lower()
            addr2 = token2.get("address", "").lower()

            # If both have addresses and they're different, it's NOT the same token
            if addr1 and addr2 and addr1 != addr2:
                self.add_log(f"⚠️ DANGER: Different token addresses detected! {addr1} vs {addr2}", "error")
                return False

            # Secondary validation: Symbol matching
            symbol1 = token1.get("symbol", "").upper()
            symbol2 = token2.get("symbol", "").upper()
            target_upper = target_symbol.upper()

            # All symbols must match the target
            if symbol1 != target_upper or symbol2 != target_upper:
                self.add_log(f"⚠️ DANGER: Symbol mismatch! Expected {target_upper}, got {symbol1} vs {symbol2}", "error")
                return False

            # Tertiary validation: Name similarity check
            name1 = token1.get("name", "").lower()
            name2 = token2.get("name", "").lower()

            # If names are very different, flag as suspicious
            if name1 and name2 and name1 != name2:
                # Allow some variation but flag major differences
                if len(name1) > 3 and len(name2) > 3:
                    # Simple similarity check
                    common_chars = set(name1) & set(name2)
                    if len(common_chars) < min(len(name1), len(name2)) * 0.5:
                        self.add_log(f"⚠️ WARNING: Suspicious name difference: '{name1}' vs '{name2}'", "warning")
                        return False

            # Additional validation: Price sanity check
            price1 = float(pair1.get("priceUsd", 0))
            price2 = float(pair2.get("priceUsd", 0))

            if price1 > 0 and price2 > 0:
                # If prices differ by more than 1000%, it's likely different tokens
                price_ratio = max(price1, price2) / min(price1, price2)
                if price_ratio > 10:  # 1000% difference
                    self.add_log(f"⚠️ DANGER: Extreme price difference detected! {price1} vs {price2} (ratio: {price_ratio:.2f})", "error")
                    return False

            return True

        except Exception as e:
            self.add_log(f"❌ Error validating token identity: {str(e)}", "error")
            return False

    async def validate_token_by_address(self, token_address: str, chain: str) -> Dict:
        """
        Additional validation: Lookup token info by contract address
        This provides extra verification for token identity
        """
        try:
            if not token_address or len(token_address) < 10:
                return {}

            # Use DexScreener token endpoint for additional validation
            token_data = await dexscreener.get(f"dex/tokens/{token_address}")

            if "pairs" in token_data and token_data["pairs"]:
                # Get token info from the first pair
                first_pair = token_data["pairs"][0]
                base_token = first_pair.get("baseToken", {})

                return {
                    "address": base_token.get("address", ""),
                    "symbol": base_token.get("symbol", ""),
                    "name": base_token.get("name", ""),
                    "verified": True
                }

            return {}

        except Exception as e:
            self.add_log(f"⚠️ Token address validation failed: {str(e)}", "warning")
            return {}

    async def enhance_opportunity_with_validation(self, opportunity: Dict) -> Dict:
        """
        CRITICAL: Enhance opportunity with comprehensive real-time validation
        """
        try:
            # Get basic opportunity data
            buy_pair_address = opportunity.get('buy_pair_address', '')
            sell_pair_address = opportunity.get('sell_pair_address', '')
            chain = opportunity.get('buy_chain', '')
            capital_per_trade = self.config.get('simulation_capital', 100)

            # 1. Validate liquidity depth
            buy_liquidity_valid, buy_depth_score, buy_warnings = await self.validator.validate_liquidity_depth(
                buy_pair_address, chain, capital_per_trade
            )
            sell_liquidity_valid, sell_depth_score, sell_warnings = await self.validator.validate_liquidity_depth(
                sell_pair_address, chain, capital_per_trade
            )

            # 2. Validate execution feasibility
            execution_feasible, execution_time, execution_warnings = await self.validator.validate_execution_feasibility(opportunity)

            # 3. Filter false positives
            is_legitimate, confidence_score, fp_warnings = await self.validator.filter_false_positives(opportunity)

            # Calculate overall feasibility score
            liquidity_score = min(buy_depth_score, sell_depth_score)
            feasibility_score = int((liquidity_score + confidence_score) / 2)

            # Collect all warnings
            all_warnings = buy_warnings + sell_warnings + execution_warnings + fp_warnings

            # Determine overall validation status
            is_valid = (buy_liquidity_valid and sell_liquidity_valid and
                       execution_feasible and is_legitimate and
                       feasibility_score >= 60)

            # 4. ENHANCED: Security analysis
            buy_token_address = opportunity.get('token_validation', {}).get('buy_token_address', '')
            sell_token_address = opportunity.get('token_validation', {}).get('sell_token_address', '')

            security_data = {}
            if buy_token_address and len(buy_token_address) > 10:
                # Check security for buy token
                goplus_data = await self.security_analyzer.check_goplus_security(buy_token_address, chain)
                quickintel_data = await self.security_analyzer.check_quickintel_rating(buy_token_address, chain)

                # Generate combined security score
                combined_security_score = await self.security_analyzer.generate_security_score(goplus_data, quickintel_data)
                security_flags = await self.security_analyzer.get_security_flags(goplus_data, quickintel_data)

                security_data = {
                    'security_score': combined_security_score,
                    'goplus_data': goplus_data,
                    'quickintel_data': quickintel_data,
                    'security_flags': security_flags,
                    'is_honeypot': goplus_data.get('is_honeypot', False),
                    'audit_status': goplus_data.get('audit_status', 'unknown'),
                    'owner_privileges': goplus_data.get('owner_privileges', [])
                }

                # Apply security-based filtering
                if combined_security_score < 30:
                    is_valid = False
                    all_warnings.append(f"CRITICAL: Very low security score ({combined_security_score}/100)")

                if goplus_data.get('is_honeypot', False):
                    is_valid = False
                    all_warnings.append("CRITICAL: Token flagged as honeypot")

                # Adjust feasibility score based on security
                security_penalty = max(0, (70 - combined_security_score) // 2)
                feasibility_score = max(0, feasibility_score - security_penalty)

            # Create validation result
            validation_result = ValidationResult(
                is_valid=is_valid,
                feasibility_score=feasibility_score,
                warnings=all_warnings,
                security_flags=security_data.get('security_flags', []),
                execution_time_estimate=execution_time,
                liquidity_depth_score=liquidity_score
            )

            # Enhance opportunity with validation data
            opportunity.update({
                'validation_result': {
                    'is_valid': validation_result.is_valid,
                    'feasibility_score': validation_result.feasibility_score,
                    'warnings': validation_result.warnings,
                    'execution_time_estimate': validation_result.execution_time_estimate,
                    'liquidity_depth_score': validation_result.liquidity_depth_score,
                    'confidence_score': confidence_score,
                    'validation_timestamp': datetime.now().isoformat()
                },
                'enhanced_validation': True,
                'security_data': security_data
            })

            # Update risk level based on validation and security
            security_score = security_data.get('security_score', 50)

            if not is_valid or feasibility_score < 40 or security_score < 30:
                opportunity['risk_level'] = 'High'
                risk_flags = opportunity.get('risk_flags', []) + ['validation_failed']
                if security_score < 30:
                    risk_flags.append('security_risk')
                opportunity['risk_flags'] = risk_flags
            elif feasibility_score < 70 or security_score < 60:
                opportunity['risk_level'] = 'Medium'
                if security_score < 60:
                    opportunity['risk_flags'] = opportunity.get('risk_flags', []) + ['moderate_security_risk']
            else:
                opportunity['risk_level'] = 'Low'

            return opportunity

        except Exception as e:
            logger.error(f"Enhanced validation error: {e}")
            opportunity['validation_result'] = {
                'is_valid': False,
                'feasibility_score': 0,
                'warnings': [f"Validation error: {str(e)}"],
                'execution_time_estimate': 300,
                'liquidity_depth_score': 0,
                'confidence_score': 0,
                'validation_timestamp': datetime.now().isoformat()
            }
            opportunity['risk_level'] = 'High'
            opportunity['risk_flags'] = opportunity.get('risk_flags', []) + ['validation_error']
            return opportunity

    def create_safe_opportunity_summary(self, opportunities: List[Dict]) -> Dict:
        """
        Create a summary with safety information about detected opportunities
        """
        total_opportunities = len(opportunities)
        validated_opportunities = len([opp for opp in opportunities if opp.get("token_validation", {}).get("validated", False)])
        high_risk_opportunities = len([opp for opp in opportunities if opp.get("risk_flags")])

        return {
            "total_opportunities": total_opportunities,
            "validated_opportunities": validated_opportunities,
            "high_risk_opportunities": high_risk_opportunities,
            "validation_rate": round((validated_opportunities / total_opportunities) * 100, 1) if total_opportunities > 0 else 0,
            "safety_score": "High" if high_risk_opportunities == 0 else "Medium" if high_risk_opportunities < total_opportunities * 0.3 else "Low"
        }

    def deduplicate_opportunities(self, opportunities: List[Dict]) -> List[Dict]:
        """Remove duplicate opportunities"""
        seen = set()
        unique_opportunities = []

        for opp in opportunities:
            # Create a key based on token, exchanges, and similar profit
            key = (
                opp.get("token_symbol"),
                tuple(sorted([opp.get("buy_exchange"), opp.get("sell_exchange")])),
                round(opp.get("profit_percentage", 0), 2)
            )

            if key not in seen:
                seen.add(key)
                unique_opportunities.append(opp)

        return unique_opportunities

    def get_dexscreener_link(self, chain: str, dex: str, pair_address: str = None) -> str:
        """Generate DexScreener link for a specific DEX pair"""
        base_url = "https://dexscreener.com"

        # Chain mapping for DexScreener URLs
        chain_mapping = {
            "ethereum": "ethereum",
            "bsc": "bsc",
            "polygon": "polygon",
            "arbitrum": "arbitrum",
            "avalanche": "avalanche",
            "solana": "solana",
            "optimism": "optimism"
        }

        mapped_chain = chain_mapping.get(chain.lower(), chain.lower())

        if pair_address:
            return f"{base_url}/{mapped_chain}/{pair_address}"
        else:
            # If no pair address, link to DEX page
            return f"{base_url}/{mapped_chain}"

    async def get_btc_prices(self) -> Dict[str, float]:
        """Get BTC prices from different sources"""
        prices = {}
        
        # CoinGecko
        try:
            cg_data = await coingecko.get("simple/price", {
                "ids": "bitcoin",
                "vs_currencies": "usd"
            })
            if "bitcoin" in cg_data and "usd" in cg_data["bitcoin"]:
                prices["coingecko"] = float(cg_data["bitcoin"]["usd"])
        except:
            pass
        
        # Binance
        try:
            binance_data = await binance.get("ticker/24hr", {"symbol": "BTCUSDT"})
            if "lastPrice" in binance_data:
                prices["binance"] = float(binance_data["lastPrice"])
        except:
            pass
        
        # DexScreener (search for WBTC)
        try:
            dex_data = await dexscreener.get("dex/search", {"q": "WBTC"})
            if "pairs" in dex_data and dex_data["pairs"]:
                # Get first valid pair
                for pair in dex_data["pairs"][:3]:
                    if pair.get("priceUsd"):
                        prices[f"dex_{pair.get('dexId', 'unknown')}"] = float(pair["priceUsd"])
                        break
        except:
            pass
        
        return prices

# Global instances - v3.0 Enhanced
websocket_manager = SimpleWebSocketManager()

# Legacy API clients (kept for backward compatibility)
coingecko = SimpleAPIClient("https://api.coingecko.com/api/v3", 10)
dexscreener = SimpleAPIClient("https://api.dexscreener.com/latest", 60)
binance = SimpleAPIClient("https://api.binance.com/api/v3", 300)

# v3.0 Enhanced detector with multi-API support
detector = AdvancedArbitrageDetector()

# FastAPI code removed - now using integrated Flask interface

# All FastAPI routes removed - using Flask interface instead

# ===== INTEGRATED FLASK WEB INTERFACE =====



                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, #ff9a9e, #fecfef, #fecfef);
                opacity: 0.7;
            }

            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            }

            .card h2 {
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 20px;
                color: #fecfef;
            }

            .button-group {
                display: flex;
                gap: 15px;
                flex-wrap: wrap;
            }

            .btn {
                background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 15px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 0.95rem;
                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
                position: relative;
                overflow: hidden;
            }

            .btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }

            .btn:hover::before {
                left: 100%;
            }

            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            }

            .btn-scan {
                background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
                box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);
            }

            .btn-scan:hover {
                box-shadow: 0 8px 25px rgba(255, 154, 158, 0.6);
            }

            .opportunities-container {
                grid-column: 1 / -1;
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 30px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                min-height: 400px;
            }

            .opportunity {
                background: rgba(255, 255, 255, 0.8);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                padding: 20px;
                margin: 15px 0;
                border: 1px solid rgba(0, 0, 0, 0.08);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                transition: all 0.3s ease;
                position: relative;
            }

            .opportunity:hover {
                transform: scale(1.02);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }

            .opportunity h3 {
                color: #1f2937;
                font-size: 1.3rem;
                margin-bottom: 10px;
                font-weight: 600;
            }

            .profit-positive {
                color: #10b981;
                font-weight: 700;
                font-size: 1.1rem;
                text-shadow: 0 1px 2px rgba(16, 185, 129, 0.2);
            }

            .profit-negative {
                color: #ef4444;
                font-weight: 700;
                font-size: 1.1rem;
                text-shadow: 0 1px 2px rgba(239, 68, 68, 0.2);
            }

            .logs-container {
                background: rgba(248, 250, 252, 0.8);
                backdrop-filter: blur(15px);
                border-radius: 20px;
                padding: 25px;
                border: 1px solid rgba(0, 0, 0, 0.08);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                max-height: 400px;
                overflow-y: auto;
            }

            .log-entry {
                display: flex;
                align-items: center;
                padding: 8px 12px;
                margin: 5px 0;
                border-radius: 12px;
                font-size: 0.9rem;
                transition: all 0.2s ease;
                border-left: 3px solid transparent;
            }

            .log-entry:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .log-entry.info {
                border-left-color: #60a5fa;
                background: rgba(219, 234, 254, 0.7);
                color: #1e40af;
            }

            .log-entry.success {
                border-left-color: #34d399;
                background: rgba(232, 245, 232, 0.7);
                color: #065f46;
            }

            .log-entry.warning {
                border-left-color: #fbbf24;
                background: rgba(255, 243, 224, 0.7);
                color: #92400e;
            }

            .log-entry.error {
                border-left-color: #f87171;
                background: rgba(255, 235, 238, 0.7);
                color: #991b1b;
            }

            .log-time {
                font-size: 0.8rem;
                opacity: 0.7;
                margin-right: 10px;
                min-width: 60px;
            }

            .status-indicator {
                display: inline-block;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin-right: 10px;
                animation: pulse 2s infinite;
            }

            .status-online {
                background: #34d399;
                box-shadow: 0 0 10px rgba(52, 211, 153, 0.5);
            }

            .status-scanning {
                background: #fbbf24;
                box-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }

            .stat-card {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 20px;
                text-align: center;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .stat-value {
                font-size: 2rem;
                font-weight: 700;
                color: #fecfef;
                margin-bottom: 5px;
            }

            .stat-label {
                font-size: 0.9rem;
                opacity: 0.8;
            }

            @media (max-width: 768px) {
                .grid {
                    grid-template-columns: 1fr;
                }

                .header h1 {
                    font-size: 2.5rem;
                }

                .button-group {
                    justify-content: center;
                }

                .container {
                    padding: 15px;
                }
            }

            /* Custom scrollbar */
            ::-webkit-scrollbar {
                width: 8px;
            }

            ::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
            }

            ::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 10px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: rgba(255, 255, 255, 0.5);
            }

            /* DexScreener link styling */
            .dex-link {
                display: inline-block;
                color: #60a5fa;
                text-decoration: none;
                font-size: 0.9rem;
                font-weight: 500;
                padding: 6px 12px;
                background: rgba(96, 165, 250, 0.1);
                border-radius: 8px;
                border: 1px solid rgba(96, 165, 250, 0.3);
                transition: all 0.3s ease;
                margin-top: 8px;
            }

            .dex-link:hover {
                background: rgba(96, 165, 250, 0.2);
                border-color: rgba(96, 165, 250, 0.5);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }

            .dex-link:active {
                transform: translateY(0);
            }

            /* Author Profile Styling */
            .author-profile {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                cursor: pointer;
            }

            .author-avatar {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 700;
                font-size: 1.2rem;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
                transition: all 0.3s ease;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }

            .author-avatar:hover {
                transform: scale(1.1);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            }

            /* Modal Styling */
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(10px);
                display: none;
                align-items: center;
                justify-content: center;
                z-index: 2000;
                animation: fadeIn 0.3s ease;
            }

            .modal-content {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 40px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                text-align: center;
                max-width: 400px;
                width: 90%;
                animation: scaleIn 0.3s ease;
            }

            .modal-avatar {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 700;
                font-size: 2rem;
                margin: 0 auto 20px;
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }

            .modal-title {
                font-size: 1.8rem;
                font-weight: 700;
                color: #fecfef;
                margin-bottom: 10px;
            }

            .modal-subtitle {
                font-size: 1rem;
                opacity: 0.8;
                margin-bottom: 30px;
            }

            .social-links {
                display: flex;
                justify-content: center;
                gap: 20px;
                margin-bottom: 30px;
            }

            .social-link {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 50px;
                height: 50px;
                border-radius: 15px;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                text-decoration: none;
                font-size: 1.5rem;
                transition: all 0.3s ease;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .social-link:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
                background: rgba(255, 255, 255, 0.2);
            }

            .close-modal {
                background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 15px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .close-modal:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(255, 154, 158, 0.4);
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes scaleIn {
                from { transform: scale(0.8); opacity: 0; }
                to { transform: scale(1); opacity: 1; }
            }

            /* Configuration Panel Styling */
            .config-panel {
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 25px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin-bottom: 30px;
            }

            .config-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                cursor: pointer;
            }

            .config-content {
                display: none;
                animation: slideDown 0.3s ease;
            }

            .config-content.expanded {
                display: block;
            }

            .config-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }

            .config-item {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 20px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .config-label {
                display: flex;
                align-items: center;
                font-weight: 600;
                margin-bottom: 10px;
                color: #fecfef;
            }

            .config-input {
                width: 100%;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                padding: 10px 15px;
                color: white;
                font-size: 0.9rem;
            }

            .config-input:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
            }

            .checkbox-group {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .checkbox-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .checkbox-item input[type="checkbox"] {
                width: 18px;
                height: 18px;
                accent-color: #667eea;
            }

            @keyframes slideDown {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            /* Token Categories Styling */
            .token-categories {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 15px 0;
            }

            .token-category {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
                padding: 15px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            .token-category h4 {
                margin: 0 0 10px 0;
                color: #fecfef;
                font-size: 0.9rem;
                font-weight: 600;
            }

            .token-list {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }

            .token-chip {
                background: rgba(102, 126, 234, 0.2);
                color: #a5b4fc;
                padding: 4px 8px;
                border-radius: 8px;
                font-size: 0.8rem;
                border: 1px solid rgba(102, 126, 234, 0.3);
            }

            /* Simulation Panel Styling */
            .simulation-panel {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 15px;
                padding: 20px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin-top: 20px;
            }

            .simulation-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 15px 0;
            }

            .simulation-summary {
                background: rgba(34, 197, 94, 0.1);
                border-radius: 12px;
                padding: 15px;
                border: 1px solid rgba(34, 197, 94, 0.3);
                margin-top: 15px;
            }

            .summary-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 5px 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            .summary-item:last-child {
                border-bottom: none;
            }

            .summary-value {
                font-weight: 600;
                color: #a7f3d0;
            }

            /* Risk Level Indicators */
            .risk-low { color: #10b981; }
            .risk-medium { color: #f59e0b; }
            .risk-high { color: #ef4444; }

            /* Tooltip Styling */
            .tooltip {
                position: relative;
                cursor: help;
            }

            .tooltip::after {
                content: attr(data-tooltip);
                position: absolute;
                bottom: 125%;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 8px 12px;
                border-radius: 8px;
                font-size: 0.8rem;
                white-space: nowrap;
                opacity: 0;
                pointer-events: none;
                transition: opacity 0.3s;
                z-index: 1000;
            }

            .tooltip:hover::after {
                opacity: 1;
            }

            /* Enhanced Opportunity Cards */
            .opportunity-enhanced {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                padding: 25px;
                margin: 20px 0;
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                position: relative;
            }

            .opportunity-enhanced:hover {
                transform: scale(1.02);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            }

            .opportunity-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }

            .risk-badge {
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 0.8rem;
                font-weight: 600;
                text-transform: uppercase;
            }

            .risk-badge.low {
                background: rgba(16, 185, 129, 0.2);
                color: #10b981;
                border: 1px solid rgba(16, 185, 129, 0.3);
            }

            .risk-badge.medium {
                background: rgba(245, 158, 11, 0.2);
                color: #f59e0b;
                border: 1px solid rgba(245, 158, 11, 0.3);
            }

            .risk-badge.high {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .simulation-metrics {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
                margin: 15px 0;
                padding: 15px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
            }

            .metric-item {
                text-align: center;
            }

            .metric-value {
                font-size: 1.2rem;
                font-weight: 700;
                color: #fecfef;
                margin-bottom: 5px;
            }

            .metric-label {
                font-size: 0.8rem;
                opacity: 0.8;
            }

            /* Solana-specific styling */
            .solana-highlight {
                background: linear-gradient(135deg, #9945FF 0%, #14F195 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                font-weight: 600;
            }

            .solana-badge {
                background: linear-gradient(135deg, #9945FF 0%, #14F195 100%);
                color: white;
                padding: 4px 8px;
                border-radius: 8px;
                font-size: 0.8rem;
                font-weight: 600;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            }

            .chain-solana {
                background: linear-gradient(135deg, rgba(153, 69, 255, 0.2) 0%, rgba(20, 241, 149, 0.2) 100%);
                border: 1px solid rgba(153, 69, 255, 0.3);
            }

            /* Enhanced Validation Styling */
            .validation-badge {
                display: inline-block;
                padding: 4px 8px;
                border-radius: 8px;
                font-size: 0.8rem;
                font-weight: 600;
                margin: 2px;
            }

            .validation-valid {
                background: rgba(34, 197, 94, 0.2);
                color: #10b981;
                border: 1px solid rgba(34, 197, 94, 0.3);
            }

            .validation-warning {
                background: rgba(245, 158, 11, 0.2);
                color: #f59e0b;
                border: 1px solid rgba(245, 158, 11, 0.3);
            }

            .validation-error {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .feasibility-score {
                display: inline-block;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.8rem;
                font-weight: 700;
                min-width: 40px;
                text-align: center;
            }

            .feasibility-high {
                background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
                color: white;
            }

            .feasibility-medium {
                background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
                color: white;
            }

            .feasibility-low {
                background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
                color: white;
            }

            .validation-details {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                padding: 10px;
                margin: 10px 0;
                border-left: 3px solid #3b82f6;
            }

            .validation-warning-list {
                list-style: none;
                padding: 0;
                margin: 5px 0;
            }

            .validation-warning-list li {
                padding: 2px 0;
                font-size: 0.85rem;
                color: #fbbf24;
            }

            .validation-warning-list li::before {
                content: "⚠️ ";
                margin-right: 5px;
            }

            /* Security Analysis Styling */
            .security-badges {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
                margin: 10px 0;
            }

            .security-badge {
                padding: 4px 8px;
                border-radius: 8px;
                font-size: 0.8rem;
                font-weight: 600;
                display: inline-block;
            }

            .goplus-high {
                background: rgba(34, 197, 94, 0.2);
                color: #10b981;
                border: 1px solid rgba(34, 197, 94, 0.3);
            }

            .goplus-medium {
                background: rgba(245, 158, 11, 0.2);
                color: #f59e0b;
                border: 1px solid rgba(245, 158, 11, 0.3);
            }

            .goplus-low {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .quickintel-a, .quickintel-b {
                background: rgba(34, 197, 94, 0.2);
                color: #10b981;
                border: 1px solid rgba(34, 197, 94, 0.3);
            }

            .quickintel-c {
                background: rgba(245, 158, 11, 0.2);
                color: #f59e0b;
                border: 1px solid rgba(245, 158, 11, 0.3);
            }

            .quickintel-d, .quickintel-f {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .security-details {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                padding: 12px;
                margin: 10px 0;
                border-left: 3px solid #8b5cf6;
            }

            .security-flag {
                display: inline-block;
                padding: 2px 6px;
                border-radius: 6px;
                font-size: 0.7rem;
                margin: 2px;
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .security-flag.honeypot {
                background: rgba(220, 38, 127, 0.2);
                color: #ec4899;
                border: 1px solid rgba(220, 38, 127, 0.3);
            }

            .security-flag.closed_source {
                background: rgba(245, 158, 11, 0.2);
                color: #f59e0b;
                border: 1px solid rgba(245, 158, 11, 0.3);
            }

            .honeypot-warning {
                background: rgba(220, 38, 127, 0.2);
                border: 1px solid rgba(220, 38, 127, 0.3);
                border-radius: 8px;
                padding: 10px;
                margin: 10px 0;
                color: #ec4899;
                font-weight: 600;
                text-align: center;
            }

            /* Solana Token Badges */
            .token-badge {
                display: inline-block;
                padding: 4px 8px;
                background: linear-gradient(135deg, #9945FF 0%, #14f195 100%);
                color: white;
                border-radius: 6px;
                font-size: 0.8rem;
                font-weight: 600;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                box-shadow: 0 2px 4px rgba(153, 69, 255, 0.3);
                transition: all 0.2s ease;
            }

            .token-badge:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(153, 69, 255, 0.4);
            }
        </style>
    </head>
    <body>
        <div class="background-overlay"></div>
        <div class="container">
            <!-- Author Profile -->
            <div class="author-profile" onclick="showAuthorModal()">
                <div class="author-avatar">BC</div>
            </div>

            <div class="header">
                <h1>🚀 Enhanced Arbitrage Bot v2.0</h1>
                <p><span class="status-indicator status-online"></span>Token Categories • Trading Simulation • Solana Support • Enhanced Filtering</p>
            </div>

            <!-- Configuration Panel -->
            <div class="config-panel">
                <div class="config-header" onclick="toggleConfig()">
                    <h2>⚙️ Panel Konfigurasi</h2>
                    <span id="config-toggle">▼</span>
                </div>
                <div class="config-content" id="config-content">
                    <div class="config-grid">
                        <div class="config-item">
                            <div class="config-label">💰 Profit Minimum (%)</div>
                            <input type="number" class="config-input" id="profit-min" value="0.05" step="0.01" min="0.01" max="100">
                        </div>
                        <div class="config-item">
                            <div class="config-label">📈 Profit Maksimal (%)</div>
                            <input type="number" class="config-input" id="profit-max" value="25" step="0.1" min="0.1" max="100">
                        </div>
                        <div class="config-item">
                            <div class="config-label">💧 Minimum Liquidity ($)</div>
                            <input type="number" class="config-input" id="min-liquidity" value="5000" step="100" min="100">
                        </div>
                        <div class="config-item">
                            <div class="config-label">📊 Volume 24h Minimum ($)</div>
                            <input type="number" class="config-input" id="min-volume" value="500" step="50" min="50">
                        </div>
                        <div class="config-item">
                            <div class="config-label">� Blockchain Aktif</div>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chain-ethereum" checked>
                                    <label for="chain-ethereum">Ethereum</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chain-bsc" checked>
                                    <label for="chain-bsc">BSC</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chain-polygon" checked>
                                    <label for="chain-polygon">Polygon</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chain-arbitrum" checked>
                                    <label for="chain-arbitrum">Arbitrum</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chain-solana" checked>
                                    <label for="chain-solana">Solana</label>
                                </div>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">⏱️ Auto-Scan Interval (detik)</div>
                            <input type="number" class="config-input" id="auto-scan" value="0" step="10" min="0" placeholder="0 = Manual">
                        </div>
                    </div>

                    <!-- Token Categories Configuration -->
                    <h3 style="color: #fecfef; margin: 30px 0 15px 0;">🎯 Kategori Token</h3>
                    <div class="token-categories" id="token-categories">
                        <div class="token-category">
                            <h4>💰 Stablecoins</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-stablecoins" checked>
                                <label for="cat-stablecoins">Enable (USDC, USDT, DAI, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>💎 Blue Chips</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-blue-chips" checked>
                                <label for="cat-blue-chips">Enable (WETH, WBTC, BNB, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>🔥 DeFi Tokens</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-defi" checked>
                                <label for="cat-defi">Enable (UNI, SUSHI, AAVE, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>⚡ Layer 1/2</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-layer1-2" checked>
                                <label for="cat-layer1-2">Enable (MATIC, ARB, OP, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>🐕 Meme Coins</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-meme-coins">
                                <label for="cat-meme-coins">Enable (DOGE, SHIB, PEPE, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>🎮 Gaming & NFT</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-gaming-nft">
                                <label for="cat-gaming-nft">Enable (AXS, SAND, MANA, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>☀️ Solana Ecosystem</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-solana-ecosystem" checked>
                                <label for="cat-solana-ecosystem">Enable (SOL, RAY, ORCA, BONK, dll)</label>
                            </div>
                        </div>
                    </div>

                    <!-- Trading Simulation Configuration -->
                    <div class="simulation-panel">
                        <h3 style="color: #fecfef; margin: 0 0 15px 0;">💼 Simulasi Modal Trading</h3>
                        <div class="simulation-grid">
                            <div class="config-item">
                                <div class="config-label">💵 Modal Simulasi ($)</div>
                                <input type="number" class="config-input" id="simulation-capital" value="100" step="10" min="10" max="10000">
                            </div>
                            <div class="config-item">
                                <div class="config-label">📉 Max Slippage (%)</div>
                                <input type="number" class="config-input" id="max-slippage" value="0.5" step="0.1" min="0.1" max="5">
                            </div>
                            <div class="config-item">
                                <div class="config-label">💰 Modal per Trade (%)</div>
                                <input type="number" class="config-input" id="capital-per-trade" value="100" step="10" min="10" max="100">
                            </div>
                            <div class="config-item">
                                <div class="config-label">🔒 Min Liquidity Ratio</div>
                                <input type="number" class="config-input" id="min-liquidity-ratio" value="10" step="1" min="1" max="100">
                            </div>
                        </div>

                        <!-- Simulation Summary -->
                        <div class="simulation-summary" id="simulation-summary">
                            <h4 style="margin: 0 0 10px 0; color: #a7f3d0;">📊 Ringkasan Simulasi</h4>
                            <div class="summary-item">
                                <span>Total Modal:</span>
                                <span class="summary-value" id="summary-capital">$100</span>
                            </div>
                            <div class="summary-item">
                                <span>Peluang Layak:</span>
                                <span class="summary-value" id="summary-opportunities">0</span>
                            </div>
                            <div class="summary-item">
                                <span>Profit Potensial:</span>
                                <span class="summary-value" id="summary-profit">$0</span>
                            </div>
                            <div class="summary-item">
                                <span>Rata-rata Slippage:</span>
                                <span class="summary-value" id="summary-slippage">0%</span>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 20px; text-align: center;">
                        <button class="btn" onclick="saveConfig()">💾 Simpan Konfigurasi</button>
                        <button class="btn" onclick="resetConfig()">🔄 Reset Default</button>
                        <button class="btn" onclick="updateSimulationSummary()">📊 Update Simulasi</button>
                    </div>
                </div>
            </div>

            <div class="grid">
                <div class="card">
                    <h2>🎯 Enhanced Control Center</h2>
                    <div class="button-group">
                        <button class="btn btn-scan" onclick="scanOpportunities()">🔍 Scan with Simulation</button>
                        <button class="btn" onclick="checkStatus()">📊 Status</button>
                        <button class="btn" onclick="updateSimulationSummary()">� Update Simulation</button>
                        <button class="btn" onclick="clearLogs()">🗑️ Hapus Log</button>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="opportunities-count">0</div>
                            <div class="stat-label">Peluang Layak</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="total-profit">$0</div>
                            <div class="stat-label">Profit Potensial</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="avg-slippage">0%</div>
                            <div class="stat-label">Avg Slippage</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="safety-score">-</div>
                            <div class="stat-label">Safety Score</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="scan-status">Siap</div>
                            <div class="stat-label">Status</div>
                        </div>
                    </div>

                    <!-- Performance Metrics Card -->
                    <div class="card" style="margin-top: 20px;">
                        <h2>⚡ Performance Monitoring</h2>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value" id="scan-speed">0</div>
                                <div class="stat-label">Tokens/Sec</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="api-response-time">0ms</div>
                                <div class="stat-label">Avg Response</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="cache-hit-rate">0%</div>
                                <div class="stat-label">Cache Hit Rate</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="success-rate">0%</div>
                                <div class="stat-label">Success Rate</div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div style="margin: 15px 0;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                <span style="font-size: 0.9rem; color: #a7f3d0;">Scan Progress</span>
                                <span style="font-size: 0.9rem; color: #a7f3d0;" id="progress-text">Ready</span>
                            </div>
                            <div style="background: rgba(255, 255, 255, 0.1); border-radius: 10px; height: 8px; overflow: hidden;">
                                <div id="progress-bar" style="background: linear-gradient(90deg, #10b981, #34d399); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2>� Live Scan Logs</h2>
                    <div class="logs-container" id="logs-container">
                        <div class="log-entry info">
                            <span class="log-time">Ready</span>
                            <span>🤖 Bot initialized and ready for scanning</span>
                        </div>
                    </div>
                </div>

                <div class="opportunities-container">
                    <h2>💰 Enhanced Arbitrage Opportunities</h2>
                    <div id="opportunities">
                        <div style="text-align: center; padding: 60px 20px; opacity: 0.7;">
                            <div style="font-size: 3rem; margin-bottom: 20px;">🔍</div>
                            <p style="font-size: 1.2rem; margin-bottom: 10px;">Ready for Enhanced Arbitrage Scanning</p>
                            <p style="opacity: 0.8;">Click "Scan with Simulation" to start analysis with token categories and trading simulation</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Solana Ecosystem Statistics -->
            <div class="card" style="background: linear-gradient(135deg, rgba(153, 69, 255, 0.1) 0%, rgba(20, 241, 149, 0.1) 100%); border: 1px solid rgba(153, 69, 255, 0.2);">
                <h2>☀️ Solana Ecosystem Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="solana-total-tokens">200+</div>
                        <div class="stat-label">Total Tokens</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="solana-defi-tokens">0</div>
                        <div class="stat-label">DeFi Tokens</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="solana-gaming-tokens">0</div>
                        <div class="stat-label">Gaming Tokens</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="solana-meme-tokens">0</div>
                        <div class="stat-label">Meme Tokens</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="solana-opportunities">0</div>
                        <div class="stat-label">Current Opportunities</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="solana-scan-speed">0</div>
                        <div class="stat-label">Scan Speed (t/s)</div>
                    </div>
                </div>

                <!-- Priority Tokens Display -->
                <div style="margin-top: 20px; padding: 15px; background: rgba(153, 69, 255, 0.1); border-radius: 12px;">
                    <h4 style="margin: 0 0 10px 0; color: #9945FF;">🌟 Priority Tokens</h4>
                    <div id="solana-priority-tokens" style="display: flex; flex-wrap: wrap; gap: 8px;">
                        <span class="token-badge">SOL</span>
                        <span class="token-badge">RAY</span>
                        <span class="token-badge">ORCA</span>
                        <span class="token-badge">BONK</span>
                        <span class="token-badge">WIF</span>
                    </div>
                </div>
            </div>

            <!-- v3.0 Enhanced Features Dashboard -->
            <div class="card" style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%); border: 1px solid rgba(59, 130, 246, 0.2);">
                <h2>🚀 v3.0 Enhanced Features</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="v3-total-tokens">1000+</div>
                        <div class="stat-label">Total Tokens</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="v3-tier1-tokens">0</div>
                        <div class="stat-label">Tier 1 (Priority)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="v3-tier2-tokens">0</div>
                        <div class="stat-label">Tier 2 (Regular)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="v3-tier3-tokens">0</div>
                        <div class="stat-label">Tier 3 (Discovery)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="v3-volatility">Normal</div>
                        <div class="stat-label">Market Volatility</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="v3-discovered">0</div>
                        <div class="stat-label">Discovered Tokens</div>
                    </div>
                </div>

                <!-- Blockchain Distribution -->
                <div style="margin-top: 20px; padding: 15px; background: rgba(59, 130, 246, 0.1); border-radius: 12px;">
                    <h4 style="margin: 0 0 10px 0; color: #3b82f6;">🌐 Blockchain Distribution</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px;">
                        <div style="text-align: center;">
                            <div style="font-weight: bold; color: #fecfef;" id="v3-ethereum-count">300</div>
                            <div style="font-size: 0.8rem;">Ethereum</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-weight: bold; color: #9945FF;" id="v3-solana-count">250</div>
                            <div style="font-size: 0.8rem;">Solana</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-weight: bold; color: #f0b90b;" id="v3-bsc-count">200</div>
                            <div style="font-size: 0.8rem;">BSC</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-weight: bold; color: #8247e5;" id="v3-polygon-count">150</div>
                            <div style="font-size: 0.8rem;">Polygon</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-weight: bold; color: #28a0f0;" id="v3-arbitrum-count">100</div>
                            <div style="font-size: 0.8rem;">Arbitrum</div>
                        </div>
                    </div>
                </div>

                <!-- Dynamic Thresholds -->
                <div style="margin-top: 15px; padding: 15px; background: rgba(139, 92, 246, 0.1); border-radius: 12px;">
                    <h4 style="margin: 0 0 10px 0; color: #8b5cf6;">⚡ Dynamic Profit Thresholds</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 8px; font-size: 0.85rem;">
                        <div>Stablecoins: <span id="v3-stable-threshold" style="color: #10b981;">0.5-2%</span></div>
                        <div>Blue Chips: <span id="v3-blue-threshold" style="color: #3b82f6;">2-5%</span></div>
                        <div>DeFi: <span id="v3-defi-threshold" style="color: #f59e0b;">5-10%</span></div>
                        <div>Memes: <span id="v3-meme-threshold" style="color: #ef4444;">10-50%</span></div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h2>🌐 Data Sources & Coverage</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">🦎</div>
                        <div class="stat-label">CoinGecko<br><small>Market Data</small></div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">📊</div>
                        <div class="stat-label">DexScreener<br><small>50+ DEXs</small></div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">🔶</div>
                        <div class="stat-label">Binance<br><small>CEX Data</small></div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">15+</div>
                        <div class="stat-label">Popular Tokens<br><small>Monitored</small></div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 20px; background: rgba(255, 255, 255, 0.05); border-radius: 15px;">
                    <h3 style="margin-bottom: 15px; color: #fecfef;">🔍 Scanning Strategy</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <strong>Multi-DEX Analysis:</strong> Compares prices across Uniswap, PancakeSwap, SushiSwap, QuickSwap, and more
                        </li>
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <strong>Cross-Chain Opportunities:</strong> Ethereum, BSC, Polygon, Arbitrum, Avalanche
                        </li>
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <strong>Liquidity Filtering:</strong> Only shows opportunities with sufficient liquidity (>$5k)
                        </li>
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <strong>Profit Range Filter:</strong> 0.05% - 200% to avoid false signals
                        </li>
                        <li style="padding: 8px 0;">
                            <strong>Real-time Updates:</strong> Continuous monitoring with live profit calculations
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Author Modal -->
        <div class="modal-overlay" id="author-modal">
            <div class="modal-content">
                <div class="modal-avatar">BC</div>
                <div class="modal-title">BOBACHEESE</div>
                <div class="modal-subtitle">Author</div>

                <div class="social-links">
                    <a href="https://github.com/bobacheese" target="_blank" class="social-link" title="GitHub">
                        🐙
                    </a>
                    <a href="https://youtube.com/@bobacheese?si=5M2leEilS3_VmNS6" target="_blank" class="social-link" title="YouTube">
                        📺
                    </a>
                    <a href="https://coff.ee/amarullohzd" target="_blank" class="social-link" title="Ko-fi">
                        ☕
                    </a>
                </div>

                <button class="close-modal" onclick="hideAuthorModal()">Tutup</button>
            </div>
        </div>

        <script>
            let scanInterval;
            let isScanning = false;

            // Function to generate DexScreener links
            function generateDexScreenerLink(chain, dex, pairAddress) {
                const baseUrl = "https://dexscreener.com";

                // Chain mapping for DexScreener URLs
                const chainMapping = {
                    "ethereum": "ethereum",
                    "bsc": "bsc",
                    "polygon": "polygon",
                    "arbitrum": "arbitrum",
                    "avalanche": "avalanche",
                    "solana": "solana",
                    "optimism": "optimism"
                };

                const mappedChain = chainMapping[chain?.toLowerCase()] || chain?.toLowerCase() || "ethereum";

                if (pairAddress) {
                    return `${baseUrl}/${mappedChain}/${pairAddress}`;
                } else {
                    // If no pair address, link to chain page
                    return `${baseUrl}/${mappedChain}`;
                }
            }

            async function scanOpportunities() {
                if (isScanning) return;

                isScanning = true;
                updateScanStatus('Memindai...');
                updateProgressBar(10, 'Initializing high-performance scan...');

                // Show v3.0 enhanced scanning animation
                document.getElementById('opportunities').innerHTML = `
                    <div style="text-align: center; padding: 60px 20px;">
                        <div style="font-size: 3rem; margin-bottom: 20px; animation: pulse 1s infinite;">🚀</div>
                        <p style="font-size: 1.2rem; margin-bottom: 10px;">v3.0 Enhanced: Intelligent Tier-Based Scanning...</p>
                        <p style="opacity: 0.8;">Analyzing 1000+ tokens across 5 blockchains with intelligent tier management</p>

                        <div style="margin-top: 20px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="padding: 12px; background: rgba(59, 130, 246, 0.1); border-radius: 12px; border-left: 3px solid #3b82f6;">
                                <p style="margin: 0; font-size: 0.9rem; color: #3b82f6; font-weight: 600;">
                                    🎯 Tier 1: Priority (100 tokens)
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 0.8rem; opacity: 0.8;">
                                    Scan every 30 seconds
                                </p>
                            </div>
                            <div style="padding: 12px; background: rgba(139, 92, 246, 0.1); border-radius: 12px; border-left: 3px solid #8b5cf6;">
                                <p style="margin: 0; font-size: 0.9rem; color: #8b5cf6; font-weight: 600;">
                                    🔄 Tier 2: Regular (400 tokens)
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 0.8rem; opacity: 0.8;">
                                    Scan every 2 minutes
                                </p>
                            </div>
                            <div style="padding: 12px; background: rgba(16, 185, 129, 0.1); border-radius: 12px; border-left: 3px solid #10b981;">
                                <p style="margin: 0; font-size: 0.9rem; color: #10b981; font-weight: 600;">
                                    🔍 Tier 3: Discovery (500+ tokens)
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 0.8rem; opacity: 0.8;">
                                    Scan every 5 minutes
                                </p>
                            </div>
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: rgba(245, 158, 11, 0.1); border-radius: 12px; border-left: 3px solid #f59e0b;">
                            <p style="margin: 0; font-size: 0.9rem; color: #f59e0b;">
                                ⚡ Dynamic Features: Adaptive Thresholds • Token Discovery • Security Validation
                            </p>
                        </div>
                    </div>
                `;

                try {
                    updateProgressBar(20, 'Initializing Solana ecosystem scan...');
                    await new Promise(resolve => setTimeout(resolve, 500)); // Brief pause for UI

                    updateProgressBar(40, 'Scanning 200+ Solana tokens...');
                    const response = await fetch('/api/scan');

                    updateProgressBar(70, 'Processing Solana opportunities...');
                    const data = await response.json();

                    updateProgressBar(90, 'Finalizing Solana arbitrage results...');

                    // Update opportunities count
                    document.getElementById('opportunities-count').textContent = data.count || 0;

                    if (data.opportunities && data.opportunities.length > 0) {
                        let html = '';
                        data.opportunities.forEach((opp, index) => {
                            const profitClass = opp.profit_percentage > 0 ? 'profit-positive' : 'profit-negative';
                            const exchangeDisplay = (exchange) => {
                                const parts = exchange.split('_');
                                return parts.length > 1 ? `${parts[1]} (${parts[0]})` : exchange;
                            };

                            // Generate DexScreener links
                            const buyLink = generateDexScreenerLink(opp.buy_chain, opp.buy_dex_name, opp.buy_pair_address);
                            const sellLink = generateDexScreenerLink(opp.sell_chain, opp.sell_dex_name, opp.sell_pair_address);

                            const riskClass = opp.risk_level ? `risk-${opp.risk_level.toLowerCase()}` : 'risk-medium';
                            const isSolana = opp.buy_chain === 'solana';
                            const chainClass = isSolana ? 'chain-solana' : '';
                            const chainEmoji = isSolana ? '☀️' : '💎';

                            html += `
                                <div class="opportunity-enhanced ${chainClass}" style="animation-delay: ${index * 0.1}s;">
                                    <div class="opportunity-header">
                                        <h3>${chainEmoji} ${opp.token_symbol} ${isSolana ? 'Solana' : 'Enhanced'} Opportunity</h3>
                                        <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                                            ${isSolana ? '<span class="solana-badge">☀️ Solana</span>' : ''}
                                            <span class="risk-badge ${opp.risk_level ? opp.risk_level.toLowerCase() : 'medium'}">${opp.risk_level || 'Medium'} Risk</span>
                                            ${opp.validation_result ? `
                                                <span class="feasibility-score feasibility-${opp.validation_result.feasibility_score >= 80 ? 'high' : opp.validation_result.feasibility_score >= 60 ? 'medium' : 'low'}">
                                                    ${opp.validation_result.feasibility_score}/100
                                                </span>
                                                <span class="validation-badge ${opp.validation_result.is_valid ? 'validation-valid' : 'validation-error'}">
                                                    ${opp.validation_result.is_valid ? '✅ Validated' : '❌ Failed'}
                                                </span>
                                            ` : ''}
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0;">
                                        <div style="background: rgba(248, 113, 113, 0.1); padding: 15px; border-radius: 12px; border-left: 3px solid #f87171;">
                                            <p><strong>🛒 Buy:</strong> ${exchangeDisplay(opp.buy_exchange)}</p>
                                            <p style="font-size: 1.2rem; color: #fca5a5; font-weight: 600; margin: 8px 0;">$${opp.buy_price}</p>
                                            <a href="${buyLink}" target="_blank" class="dex-link">📊 DexScreener</a>
                                        </div>
                                        <div style="background: rgba(52, 211, 153, 0.1); padding: 15px; border-radius: 12px; border-left: 3px solid #34d399;">
                                            <p><strong>💰 Sell:</strong> ${exchangeDisplay(opp.sell_exchange)}</p>
                                            <p style="font-size: 1.2rem; color: #a7f3d0; font-weight: 600; margin: 8px 0;">$${opp.sell_price}</p>
                                            <a href="${sellLink}" target="_blank" class="dex-link">📊 DexScreener</a>
                                        </div>
                                    </div>

                                    <div class="simulation-metrics">
                                        <div class="metric-item">
                                            <div class="metric-value">$${opp.estimated_profit_usd || 0}</div>
                                            <div class="metric-label">Estimated Profit</div>
                                        </div>
                                        <div class="metric-item">
                                            <div class="metric-value">${opp.total_slippage || 0}%</div>
                                            <div class="metric-label">Total Slippage</div>
                                        </div>
                                        <div class="metric-item">
                                            <div class="metric-value">${opp.liquidity_ratio || 0}x</div>
                                            <div class="metric-label">Liquidity Ratio</div>
                                        </div>
                                        <div class="metric-item">
                                            <div class="metric-value">$${opp.recommended_order_size || 0}</div>
                                            <div class="metric-label">Recommended Size</div>
                                        </div>
                                    </div>

                                    <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 12px; margin: 10px 0;">
                                        <p><strong>📊 Profit:</strong> <span class="profit-positive">${opp.profit_percentage}%</span></p>
                                        <p><strong>💧 Min Liquidity:</strong> $${opp.min_liquidity?.toLocaleString()}</p>
                                        <p><strong>🔗 Blockchain:</strong> ${isSolana ? '<span class="solana-highlight">☀️ Solana</span>' : opp.buy_chain}</p>
                                        <p><strong>⏰ Detected:</strong> ${new Date(opp.timestamp).toLocaleString()}</p>

                                        ${opp.token_validation ? `
                                            <div style="margin-top: 10px; padding: 8px; background: rgba(34, 197, 94, 0.2); border-radius: 8px; border-left: 3px solid #22c55e;">
                                                <p style="margin: 0; font-size: 0.9rem; color: #a7f3d0;">
                                                    <strong>✅ Token Validated:</strong> Same contract addresses confirmed
                                                </p>
                                                ${opp.token_validation.price_ratio > 2 ? `
                                                    <p style="margin: 5px 0 0 0; font-size: 0.8rem; color: #fbbf24;">
                                                        ⚠️ Price ratio: ${opp.token_validation.price_ratio}x - Verify manually
                                                    </p>
                                                ` : ''}
                                            </div>
                                        ` : ''}

                                        ${opp.risk_flags && opp.risk_flags.includes('high_price_difference') ? `
                                            <div style="margin-top: 10px; padding: 8px; background: rgba(239, 68, 68, 0.2); border-radius: 8px; border-left: 3px solid #ef4444;">
                                                <p style="margin: 0; font-size: 0.9rem; color: #fca5a5;">
                                                    <strong>⚠️ HIGH RISK:</strong> Large price difference detected
                                                </p>
                                                <p style="margin: 5px 0 0 0; font-size: 0.8rem; color: #fca5a5;">
                                                    Please verify token contracts manually before trading
                                                </p>
                                            </div>
                                        ` : ''}

                                        ${opp.validation_result ? `
                                            <div class="validation-details">
                                                <p style="margin: 0 0 8px 0; font-weight: 600; color: #3b82f6;">
                                                    🔍 Enhanced Validation Results
                                                </p>
                                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; margin: 8px 0;">
                                                    <div style="text-align: center;">
                                                        <div style="font-weight: bold; color: #fecfef;">${opp.validation_result.liquidity_depth_score}/100</div>
                                                        <div style="font-size: 0.8rem;">Liquidity Depth</div>
                                                    </div>
                                                    <div style="text-align: center;">
                                                        <div style="font-weight: bold; color: #fecfef;">${opp.validation_result.confidence_score || 0}/100</div>
                                                        <div style="font-size: 0.8rem;">Confidence</div>
                                                    </div>
                                                    <div style="text-align: center;">
                                                        <div style="font-weight: bold; color: #fecfef;">${opp.validation_result.execution_time_estimate?.toFixed(1) || 0}s</div>
                                                        <div style="font-size: 0.8rem;">Est. Execution</div>
                                                    </div>
                                                </div>
                                                ${opp.validation_result.warnings && opp.validation_result.warnings.length > 0 ? `
                                                    <ul class="validation-warning-list">
                                                        ${opp.validation_result.warnings.slice(0, 3).map(warning => `<li>${warning}</li>`).join('')}
                                                    </ul>
                                                ` : ''}
                                            </div>
                                        ` : ''}

                                        ${opp.security_data && Object.keys(opp.security_data).length > 0 ? `
                                            <div class="security-details">
                                                <p style="margin: 0 0 8px 0; font-weight: 600; color: #8b5cf6;">
                                                    🛡️ Security Analysis
                                                </p>

                                                <div class="security-badges">
                                                    <span class="security-badge goplus-${opp.security_data.security_score >= 70 ? 'high' : opp.security_data.security_score >= 50 ? 'medium' : 'low'}">
                                                        GoPlus: ${opp.security_data.security_score}/100
                                                    </span>
                                                    ${opp.security_data.quickintel_data ? `
                                                        <span class="security-badge quickintel-${opp.security_data.quickintel_data.rating?.toLowerCase() || 'unknown'}">
                                                            QuickIntel: ${opp.security_data.quickintel_data.rating || 'Unknown'}
                                                        </span>
                                                    ` : ''}
                                                </div>

                                                ${opp.security_data.is_honeypot ? `
                                                    <div class="honeypot-warning">
                                                        🚨 HONEYPOT DETECTED - DO NOT TRADE! 🚨
                                                    </div>
                                                ` : ''}

                                                ${opp.security_data.security_flags && opp.security_data.security_flags.length > 0 ? `
                                                    <div style="margin: 8px 0;">
                                                        <p style="margin: 0 0 5px 0; font-size: 0.9rem; color: #fbbf24;">Security Flags:</p>
                                                        ${opp.security_data.security_flags.slice(0, 4).map(flag => `
                                                            <span class="security-flag ${flag}">${flag.replace(/_/g, ' ')}</span>
                                                        `).join('')}
                                                    </div>
                                                ` : ''}

                                                ${opp.security_data.owner_privileges && opp.security_data.owner_privileges.length > 0 ? `
                                                    <div style="margin: 8px 0;">
                                                        <p style="margin: 0 0 5px 0; font-size: 0.9rem; color: #f87171;">Owner Privileges:</p>
                                                        <p style="font-size: 0.8rem; color: #fca5a5;">
                                                            ${opp.security_data.owner_privileges.join(', ').replace(/_/g, ' ')}
                                                        </p>
                                                    </div>
                                                ` : ''}
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            `;
                        });
                        document.getElementById('opportunities').innerHTML = html;

                        // Update enhanced summary statistics with safety info
                        updateSummaryStats(data.opportunities, data.safety_summary);

                        // Update performance metrics
                        updatePerformanceMetrics();

                        updateProgressBar(100, 'Scan completed!');

                    } else {
                        document.getElementById('opportunities').innerHTML = `
                            <div style="text-align: center; padding: 60px 20px; opacity: 0.7;">
                                <div style="font-size: 3rem; margin-bottom: 20px;">😔</div>
                                <p style="font-size: 1.2rem; margin-bottom: 10px;">Tidak ada peluang menguntungkan ditemukan</p>
                                <p style="opacity: 0.8;">Kondisi pasar mungkin tidak menguntungkan untuk arbitrase saat ini</p>
                                <button class="btn" onclick="scanOpportunities()" style="margin-top: 20px;">🔄 Pindai Lagi</button>
                            </div>
                        `;
                    }

                    // Fetch and display logs
                    await updateLogs();

                } catch (error) {
                    document.getElementById('opportunities').innerHTML = `
                        <div style="text-align: center; padding: 60px 20px;">
                            <div style="font-size: 3rem; margin-bottom: 20px;">❌</div>
                            <p style="font-size: 1.2rem; margin-bottom: 10px;">Error Pemindaian</p>
                            <p style="opacity: 0.8;">${error.message}</p>
                            <button class="btn" onclick="scanOpportunities()" style="margin-top: 20px;">🔄 Coba Lagi</button>
                        </div>
                    `;
                    addLogEntry('error', `Pemindaian gagal: ${error.message}`);
                }

                isScanning = false;
                updateScanStatus('Siap');
            }

            async function updateLogs() {
                try {
                    const response = await fetch('/api/logs');
                    const data = await response.json();

                    if (data.logs && data.logs.length > 0) {
                        const logsContainer = document.getElementById('logs-container');
                        let html = '';

                        data.logs.slice(-20).forEach(log => {
                            const time = new Date(log.timestamp).toLocaleTimeString();
                            html += `
                                <div class="log-entry ${log.level}">
                                    <span class="log-time">${time}</span>
                                    <span>${log.message}</span>
                                </div>
                            `;
                        });

                        logsContainer.innerHTML = html;
                        logsContainer.scrollTop = logsContainer.scrollHeight;
                    }
                } catch (error) {
                    console.error('Failed to update logs:', error);
                }
            }

            function addLogEntry(level, message) {
                const logsContainer = document.getElementById('logs-container');
                const time = new Date().toLocaleTimeString();

                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${level}`;
                logEntry.innerHTML = `
                    <span class="log-time">${time}</span>
                    <span>${message}</span>
                `;

                logsContainer.appendChild(logEntry);
                logsContainer.scrollTop = logsContainer.scrollHeight;
            }

            function updateScanStatus(status) {
                document.getElementById('scan-status').textContent = status;
                const indicator = document.querySelector('.status-indicator');

                if (status === 'Memindai...') {
                    indicator.className = 'status-indicator status-scanning';
                } else {
                    indicator.className = 'status-indicator status-online';
                }
            }

            // Configuration Panel Functions
            function toggleConfig() {
                const content = document.getElementById('config-content');
                const toggle = document.getElementById('config-toggle');

                if (content.classList.contains('expanded')) {
                    content.classList.remove('expanded');
                    toggle.textContent = '▼';
                } else {
                    content.classList.add('expanded');
                    toggle.textContent = '▲';
                }
            }

            function saveConfig() {
                const config = {
                    profit_min: parseFloat(document.getElementById('profit-min').value),
                    profit_max: parseFloat(document.getElementById('profit-max').value),
                    min_liquidity: parseFloat(document.getElementById('min-liquidity').value),
                    min_volume_24h: parseFloat(document.getElementById('min-volume').value),
                    enabled_chains: getEnabledChains(),
                    auto_scan_interval: parseInt(document.getElementById('auto-scan').value),
                    // Enhanced configuration
                    enabled_token_categories: getEnabledTokenCategories(),
                    simulation_capital: parseFloat(document.getElementById('simulation-capital').value),
                    max_slippage: parseFloat(document.getElementById('max-slippage').value),
                    capital_per_trade: parseFloat(document.getElementById('capital-per-trade').value),
                    min_liquidity_ratio: parseFloat(document.getElementById('min-liquidity-ratio').value)
                };

                // Validate configuration
                if (config.profit_min >= config.profit_max) {
                    alert('❌ Profit minimum harus lebih kecil dari profit maksimal');
                    return;
                }

                if (config.profit_max > 100) {
                    alert('❌ Profit maksimal tidak boleh lebih dari 100%');
                    return;
                }

                if (config.enabled_chains.length === 0) {
                    alert('❌ Pilih minimal satu blockchain');
                    return;
                }

                // Save to localStorage
                localStorage.setItem('arbitrage_config', JSON.stringify(config));

                // Send to backend
                fetch('/api/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(config)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        addLogEntry('success', '✅ Konfigurasi berhasil disimpan');
                        alert('✅ Konfigurasi berhasil disimpan!');
                    } else {
                        addLogEntry('error', '❌ Gagal menyimpan konfigurasi');
                        alert('❌ Gagal menyimpan konfigurasi');
                    }
                })
                .catch(error => {
                    addLogEntry('error', `❌ Error: ${error.message}`);
                    alert('❌ Error menyimpan konfigurasi');
                });
            }

            function resetConfig() {
                document.getElementById('profit-min').value = 0.05;
                document.getElementById('profit-max').value = 25;
                document.getElementById('min-liquidity').value = 5000;
                document.getElementById('min-volume').value = 500;
                document.getElementById('auto-scan').value = 0;

                // Reset checkboxes
                document.getElementById('chain-ethereum').checked = true;
                document.getElementById('chain-bsc').checked = true;
                document.getElementById('chain-polygon').checked = true;
                document.getElementById('chain-arbitrum').checked = true;

                // Clear localStorage
                localStorage.removeItem('arbitrage_config');

                addLogEntry('info', '🔄 Konfigurasi direset ke default');
                alert('🔄 Konfigurasi direset ke default');
            }

            function getEnabledChains() {
                const chains = [];
                if (document.getElementById('chain-ethereum').checked) chains.push('ethereum');
                if (document.getElementById('chain-bsc').checked) chains.push('bsc');
                if (document.getElementById('chain-polygon').checked) chains.push('polygon');
                if (document.getElementById('chain-arbitrum').checked) chains.push('arbitrum');
                if (document.getElementById('chain-solana').checked) chains.push('solana');
                return chains;
            }

            function loadConfig() {
                const saved = localStorage.getItem('arbitrage_config');
                if (saved) {
                    const config = JSON.parse(saved);

                    document.getElementById('profit-min').value = config.profit_min || 0.05;
                    document.getElementById('profit-max').value = config.profit_max || 25;
                    document.getElementById('min-liquidity').value = config.min_liquidity || 5000;
                    document.getElementById('min-volume').value = config.min_volume_24h || 500;
                    document.getElementById('auto-scan').value = config.auto_scan_interval || 0;

                    // Set blockchain checkboxes
                    document.getElementById('chain-ethereum').checked = config.enabled_chains.includes('ethereum');
                    document.getElementById('chain-bsc').checked = config.enabled_chains.includes('bsc');
                    document.getElementById('chain-polygon').checked = config.enabled_chains.includes('polygon');
                    document.getElementById('chain-arbitrum').checked = config.enabled_chains.includes('arbitrum');
                    document.getElementById('chain-solana').checked = config.enabled_chains.includes('solana');

                    // Set enhanced configuration
                    if (config.enabled_token_categories) {
                        document.getElementById('cat-stablecoins').checked = config.enabled_token_categories.includes('stablecoins');
                        document.getElementById('cat-blue-chips').checked = config.enabled_token_categories.includes('blue_chips');
                        document.getElementById('cat-defi').checked = config.enabled_token_categories.includes('defi');
                        document.getElementById('cat-layer1-2').checked = config.enabled_token_categories.includes('layer1_2');
                        document.getElementById('cat-meme-coins').checked = config.enabled_token_categories.includes('meme_coins');
                        document.getElementById('cat-gaming-nft').checked = config.enabled_token_categories.includes('gaming_nft');
                        document.getElementById('cat-solana-ecosystem').checked = config.enabled_token_categories.includes('solana_ecosystem');
                    }

                    // Set simulation configuration
                    document.getElementById('simulation-capital').value = config.simulation_capital || 100;
                    document.getElementById('max-slippage').value = config.max_slippage || 0.5;
                    document.getElementById('capital-per-trade').value = config.capital_per_trade || 100;
                    document.getElementById('min-liquidity-ratio').value = config.min_liquidity_ratio || 10;

                    // Update summary display
                    document.getElementById('summary-capital').textContent = `$${config.simulation_capital || 100}`;
                }
            }

            // Enhanced Functions
            function updateSummaryStats(opportunities, safetySummary = null) {
                const totalProfit = opportunities.reduce((sum, opp) => sum + (opp.estimated_profit_usd || 0), 0);
                const avgSlippage = opportunities.length > 0 ?
                    opportunities.reduce((sum, opp) => sum + (opp.total_slippage || 0), 0) / opportunities.length : 0;

                document.getElementById('total-profit').textContent = `$${totalProfit.toFixed(2)}`;
                document.getElementById('avg-slippage').textContent = `${avgSlippage.toFixed(2)}%`;

                // Update safety score
                if (safetySummary) {
                    const safetyElement = document.getElementById('safety-score');
                    const safetyScore = safetySummary.safety_score;
                    safetyElement.textContent = safetyScore;

                    // Color code safety score
                    safetyElement.style.color =
                        safetyScore === 'High' ? '#10b981' :
                        safetyScore === 'Medium' ? '#f59e0b' : '#ef4444';

                    // Show validation rate in tooltip or subtitle
                    const validationRate = safetySummary.validation_rate;
                    safetyElement.title = `${safetySummary.validated_opportunities}/${opportunities.length} validated (${validationRate}%)`;

                    // Log safety information
                    if (safetySummary.high_risk_opportunities > 0) {
                        addLogEntry('warning', `⚠️ ${safetySummary.high_risk_opportunities} opportunities flagged as HIGH RISK`);
                    }
                } else {
                    document.getElementById('safety-score').textContent = '-';
                }
            }

            // Performance Monitoring Functions
            async function updatePerformanceMetrics() {
                try {
                    const response = await fetch('/api/performance');
                    const data = await response.json();

                    if (data.status === 'success') {
                        const metrics = data.metrics;

                        document.getElementById('scan-speed').textContent = metrics.tokens_per_second.toFixed(1);
                        document.getElementById('api-response-time').textContent = `${metrics.avg_response_time}ms`;
                        document.getElementById('cache-hit-rate').textContent = `${metrics.cache_hit_rate}%`;
                        document.getElementById('success-rate').textContent = `${metrics.success_rate}%`;

                        // Color code performance metrics
                        const speedElement = document.getElementById('scan-speed');
                        speedElement.style.color = metrics.tokens_per_second > 10 ? '#10b981' :
                                                  metrics.tokens_per_second > 5 ? '#f59e0b' : '#ef4444';

                        const cacheElement = document.getElementById('cache-hit-rate');
                        cacheElement.style.color = metrics.cache_hit_rate > 70 ? '#10b981' :
                                                  metrics.cache_hit_rate > 40 ? '#f59e0b' : '#ef4444';

                        const successElement = document.getElementById('success-rate');
                        successElement.style.color = metrics.success_rate > 80 ? '#10b981' :
                                                    metrics.success_rate > 60 ? '#f59e0b' : '#ef4444';
                    }
                } catch (error) {
                    console.error('Performance metrics update error:', error);
                }
            }

            function updateProgressBar(progress, text) {
                const progressBar = document.getElementById('progress-bar');
                const progressText = document.getElementById('progress-text');

                if (progressBar && progressText) {
                    progressBar.style.width = `${progress}%`;
                    progressText.textContent = text;
                }
            }

            // Solana Statistics Functions
            async function updateSolanaStatistics() {
                try {
                    const response = await fetch('/api/solana-stats');
                    const data = await response.json();

                    if (data.status === 'success') {
                        const stats = data.solana_stats;

                        // Update Solana statistics
                        document.getElementById('solana-total-tokens').textContent = stats.total_tokens;
                        document.getElementById('solana-defi-tokens').textContent = stats.categories_breakdown.defi || 0;
                        document.getElementById('solana-gaming-tokens').textContent = stats.categories_breakdown.gaming || 0;
                        document.getElementById('solana-meme-tokens').textContent = stats.categories_breakdown.meme || 0;
                        document.getElementById('solana-opportunities').textContent = stats.current_opportunities;
                        document.getElementById('solana-scan-speed').textContent = stats.performance_metrics.scan_speed?.toFixed(1) || '0';

                        // Update priority tokens display
                        const priorityContainer = document.getElementById('solana-priority-tokens');
                        if (stats.priority_tokens && stats.priority_tokens.length > 0) {
                            priorityContainer.innerHTML = stats.priority_tokens.map(token =>
                                `<span class="token-badge">${token}</span>`
                            ).join('');
                        }

                        // Color code Solana scan speed
                        const speedElement = document.getElementById('solana-scan-speed');
                        const scanSpeed = stats.performance_metrics.scan_speed || 0;
                        speedElement.style.color = scanSpeed > 15 ? '#10b981' :
                                                  scanSpeed > 8 ? '#f59e0b' : '#ef4444';
                    }
                } catch (error) {
                    console.error('Solana statistics update error:', error);
                }
            }

            // Auto-update performance metrics every 5 seconds
            setInterval(updatePerformanceMetrics, 5000);

            // Auto-update Solana statistics every 10 seconds
            setInterval(updateSolanaStatistics, 10000);

            // Initial load of Solana statistics
            updateSolanaStatistics();

            // v3.0 Enhanced Functions
            async function updateV3Dashboard() {
                try {
                    // Update tier statistics
                    const tierResponse = await fetch('/api/v3/tier-stats');
                    const tierData = await tierResponse.json();

                    if (tierData.status === 'success') {
                        const stats = tierData.tier_statistics;

                        // Update tier counts
                        document.getElementById('v3-tier1-tokens').textContent = stats.tier_stats?.tier_1?.token_count || 0;
                        document.getElementById('v3-tier2-tokens').textContent = stats.tier_stats?.tier_2?.token_count || 0;
                        document.getElementById('v3-tier3-tokens').textContent = stats.tier_stats?.tier_3?.token_count || 0;

                        // Update total tokens
                        document.getElementById('v3-total-tokens').textContent = stats.total_tokens || '1000+';
                    }

                    // Update token distribution
                    const distResponse = await fetch('/api/v3/token-distribution');
                    const distData = await distResponse.json();

                    if (distData.status === 'success') {
                        const dist = distData.token_distribution.blockchain_distribution;

                        document.getElementById('v3-ethereum-count').textContent = dist.ethereum || 0;
                        document.getElementById('v3-solana-count').textContent = dist.solana || 0;
                        document.getElementById('v3-bsc-count').textContent = dist.bsc || 0;
                        document.getElementById('v3-polygon-count').textContent = dist.polygon || 0;
                        document.getElementById('v3-arbitrum-count').textContent = dist.arbitrum || 0;
                    }

                    // Update profit thresholds
                    const thresholdResponse = await fetch('/api/v3/profit-thresholds');
                    const thresholdData = await thresholdResponse.json();

                    if (thresholdData.status === 'success') {
                        const thresholds = thresholdData.profit_thresholds.thresholds;
                        const volatility = thresholdData.profit_thresholds.current_volatility;

                        // Update volatility display
                        document.getElementById('v3-volatility').textContent = volatility.charAt(0).toUpperCase() + volatility.slice(1);

                        // Color code volatility
                        const volatilityElement = document.getElementById('v3-volatility');
                        volatilityElement.style.color = volatility === 'low' ? '#10b981' :
                                                       volatility === 'normal' ? '#3b82f6' :
                                                       volatility === 'high' ? '#f59e0b' : '#ef4444';

                        // Update threshold displays
                        if (thresholds.stablecoins) {
                            document.getElementById('v3-stable-threshold').textContent =
                                `${thresholds.stablecoins.min.toFixed(1)}-${thresholds.stablecoins.max.toFixed(1)}%`;
                        }
                        if (thresholds.blue_chips) {
                            document.getElementById('v3-blue-threshold').textContent =
                                `${thresholds.blue_chips.min.toFixed(1)}-${thresholds.blue_chips.max.toFixed(1)}%`;
                        }
                        if (thresholds.defi) {
                            document.getElementById('v3-defi-threshold').textContent =
                                `${thresholds.defi.min.toFixed(1)}-${thresholds.defi.max.toFixed(1)}%`;
                        }
                        if (thresholds.meme_coins) {
                            document.getElementById('v3-meme-threshold').textContent =
                                `${thresholds.meme_coins.min.toFixed(1)}-${thresholds.meme_coins.max.toFixed(1)}%`;
                        }
                    }

                } catch (error) {
                    console.error('v3.0 Dashboard update error:', error);
                }
            }

            // Auto-update v3.0 dashboard every 15 seconds
            setInterval(updateV3Dashboard, 15000);

            // Initial load of v3.0 dashboard
            updateV3Dashboard();

            async function updateSimulationSummary() {
                try {
                    const response = await fetch('/api/simulation-summary');
                    const data = await response.json();

                    if (data.summary) {
                        const summary = data.summary;
                        document.getElementById('summary-capital').textContent = `$${summary.total_capital}`;
                        document.getElementById('summary-opportunities').textContent = summary.feasible_opportunities;
                        document.getElementById('summary-profit').textContent = `$${summary.total_potential_profit}`;
                        document.getElementById('summary-slippage').textContent = `${summary.average_slippage}%`;

                        alert(`💼 Simulation Summary:\n` +
                              `💰 Total Capital: $${summary.total_capital}\n` +
                              `🎯 Feasible Opportunities: ${summary.feasible_opportunities}\n` +
                              `📈 Total Potential Profit: $${summary.total_potential_profit}\n` +
                              `📉 Average Slippage: ${summary.average_slippage}%`);
                    }
                } catch (error) {
                    alert('❌ Error fetching simulation summary');
                }
            }

            async function updateSimulationConfig() {
                const capital = parseFloat(document.getElementById('simulation-capital').value);
                const maxSlippage = parseFloat(document.getElementById('max-slippage').value);
                const capitalPerTrade = parseFloat(document.getElementById('capital-per-trade').value);
                const minLiquidityRatio = parseFloat(document.getElementById('min-liquidity-ratio').value);

                try {
                    const response = await fetch('/api/config', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            simulation_capital: capital,
                            max_slippage: maxSlippage,
                            capital_per_trade: capitalPerTrade,
                            min_liquidity_ratio: minLiquidityRatio
                        })
                    });

                    if (response.ok) {
                        document.getElementById('summary-capital').textContent = `$${capital}`;
                        addLogEntry('info', '✅ Simulation configuration updated!');
                    }
                } catch (error) {
                    addLogEntry('error', '❌ Error updating simulation configuration');
                }
            }

            function getEnabledTokenCategories() {
                const categories = [];
                if (document.getElementById('cat-stablecoins').checked) categories.push('stablecoins');
                if (document.getElementById('cat-blue-chips').checked) categories.push('blue_chips');
                if (document.getElementById('cat-defi').checked) categories.push('defi');
                if (document.getElementById('cat-layer1-2').checked) categories.push('layer1_2');
                if (document.getElementById('cat-meme-coins').checked) categories.push('meme_coins');
                if (document.getElementById('cat-gaming-nft').checked) categories.push('gaming_nft');
                if (document.getElementById('cat-solana-ecosystem').checked) categories.push('solana_ecosystem');
                return categories;
            }

            // Author Modal Functions
            function showAuthorModal() {
                document.getElementById('author-modal').style.display = 'flex';
            }

            function hideAuthorModal() {
                document.getElementById('author-modal').style.display = 'none';
            }

            // Close modal when clicking outside
            document.addEventListener('click', function(event) {
                const modal = document.getElementById('author-modal');
                if (event.target === modal) {
                    hideAuthorModal();
                }
            });

            async function checkStatus() {
                try {
                    const response = await fetch('/api/status');
                    const data = await response.json();

                    const statusInfo = `
                        ✅ Status: ${data.status}
                        📊 Versi: ${data.version}
                        🔄 Mode: ${data.mode}
                        💰 Peluang: ${data.opportunities_count}
                        🕒 Scan Terakhir: ${data.last_scan ? new Date(data.last_scan).toLocaleString() : 'Belum pernah'}
                        ⏰ Waktu Sekarang: ${new Date().toLocaleString()}
                    `;

                    alert(statusInfo);
                } catch (error) {
                    alert('❌ Error: ' + error.message);
                }
            }

            function clearLogs() {
                document.getElementById('logs-container').innerHTML = `
                    <div class="log-entry info">
                        <span class="log-time">Sekarang</span>
                        <span>🗑️ Log dibersihkan</span>
                    </div>
                `;
            }

            function viewDocs() {
                window.open('/docs', '_blank');
            }

            // Auto-update logs every 5 seconds
            setInterval(updateLogs, 5000);

            // Load saved configuration on page load
            document.addEventListener('DOMContentLoaded', function() {
                loadConfig();
            });

            // Initial log
            addLogEntry('info', '🚀 UI dimuat dan siap untuk pemindaian');
        </script>
    </body>
    </html>
    """, status_code=200)

@app.get("/api/status")
async def get_status():
    """Get application status"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "mode": "public_apis_only",
        "timestamp": datetime.now().isoformat(),
        "last_scan": detector.last_scan,
        "opportunities_count": len(detector.opportunities)
    }

@app.get("/api/scan")
async def scan_opportunities():
    """Scan for arbitrage opportunities with enhanced safety validation"""
    try:
        opportunities = await detector.scan_opportunities()
        safety_summary = detector.create_safe_opportunity_summary(opportunities)

        return {
            "status": "success",
            "opportunities": opportunities,
            "count": len(opportunities),
            "timestamp": datetime.now().isoformat(),
            "safety_summary": safety_summary
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/opportunities")
async def get_opportunities():
    """Get current opportunities"""
    return {
        "status": "success",
        "opportunities": detector.opportunities,
        "count": len(detector.opportunities),
        "last_scan": detector.last_scan
    }

@app.get("/api/performance")
async def get_performance_metrics():
    """Get real-time performance metrics"""
    try:
        metrics = detector.scan_metrics

        return {
            "status": "success",
            "metrics": {
                "total_tokens": metrics.total_tokens,
                "tokens_per_second": round(metrics.tokens_per_second, 2),
                "avg_response_time": round(sum(metrics.api_response_times) / len(metrics.api_response_times) * 1000, 1) if metrics.api_response_times else 0,
                "max_response_time": round(max(metrics.api_response_times) * 1000, 1) if metrics.api_response_times else 0,
                "cache_hit_rate": round(detector.smart_cache.get_hit_rate(), 1),
                "success_rate": round(metrics.success_rate, 1),
                "scan_duration": round(metrics.scan_duration, 2),
                "errors_count": metrics.errors_count,
                "cache_size": len(detector.smart_cache.cache)
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "error",
            "metrics": {
                "total_tokens": 0,
                "tokens_per_second": 0,
                "avg_response_time": 0,
                "max_response_time": 0,
                "cache_hit_rate": 0,
                "success_rate": 0,
                "scan_duration": 0,
                "errors_count": 0,
                "cache_size": 0
            },
            "error": str(e)
        }

@app.get("/api/logs")
async def get_logs():
    """Get scan logs"""
    return {
        "status": "success",
        "logs": detector.scan_logs,
        "count": len(detector.scan_logs)
    }

@app.get("/api/solana-stats")
async def get_solana_statistics():
    """Get comprehensive Solana ecosystem statistics"""
    try:
        # Get Solana token categories
        solana_subcategories = detector.get_solana_token_subcategories()
        priority_tokens = detector.get_priority_solana_tokens()

        # Count Solana opportunities
        solana_opportunities = [
            opp for opp in detector.opportunities
            if opp.get('buy_chain', '').lower() == 'solana' or
               opp.get('sell_chain', '').lower() == 'solana'
        ]

        # Calculate Solana-specific metrics
        total_solana_tokens = len(detector.token_categories.get("solana_ecosystem", {}).get("tokens", []))

        return {
            "status": "success",
            "solana_stats": {
                "total_tokens": total_solana_tokens,
                "priority_tokens_count": len(priority_tokens),
                "subcategories": {
                    category: len(tokens) for category, tokens in solana_subcategories.items()
                },
                "current_opportunities": len(solana_opportunities),
                "priority_tokens": priority_tokens[:10],  # Top 10 priority tokens
                "categories_breakdown": {
                    "defi": len(solana_subcategories.get("defi", [])),
                    "gaming": len(solana_subcategories.get("gaming", [])),
                    "meme": len(solana_subcategories.get("meme", [])),
                    "infrastructure": len(solana_subcategories.get("infrastructure", [])),
                    "liquid_staking": len(solana_subcategories.get("liquid_staking", []))
                },
                "performance_metrics": {
                    "cache_hit_rate": detector.smart_cache.get_hit_rate(),
                    "scan_speed": detector.scan_metrics.tokens_per_second,
                    "success_rate": detector.scan_metrics.success_rate
                }
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "solana_stats": {
                "total_tokens": 0,
                "priority_tokens_count": 0,
                "subcategories": {},
                "current_opportunities": 0,
                "priority_tokens": [],
                "categories_breakdown": {},
                "performance_metrics": {}
            }
        }

@app.get("/api/dexscreener-link")
async def get_dexscreener_link(chain: str, dex: str, pair_address: str = None):
    """Get DexScreener link for a specific DEX pair"""
    link = detector.get_dexscreener_link(chain, dex, pair_address)
    return {
        "status": "success",
        "link": link,
        "chain": chain,
        "dex": dex
    }


@app.get("/api/config")
async def get_config():
    """Get current configuration"""
    return {
        "status": "success",
        "config": detector.config
    }


@app.post("/api/config")
async def update_config(config_data: dict):
    """Update scanning configuration"""
    try:
        # Validate configuration
        valid_keys = [
            "profit_min", "profit_max", "min_liquidity", "min_volume_24h",
            "enabled_chains", "auto_scan_interval", "enabled_token_categories",
            "max_tokens_per_category", "simulation_capital", "max_slippage",
            "capital_per_trade", "min_liquidity_ratio", "risk_tolerance"
        ]
        filtered_config = {k: v for k, v in config_data.items() if k in valid_keys}

        # Update configuration
        detector.update_config(filtered_config)

        return {
            "status": "success",
            "message": "Konfigurasi berhasil diperbarui",
            "config": detector.config
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error updating config: {str(e)}")


@app.get("/api/token-categories")
async def get_token_categories():
    """Get available token categories"""
    return {
        "status": "success",
        "categories": detector.token_categories,
        "chains": detector.supported_chains
    }


@app.get("/api/simulation-summary")
async def get_simulation_summary():
    """Get simulation summary statistics"""
    try:
        opportunities = detector.opportunities
        config = detector.config

        if not opportunities:
            return {
                "status": "success",
                "summary": {
                    "total_capital": config["simulation_capital"],
                    "feasible_opportunities": 0,
                    "total_potential_profit": 0,
                    "average_slippage": 0,
                    "risk_distribution": {"Low": 0, "Medium": 0, "High": 0}
                }
            }

        # Calculate summary statistics
        total_capital = config["simulation_capital"]
        feasible_count = len(opportunities)
        total_potential_profit = sum(opp.get("estimated_profit_usd", 0) for opp in opportunities)
        average_slippage = sum(opp.get("total_slippage", 0) for opp in opportunities) / feasible_count if feasible_count > 0 else 0

        # Risk distribution
        risk_distribution = {"Low": 0, "Medium": 0, "High": 0}
        for opp in opportunities:
            risk_level = opp.get("risk_level", "High")
            risk_distribution[risk_level] += 1

        return {
            "status": "success",
            "summary": {
                "total_capital": total_capital,
                "feasible_opportunities": feasible_count,
                "total_potential_profit": round(total_potential_profit, 2),
                "average_slippage": round(average_slippage, 3),
                "risk_distribution": risk_distribution,
                "capital_utilization": round((total_potential_profit / total_capital) * 100, 2) if total_capital > 0 else 0
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating summary: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            
            if data == "ping":
                await websocket.send_text("pong")
            elif data.startswith("scan"):
                opportunities = await detector.scan_opportunities()
                await websocket.send_text(json.dumps({
                    "type": "opportunities",
                    "data": opportunities
                }))
                
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)

# v3.0 ENHANCED API Endpoints

@app.get("/api/v3/tier-stats")
async def get_tier_statistics():
    """Get comprehensive tier-based scanning statistics"""
    try:
        tier_stats = detector.tiered_scanner.get_tier_statistics()

        return {
            "status": "success",
            "version": "3.0",
            "tier_statistics": tier_stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "tier_statistics": {}
        }

@app.get("/api/v3/profit-thresholds")
async def get_profit_thresholds():
    """Get dynamic profit thresholds with volatility adjustments"""
    try:
        thresholds = detector.profit_threshold_manager.get_all_thresholds()

        return {
            "status": "success",
            "version": "3.0",
            "profit_thresholds": thresholds,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "profit_thresholds": {}
        }

@app.get("/api/v3/token-distribution")
async def get_token_distribution():
    """Get comprehensive token distribution across blockchains"""
    try:
        # Get current enabled tokens
        enabled_tokens = await detector.get_enabled_tokens_expanded()
        distribution = detector._analyze_token_distribution(enabled_tokens)

        # Get tier distribution
        tier_distribution = {}
        for tier in [1, 2, 3]:
            tier_tokens = detector.tiered_scanner.get_tokens_for_tier(tier)
            tier_distribution[f'tier_{tier}'] = len(tier_tokens)

        return {
            "status": "success",
            "version": "3.0",
            "token_distribution": {
                "total_tokens": len(enabled_tokens),
                "blockchain_distribution": distribution,
                "tier_distribution": tier_distribution,
                "target_distribution": {
                    "ethereum": 300,
                    "solana": 250,
                    "bsc": 200,
                    "polygon": 150,
                    "arbitrum": 100
                }
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "token_distribution": {}
        }

@app.get("/api/v3/performance-metrics")
async def get_v3_performance_metrics():
    """Get comprehensive v3.0 performance metrics"""
    try:
        # Check if detector exists and has required attributes
        if not hasattr(detector, 'scan_metrics'):
            return {
                "status": "error",
                "error": "Detector not properly initialized - scan_metrics missing",
                "performance_metrics": {}
            }

        # Get existing performance metrics
        base_metrics = detector.scan_metrics

        # Get v3.0 specific metrics with error handling
        tier_stats = {}
        threshold_stats = {}
        discovery_stats = {}

        try:
            tier_stats = detector.tiered_scanner.get_tier_statistics()
        except Exception as e:
            tier_stats = {"error": f"Tier stats error: {e}"}

        try:
            threshold_stats = detector.profit_threshold_manager.get_threshold_stats()
        except Exception as e:
            threshold_stats = {"error": f"Threshold stats error: {e}"}

        try:
            discovery_stats = detector.dynamic_discovery.get_discovery_stats()
        except Exception as e:
            discovery_stats = {"error": f"Discovery stats error: {e}"}

        # Check v3_config with detailed debugging
        v3_config_exists = hasattr(detector, 'v3_config')
        v3_config_content = getattr(detector, 'v3_config', {})

        return {
            "status": "success",
            "version": "3.0",
            "performance_metrics": {
                "scan_performance": {
                    "total_tokens": getattr(base_metrics, 'total_tokens', 0),
                    "tokens_per_second": round(getattr(base_metrics, 'tokens_per_second', 0), 2),
                    "cache_hit_rate": round(detector.smart_cache.get_hit_rate(), 1) if hasattr(detector, 'smart_cache') else 0,
                    "success_rate": round(getattr(base_metrics, 'success_rate', 0), 1),
                    "scan_duration": round(getattr(base_metrics, 'scan_duration', 0), 2)
                },
                "tier_performance": tier_stats,
                "threshold_performance": threshold_stats,
                "discovery_performance": discovery_stats,
                "v3_features": {
                    "dynamic_discovery_enabled": v3_config_content.get('enable_dynamic_discovery', False),
                    "tier_management_enabled": v3_config_content.get('enable_tier_management', False),
                    "adaptive_thresholds_enabled": v3_config_content.get('enable_adaptive_thresholds', False),
                    "enable_demo_mode": v3_config_content.get('enable_demo_mode', False),
                    "max_total_tokens": v3_config_content.get('max_total_tokens', 0),
                    "v3_config_exists": v3_config_exists,
                    "v3_config_content": v3_config_content,
                    "detector_attributes": [attr for attr in dir(detector) if not attr.startswith('_')][:10]  # Debug info
                }
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        import traceback
        return {
            "status": "error",
            "error": str(e),
            "traceback": traceback.format_exc(),
            "performance_metrics": {}
        }

@app.get("/api/v3/api-health")
async def get_api_health():
    """Get comprehensive API health status and data quality metrics"""
    try:
        # Get API health from multi-API provider
        api_health = detector.multi_api_provider.get_api_health_status()

        # Calculate overall health score
        healthy_count = sum(1 for api_status in api_health['apis'].values() if api_status['status'] == 'healthy')
        total_count = len(api_health['apis'])
        health_percentage = (healthy_count / total_count * 100) if total_count > 0 else 0

        # Get data quality metrics
        data_quality = {
            "validation_enabled": True,
            "known_price_ranges": len(detector.multi_api_provider.data_validators.known_price_ranges),
            "multi_source_consolidation": True,
            "fallback_mechanisms": True
        }

        return {
            "status": "success",
            "api_health": {
                "overall_health": f"{health_percentage:.1f}%",
                "healthy_apis": healthy_count,
                "total_apis": total_count,
                "apis": api_health['apis'],
                "last_check": api_health['last_check']
            },
            "data_quality": data_quality,
            "multi_api_features": {
                "dexscreener_enhanced": True,
                "coingecko_integration": True,
                "1inch_integration": True,
                "cross_validation": True,
                "source_prioritization": True
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "api_health": {}
        }

# ===== INTEGRATED FLASK WEB INTERFACE =====

if FLASK_AVAILABLE:

    # Enhanced Logging dengan Real-time WebSocket
    def add_web_log(message, level="info"):
        """Enhanced logging dengan real-time WebSocket broadcast"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = {
            'timestamp': timestamp,
            'message': message,
            'level': level,
            'full_text': f"[{timestamp}] {message}"
        }
        web_app_state['logs'].appendleft(log_entry)
        print(log_entry['full_text'])

        # Broadcast ke semua connected clients
        if socketio:
            socketio.emit('new_log', log_entry)

    def update_web_progress(percentage, action, tokens_scanned=0, total_tokens=0, api_calls=0, opportunities=0):
        """Update progress dengan real-time broadcast"""
        web_app_state['progress'].update({
            'percentage': percentage,
            'current_action': action,
            'tokens_scanned': tokens_scanned,
            'total_tokens': total_tokens,
            'api_calls_made': api_calls,
            'opportunities_found': opportunities
        })

        # Calculate tokens per second dan estimated time
        if web_app_state['statistics']['start_time']:
            elapsed = time.time() - web_app_state['statistics']['start_time']
            if elapsed > 0:
                web_app_state['progress']['tokens_per_second'] = round(tokens_scanned / elapsed, 2)

                if tokens_scanned > 0 and total_tokens > tokens_scanned:
                    remaining_tokens = total_tokens - tokens_scanned
                    time_per_token = elapsed / tokens_scanned
                    web_app_state['progress']['estimated_time_remaining'] = round(remaining_tokens * time_per_token)

        # Broadcast progress update
        if socketio:
            socketio.emit('progress_update', web_app_state['progress'])

    # WebSocket Event Handlers
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection"""
        add_web_log("🔗 Client terhubung ke WebSocket", "info")
        emit('status_update', {
            'status': web_app_state['status'],
            'is_running': web_app_state['is_running'],
            'parameters': web_app_state['bot_parameters'],
            'statistics': web_app_state['statistics']
        })

    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection"""
        add_web_log("🔌 Client terputus dari WebSocket", "info")

    @socketio.on('update_parameters')
    def handle_parameter_update(data):
        """Handle real-time parameter updates"""
        try:
            parameter_type = data.get('type')
            parameter_key = data.get('key')
            parameter_value = data.get('value')

            if parameter_type == 'profit_threshold':
                category = data.get('category')
                threshold_type = data.get('threshold_type')  # 'min' or 'max'
                web_app_state['bot_parameters']['profit_thresholds'][category][threshold_type] = float(parameter_value)

            elif parameter_type == 'scan_interval':
                web_app_state['bot_parameters']['scan_intervals'][parameter_key] = int(parameter_value)

            elif parameter_type == 'blockchain':
                web_app_state['bot_parameters']['blockchain_selection'][parameter_key] = bool(parameter_value)

            elif parameter_type == 'general':
                web_app_state['bot_parameters'][parameter_key] = parameter_value

            add_web_log(f"⚙️ Parameter diperbarui: {parameter_type}.{parameter_key} = {parameter_value}", "success")

            # Broadcast parameter update to all clients
            socketio.emit('parameter_updated', {
                'type': parameter_type,
                'key': parameter_key,
                'value': parameter_value,
                'parameters': web_app_state['bot_parameters']
            })

        except Exception as e:
            add_web_log(f"❌ Error updating parameter: {e}", "error")

    @socketio.on('start_bot')
    def handle_start_bot():
        """Handle bot start request"""
        if not web_app_state['is_running']:
            start_web_arbitrage_bot()
        else:
            add_web_log("⚠️ Bot sudah berjalan", "warning")

    @socketio.on('stop_bot')
    def handle_stop_bot():
        """Handle bot stop request"""
        if web_app_state['is_running']:
            stop_web_arbitrage_bot()
        else:
            add_web_log("⚠️ Bot tidak sedang berjalan", "warning")

    # Bot Control Functions
    def start_web_arbitrage_bot():
        """Start the arbitrage bot with enhanced progress tracking"""
        if web_app_state['is_running']:
            add_web_log("⚠️ Bot sudah berjalan", "warning")
            return

        web_app_state['is_running'] = True
        web_app_state['status'] = 'Memulai bot...'
        web_app_state['statistics']['start_time'] = time.time()
        web_app_state['scan_count'] = 0

        add_web_log("🚀 Memulai Enhanced Crypto Arbitrage Bot v3.0", "success")
        update_web_progress(0, "Inisialisasi bot...")

        # Start bot in separate thread
        bot_thread = threading.Thread(target=run_web_arbitrage_bot, daemon=True)
        bot_thread.start()

    def stop_web_arbitrage_bot():
        """Stop the arbitrage bot"""
        web_app_state['is_running'] = False
        web_app_state['status'] = 'Menghentikan bot...'

        add_web_log("🛑 Bot dihentikan", "warning")
        update_web_progress(0, "Bot dihentikan")

        # Calculate final statistics
        if web_app_state['statistics']['start_time']:
            web_app_state['statistics']['uptime_seconds'] = int(time.time() - web_app_state['statistics']['start_time'])

    def run_web_arbitrage_bot():
        """Main bot execution loop with enhanced progress tracking"""
        try:
            while web_app_state['is_running']:
                update_web_progress(10, "Memulai scan arbitrase...")

                # Run the main detector scan
                asyncio.run(detector.scan_for_arbitrage_opportunities())

                if not web_app_state['is_running']:
                    break

                # Get opportunities from detector
                opportunities = detector.opportunities[-10:]  # Latest 10
                web_app_state['opportunities'].extend(opportunities)

                # Keep only latest 50 opportunities
                web_app_state['opportunities'] = web_app_state['opportunities'][-50:]

                # Broadcast new opportunities
                if opportunities and socketio:
                    socketio.emit('new_opportunities', opportunities)

                web_app_state['scan_count'] += 1
                web_app_state['statistics']['total_scans'] += 1
                web_app_state['statistics']['total_opportunities'] += len(opportunities)

                update_web_progress(100, f"Scan selesai - {len(opportunities)} peluang ditemukan")
                add_web_log(f"✅ Scan #{web_app_state['scan_count']} selesai - {len(opportunities)} peluang ditemukan", "success")

                # Wait before next scan
                for i in range(30):
                    if not web_app_state['is_running']:
                        break
                    update_web_progress(100, f"Menunggu scan berikutnya... {30-i}s")
                    time.sleep(1)

        except Exception as e:
            add_web_log(f"❌ Error dalam bot execution: {e}", "error")
        finally:
            web_app_state['is_running'] = False
            web_app_state['status'] = 'Idle'

    # Flask Routes
    @app.route('/')
    def index():
        """Main dashboard with dark futuristic UI"""
        return render_template_string(DARK_FUTURISTIC_HTML_TEMPLATE)

    @app.route('/api/status')
    def api_get_status():
        """Get current bot status for v3.0 UI"""
        return jsonify({
            'status': web_app_state['status'],
            'is_running': web_app_state['is_running'],
            'opportunities': web_app_state['opportunities'][-10:],  # Latest 10
            'scan_count': web_app_state['scan_count'],
            'last_scan_time': web_app_state['last_scan_time'],
            'logs': list(web_app_state['logs'])[-20:],  # Latest 20 logs
            'parameters': web_app_state['bot_parameters'],
            'statistics': web_app_state['statistics'],
            'progress': web_app_state['progress']
        })

    @app.route('/api/start', methods=['POST'])
    def api_start_bot():
        """Start the arbitrage bot"""
        if not web_app_state['is_running']:
            start_web_arbitrage_bot()
            return jsonify({'success': True, 'message': 'Bot started'})
        else:
            return jsonify({'success': False, 'message': 'Bot already running'})

    @app.route('/api/stop', methods=['POST'])
    def api_stop_bot():
        """Stop the arbitrage bot"""
        if web_app_state['is_running']:
            stop_web_arbitrage_bot()
            return jsonify({'success': True, 'message': 'Bot stopped'})
        else:
            return jsonify({'success': False, 'message': 'Bot not running'})

    @app.route('/api/opportunities')
    def api_get_opportunities():
        """Get current arbitrage opportunities"""
        return jsonify({
            'opportunities': web_app_state['opportunities'],
            'count': len(web_app_state['opportunities'])
        })

    @app.route('/api/logs')
    def api_get_logs():
        """Get recent logs"""
        return jsonify({
            'logs': list(web_app_state['logs'])
        })

    @app.route('/api/parameters', methods=['GET', 'POST'])
    def api_handle_parameters():
        """Get or update bot parameters"""
        if request.method == 'GET':
            return jsonify(web_app_state['bot_parameters'])
        else:
            # Update parameters
            data = request.get_json()
            if data:
                # Update specific parameter
                param_type = data.get('type')
                param_key = data.get('key')
                param_value = data.get('value')

                if param_type == 'profit_threshold':
                    category = data.get('category')
                    threshold_type = data.get('threshold_type')
                    web_app_state['bot_parameters']['profit_thresholds'][category][threshold_type] = float(param_value)
                elif param_type == 'scan_interval':
                    web_app_state['bot_parameters']['scan_intervals'][param_key] = int(param_value)
                elif param_type == 'blockchain':
                    web_app_state['bot_parameters']['blockchain_selection'][param_key] = bool(param_value)
                elif param_type == 'general':
                    web_app_state['bot_parameters'][param_key] = param_value

                add_web_log(f"⚙️ Parameter updated: {param_type}.{param_key} = {param_value}", "success")

                return jsonify({'success': True, 'parameters': web_app_state['bot_parameters']})

            return jsonify({'success': False, 'message': 'Invalid data'})

    # Dark Futuristic HTML Template
    DARK_FUTURISTIC_HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Enhanced Crypto Arbitrage Bot v3.0 - BOBACHEESE</title>

    <!-- Futuristic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;800&display=swap" rel="stylesheet">

    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>

    <!-- Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
            --bg-tertiary: #2a2a2a;
            --accent-cyan: #00ffff;
            --accent-purple: #8a2be2;
            --accent-green: #00ff41;
            --accent-red: #ff073a;
            --accent-orange: #ff8c00;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --text-muted: #666666;
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --shadow-glow: 0 0 20px rgba(0, 255, 255, 0.3);
            --shadow-purple: 0 0 20px rgba(138, 43, 226, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Exo 2', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            overflow-x: hidden;
            min-height: 100vh;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 255, 65, 0.05) 0%, transparent 50%);
        }

        .cyberpunk-grid {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: -1;
        }

        .header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow-glow);
        }

        .header h1 {
            font-family: 'Orbitron', monospace;
            font-weight: 900;
            font-size: 2rem;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
            animation: glow-pulse 2s ease-in-out infinite alternate;
        }

        @keyframes glow-pulse {
            from { filter: brightness(1); }
            to { filter: brightness(1.2); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .glass-panel {
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow-glow);
            transition: all 0.3s ease;
        }

        .glass-panel:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-purple);
        }

        .panel-title {
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: var(--accent-cyan);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--accent-green);
            animation: pulse 1.5s ease-in-out infinite;
        }

        .status-indicator.idle {
            background: var(--text-muted);
            animation: none;
        }

        .status-indicator.running {
            background: var(--accent-green);
        }

        .status-indicator.error {
            background: var(--accent-red);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .control-section {
            grid-column: 1 / -1;
        }

        .control-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .cyber-button {
            font-family: 'Orbitron', monospace;
            font-weight: 600;
            padding: 0.8rem 2rem;
            border: 2px solid var(--accent-cyan);
            background: transparent;
            color: var(--accent-cyan);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .cyber-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .cyber-button:hover::before {
            left: 100%;
        }

        .cyber-button:hover {
            background: rgba(0, 255, 255, 0.1);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .cyber-button.start {
            border-color: var(--accent-green);
            color: var(--accent-green);
        }

        .cyber-button.start:hover {
            background: rgba(0, 255, 65, 0.1);
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
        }

        .cyber-button.stop {
            border-color: var(--accent-red);
            color: var(--accent-red);
        }

        .cyber-button.stop:hover {
            background: rgba(255, 7, 58, 0.1);
            box-shadow: 0 0 20px rgba(255, 7, 58, 0.5);
        }

        .cyber-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .simple-panel {
            background: var(--bg-secondary);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .simple-button {
            background: var(--accent-cyan);
            color: var(--bg-primary);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .simple-button:hover {
            background: var(--accent-purple);
            transform: translateY(-2px);
        }

        .simple-button:disabled {
            background: var(--text-muted);
            cursor: not-allowed;
            transform: none;
        }

        .logs-container {
            max-height: 300px;
            overflow-y: auto;
            background: var(--bg-secondary);
            border-radius: 10px;
            border: 1px solid var(--glass-border);
            padding: 1rem;
            font-family: 'Courier New', monospace;
        }

        .log-entry {
            padding: 0.3rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            font-size: 0.9rem;
            animation: fadeInUp 0.3s ease;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .log-time {
            color: var(--text-muted);
            margin-right: 0.5rem;
        }

        .log-message {
            color: var(--text-primary);
        }

        .log-entry.info .log-message {
            color: var(--accent-cyan);
        }

        .log-entry.success .log-message {
            color: var(--accent-green);
        }

        .log-entry.warning .log-message {
            color: var(--accent-orange);
        }

        .log-entry.error .log-message {
            color: var(--accent-red);
        }

        .opportunities-container {
            max-height: 400px;
            overflow-y: auto;
            padding: 1rem 0;
        }

        .opportunity-card {
            background: var(--bg-secondary);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .opportunity-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-green), var(--accent-cyan));
        }

        .opportunity-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
        }

        .no-opportunities {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
        }

        .pulse-icon {
            font-size: 3rem;
            animation: pulse 2s ease-in-out infinite;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                padding: 1rem;
            }

            .control-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="cyberpunk-grid"></div>

    <header class="header">
        <h1>🚀 Enhanced Crypto Arbitrage Bot v3.0</h1>
        <div style="text-align: center; margin-top: 0.5rem; font-family: 'Orbitron', monospace; color: var(--accent-purple);">
            by BOBACHEESE | Advanced Multi-Chain Arbitrage Detection
        </div>
    </header>

    <div class="container">
        <!-- Control Section -->
        <div class="glass-panel control-section">
            <div class="panel-title">
                <span class="status-indicator" id="statusIndicator"></span>
                🎮 Bot Control Center
            </div>

            <div class="control-buttons">
                <button class="cyber-button start" id="startBtn" onclick="startBot()">
                    ▶️ Start Bot
                </button>
                <button class="cyber-button stop" id="stopBtn" onclick="stopBot()" disabled>
                    ⏹️ Stop Bot
                </button>
                <button class="cyber-button" onclick="resetStats()">
                    🔄 Reset Stats
                </button>
            </div>

            <!-- Simple Progress Display -->
            <div class="simple-panel">
                <h4>📊 Bot Status</h4>
                <div id="currentAction">Menunggu...</div>
                <div>Scan Count: <span id="scanCount">0</span></div>
                <div>Opportunities: <span id="opportunitiesCount">0</span></div>
            </div>
        </div>

        <!-- Arbitrage Opportunities Display -->
        <div class="glass-panel">
            <div class="panel-title">💎 Live Arbitrage Opportunities</div>

            <div class="opportunities-container" id="opportunitiesContainer">
                <div class="no-opportunities">
                    <div class="pulse-icon">🔍</div>
                    <p>Menunggu peluang arbitrase...</p>
                </div>
            </div>
        </div>

        <!-- Real-time Logs -->
        <div class="glass-panel" style="grid-column: 1 / -1;">
            <div class="panel-title">📋 Real-time Logs</div>

            <div class="logs-container" id="logsContainer">
                <div class="log-entry info">
                    <span class="log-time">[00:00:00]</span>
                    <span class="log-message">🚀 Enhanced Crypto Arbitrage Bot v3.0 siap digunakan</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple WebSocket connection (fallback to polling if SocketIO not available)
        let socket = null;
        let isRunning = false;

        try {
            socket = io();

            socket.on('connect', function() {
                console.log('Connected to WebSocket');
                addLogEntry('🔗 Terhubung ke server', 'info');
            });

            socket.on('disconnect', function() {
                console.log('Disconnected from WebSocket');
                addLogEntry('🔌 Terputus dari server', 'warning');
            });

            socket.on('new_log', function(logData) {
                addLogEntry(logData.message, logData.level);
            });

            socket.on('new_opportunities', function(opportunities) {
                displayOpportunities(opportunities);
            });
        } catch (e) {
            console.log('SocketIO not available, using polling');
            // Fallback to polling
            setInterval(updateStatus, 5000);
        }

        // Bot Control Functions
        function startBot() {
            if (!isRunning) {
                if (socket) {
                    socket.emit('start_bot');
                } else {
                    fetch('/api/start', { method: 'POST' })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateButtonStates(true);
                                addLogEntry('🚀 Memulai bot...', 'info');
                            }
                        });
                }
                updateButtonStates(true);
                addLogEntry('🚀 Memulai bot...', 'info');
            }
        }

        function stopBot() {
            if (isRunning) {
                if (socket) {
                    socket.emit('stop_bot');
                } else {
                    fetch('/api/stop', { method: 'POST' })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateButtonStates(false);
                                addLogEntry('🛑 Menghentikan bot...', 'warning');
                            }
                        });
                }
                updateButtonStates(false);
                addLogEntry('🛑 Menghentikan bot...', 'warning');
            }
        }

        function resetStats() {
            document.getElementById('scanCount').textContent = '0';
            document.getElementById('opportunitiesCount').textContent = '0';

            const container = document.getElementById('opportunitiesContainer');
            container.innerHTML = `
                <div class="no-opportunities">
                    <div class="pulse-icon">🔍</div>
                    <p>Menunggu peluang arbitrase...</p>
                </div>
            `;

            addLogEntry('🔄 Statistik direset', 'info');
        }

        function updateButtonStates(running) {
            isRunning = running;
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const statusIndicator = document.getElementById('statusIndicator');

            if (running) {
                startBtn.disabled = true;
                stopBtn.disabled = false;
                statusIndicator.className = 'status-indicator running';
            } else {
                startBtn.disabled = false;
                stopBtn.disabled = true;
                statusIndicator.className = 'status-indicator idle';
            }
        }

        function displayOpportunities(opportunities) {
            const container = document.getElementById('opportunitiesContainer');

            if (opportunities.length === 0) {
                container.innerHTML = `
                    <div class="no-opportunities">
                        <div class="pulse-icon">🔍</div>
                        <p>Menunggu peluang arbitrase...</p>
                    </div>
                `;
                return;
            }

            let html = '';
            opportunities.forEach(opp => {
                html += `
                    <div class="opportunity-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <div style="font-weight: 700; color: var(--accent-cyan);">${opp.token_symbol || 'Unknown'}</div>
                            <div style="background: linear-gradient(45deg, var(--accent-green), var(--accent-cyan)); color: white; padding: 0.3rem 0.8rem; border-radius: 20px; font-weight: 600;">
                                ${opp.profit_percentage || 0}%
                            </div>
                        </div>
                        <div style="font-size: 0.9rem; color: var(--text-secondary);">
                            Buy: $${opp.buy_price || 0} | Sell: $${opp.sell_price || 0}
                        </div>
                        <div style="font-size: 0.8rem; color: var(--text-muted); margin-top: 0.5rem;">
                            ${opp.buy_exchange || 'Unknown'} → ${opp.sell_exchange || 'Unknown'}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;

            // Update count
            document.getElementById('opportunitiesCount').textContent = opportunities.length;
        }

        function addLogEntry(message, level = 'info') {
            const logsContainer = document.getElementById('logsContainer');
            const timestamp = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.innerHTML = `
                <span class="log-time">[${timestamp}]</span>
                <span class="log-message">${message}</span>
            `;

            // Insert at the beginning
            logsContainer.insertBefore(logEntry, logsContainer.firstChild);

            // Keep only latest 100 logs
            while (logsContainer.children.length > 100) {
                logsContainer.removeChild(logsContainer.lastChild);
            }
        }

        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('currentAction').textContent = data.status || 'Idle';
                    document.getElementById('scanCount').textContent = data.scan_count || 0;

                    if (data.opportunities && data.opportunities.length > 0) {
                        displayOpportunities(data.opportunities);
                    }

                    if (data.logs && data.logs.length > 0) {
                        // Update logs if needed
                    }
                })
                .catch(e => console.error('Status update failed:', e));
        }

        // Initialize UI on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateButtonStates(false);
            addLogEntry('🚀 Enhanced Crypto Arbitrage Bot v3.0 siap digunakan', 'info');
            addLogEntry('💡 Gunakan panel kontrol untuk memulai scanning', 'info');
            addLogEntry('🔍 Bot akan mencari peluang arbitrase dengan validasi pair yang ketat', 'info');

            // Start status polling if no WebSocket
            if (!socket) {
                setInterval(updateStatus, 5000);
            }
        });
    </script>
</body>
</html>
    """

# Initialize the detector globally
detector = AdvancedArbitrageDetector()

if __name__ == "__main__":
    print("🚀 Starting Enhanced Crypto Arbitrage Bot v3.0")
    print("📊 Features: 1000+ Tokens, Intelligent Tiers, Dynamic Thresholds, Advanced Security")
    print("💎 Pair Validation System prevents false arbitrage signals")
    print("🌐 Dark Futuristic Web Interface with Real-time Controls")
    print("=" * 80)

    if FLASK_AVAILABLE:
        print("🌐 Web interface: http://localhost:5000")
        print("📱 Mobile-responsive dark futuristic UI")
        print("⚙️ Real-time parameter controls via WebSocket")
        print("🔍 Live arbitrage opportunity tracking")
        print("=" * 80)

        # Add initial log entries
        add_web_log("🚀 Enhanced Crypto Arbitrage Bot v3.0 initialized", "success")
        add_web_log("💎 Pair validation system active - zero false signals", "info")
        add_web_log("🌐 1000+ tokens across 5 major blockchains", "info")
        add_web_log("⚙️ Real-time parameter controls ready", "info")

        try:
            # Start with SocketIO support
            socketio.run(app, debug=False, host='0.0.0.0', port=5000, allow_unsafe_werkzeug=True)
        except Exception as e:
            print(f"⚠️ SocketIO failed: {e}")
            print("🔄 Falling back to standard Flask...")
            app.run(host='0.0.0.0', port=5000, debug=False)
    else:
        print("⚠️ Flask not available - running console mode only")
        print("💡 Install Flask for web interface: pip install Flask flask-socketio eventlet")
        print("🔄 Starting console-based arbitrage detection...")

        # Run console mode
        async def console_mode():
            while True:
                try:
                    print("\n" + "="*60)
                    print("🔍 Starting arbitrage scan...")
                    await detector.scan_for_arbitrage_opportunities()

                    if detector.opportunities:
                        print(f"✅ Found {len(detector.opportunities)} opportunities!")
                        for i, opp in enumerate(detector.opportunities[-5:], 1):  # Show last 5
                            print(f"  {i}. {opp.get('token_symbol', 'Unknown')} - {opp.get('profit_percentage', 0)}% profit")
                    else:
                        print("📊 No arbitrage opportunities found in this scan")

                    print("⏳ Waiting 60 seconds before next scan...")
                    await asyncio.sleep(60)

                except KeyboardInterrupt:
                    print("\n🛑 Stopping bot...")
                    break
                except Exception as e:
                    print(f"❌ Error: {e}")
                    await asyncio.sleep(30)

        asyncio.run(console_mode())
