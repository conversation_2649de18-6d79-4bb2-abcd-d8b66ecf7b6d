# 🌐 Public APIs Guide - No API Keys Required

This guide explains how the Crypto Arbitrage Bot v2.0 uses only **public APIs** without requiring any API keys or authentication.

## 🎯 Overview

The bot has been completely redesigned to work exclusively with public endpoints, making it:
- ✅ **Zero setup** - No API key registration required
- ✅ **Free to use** - All endpoints are publicly accessible
- ✅ **Privacy-focused** - No personal data or authentication needed
- ✅ **Rate-limited** - Built-in respect for API limits

## 📊 Data Sources

### 1. 🦎 CoinGecko Public API

**Base URL**: `https://api.coingecko.com/api/v3`
**Rate Limit**: 10-30 requests/minute (free tier)
**Documentation**: https://www.coingecko.com/en/api/documentation

#### Endpoints Used:
- `GET /simple/price` - Current token prices
- `GET /coins/list` - List of all supported coins
- `GET /coins/{id}` - Detailed coin information

#### Example Usage:
```python
# Get Bitcoin price
response = await coingecko_client.get_price(["bitcoin"], ["usd"])
# Returns: {"bitcoin": {"usd": 45000, "usd_24h_change": 2.5}}
```

### 2. 📊 DexScreener Public API

**Base URL**: `https://api.dexscreener.com/latest`
**Rate Limit**: 60 requests/minute
**Documentation**: https://docs.dexscreener.com/api/reference

#### Endpoints Used:
- `GET /dex/tokens/{tokenAddress}` - Get pairs for token
- `GET /dex/search/?q={query}` - Search trading pairs
- `GET /dex/pairs/{chainId}/{pairId}` - Get specific pair data

#### Example Usage:
```python
# Search for USDC pairs
response = await dexscreener_client.search_pairs("USDC")
# Returns: {"pairs": [...]} with liquidity, volume, price data
```

### 3. 🔶 Binance Public API

**Base URL**: `https://api.binance.com/api/v3`
**Rate Limit**: 1200 requests/minute
**Documentation**: https://developers.binance.com/docs/binance-spot-api-docs/rest-api

#### Endpoints Used:
- `GET /ticker/24hr` - 24hr ticker statistics
- `GET /depth` - Order book data
- `GET /exchangeInfo` - Exchange trading rules

#### Example Usage:
```python
# Get BTCUSDT ticker
response = await binance_client.get_ticker_24hr("BTCUSDT")
# Returns: {"symbol": "BTCUSDT", "lastPrice": "45000", ...}
```

### 4. 💬 Reddit Public API

**Base URL**: `https://www.reddit.com`
**Rate Limit**: 60 requests/minute (conservative)
**Documentation**: https://www.reddit.com/dev/api/

#### Endpoints Used:
- `GET /r/{subreddit}/hot.json` - Hot posts from subreddit
- `GET /r/{subreddit}/search.json` - Search posts in subreddit
- `GET /search.json` - Global search

#### Example Usage:
```python
# Get crypto sentiment from r/cryptocurrency
response = await reddit_client.get_subreddit_posts("cryptocurrency", "hot", 25)
# Returns: {"data": {"children": [...]}} with post data
```

## 🔧 Implementation Details

### Rate Limiting Strategy

Each API client implements intelligent rate limiting:

```python
class RateLimiter:
    def __init__(self, requests_per_minute: int = 30):
        self.requests_per_minute = requests_per_minute
        self.tokens = requests_per_minute
        self.last_update = time.time()
    
    async def wait_for_token(self):
        """Wait until a token is available"""
        while not await self.acquire():
            await asyncio.sleep(1)
```

### Caching Strategy

All responses are cached to minimize API calls:

```python
# Cache configuration per API
CACHE_SETTINGS = {
    "coingecko": 300,      # 5 minutes
    "dexscreener": 120,    # 2 minutes  
    "binance": 60,         # 1 minute
    "reddit": 600          # 10 minutes
}
```

### Error Handling

Robust error handling with fallbacks:

```python
async def _make_request(self, endpoint: str, params: Dict = None):
    try:
        response = await self.client.get(url, params=params)
        if response.status_code == 429:  # Rate limited
            await asyncio.sleep(60)
            raise Exception("Rate limited")
        return response.json()
    except Exception as e:
        logger.error(f"API request failed: {e}")
        raise
```

## 🚀 Getting Started

### 1. Installation

```bash
# Clone repository
git clone <repository-url>
cd crypto-arbitrage-bot

# Install dependencies
pip install -r requirements.txt

# No API key setup required!
```

### 2. Configuration

Update `.env` file:

```bash
# No API keys needed - all public endpoints
USE_PUBLIC_APIS_ONLY=true

# Database and cache settings
DATABASE_URL="sqlite:///./arbitrage.db"
REDIS_URL="redis://localhost:6379/0"
```

### 3. Run the Application

```bash
# Start the backend
python main.py

# Or with Docker
docker-compose up -d
```

### 4. Access the Application

- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 📈 Features Available

### ✅ Arbitrage Detection
- Multi-chain price comparison
- DEX vs CEX arbitrage opportunities
- Real-time opportunity scoring

### ✅ ML-Enhanced Analysis
- Price prediction using historical data
- Sentiment analysis from Reddit
- Pattern recognition for technical analysis

### ✅ Risk Assessment
- Liquidity analysis from order books
- Slippage calculations
- MEV risk evaluation

### ✅ Real-time Updates
- WebSocket connections for live data
- Push notifications for opportunities
- Auto-refresh dashboards

## 🔒 Rate Limit Management

### Conservative Limits
- **CoinGecko**: 10 requests/minute (well below 30/min limit)
- **DexScreener**: 60 requests/minute (at limit)
- **Binance**: 300 requests/minute (well below 1200/min limit)
- **Reddit**: 60 requests/minute (conservative)

### Automatic Backoff
- Exponential backoff on rate limit errors
- Intelligent request queuing
- Cache-first strategy to minimize requests

## 🛠️ Troubleshooting

### Common Issues

1. **Rate Limiting**
   ```
   Error: Rate limited by [API]
   Solution: Wait 60 seconds, rate limiter will auto-retry
   ```

2. **No Data Available**
   ```
   Error: No opportunities found
   Solution: Check if tokens exist on tracked exchanges
   ```

3. **Connection Issues**
   ```
   Error: API request failed
   Solution: Check internet connection and API status
   ```

### Debug Mode

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 API Response Examples

### Arbitrage Opportunity
```json
{
  "id": "uuid-here",
  "token_symbol": "USDC",
  "buy_exchange": "uniswap_v3",
  "sell_exchange": "binance",
  "buy_price": 0.9995,
  "sell_price": 1.0005,
  "profit_percentage": 0.1,
  "profit_usd": 10.0,
  "liquidity_usd": 100000,
  "confidence_score": 0.85,
  "ml_score": {
    "total_score": 1.2,
    "sentiment_factor": 1.1,
    "prediction_factor": 1.0,
    "risk_factor": 0.9
  }
}
```

### Sentiment Analysis
```json
{
  "token_symbol": "BTC",
  "overall_sentiment": 0.3,
  "sentiment_label": "bullish",
  "confidence": 0.75,
  "total_posts": 45,
  "positive_posts": 25,
  "negative_posts": 10,
  "neutral_posts": 10,
  "data_source": "reddit_public"
}
```

## 🎯 Best Practices

1. **Respect Rate Limits**: Built-in rate limiting prevents API abuse
2. **Cache Aggressively**: Reduce API calls with intelligent caching
3. **Handle Errors Gracefully**: Fallback mechanisms for API failures
4. **Monitor Usage**: Track API call patterns and optimize
5. **Stay Updated**: Monitor API documentation for changes

## 🔮 Future Enhancements

- **Additional Public APIs**: Integration with more free data sources
- **Enhanced Caching**: Redis-based distributed caching
- **API Monitoring**: Real-time API health monitoring
- **Fallback Sources**: Multiple data sources for redundancy

---

**🎉 Enjoy using the Crypto Arbitrage Bot v2.0 with zero API key requirements!**
