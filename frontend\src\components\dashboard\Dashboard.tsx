import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Card, { CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import Button from '@components/ui/Button'
import Badge from '@components/ui/Badge'
import OpportunityCard from '@components/arbitrage/OpportunityCard'
import { ArbitrageOpportunity, DashboardStats, FilterOptions } from '@types/index'
import { formatCurrency, formatPercentage, formatNumber } from '@utils/formatters'
import {
  PlayIcon,
  PauseIcon,
  AdjustmentsHorizontalIcon,
  ChartBarIcon,
  CpuChipIcon,
  GlobeAltIcon,
  BanknotesIcon,
  TrendingUpIcon,
  ClockIcon,
  FunnelIcon
} from '@heroicons/react/24/outline'

interface DashboardProps {
  opportunities: ArbitrageOpportunity[]
  isScanning: boolean
  stats: DashboardStats
  onStartScan: () => void
  onStopScan: () => void
  onConfigureScan: () => void
  onSelectOpportunity: (opportunity: ArbitrageOpportunity) => void
}

const Dashboard: React.FC<DashboardProps> = ({
  opportunities,
  isScanning,
  stats,
  onStartScan,
  onStopScan,
  onConfigureScan,
  onSelectOpportunity
}) => {
  const [filters, setFilters] = useState<FilterOptions>({
    chains: [],
    min_profit: 0.5,
    max_profit: 100,
    min_liquidity: 1000,
    risk_levels: [],
    opportunity_types: [],
    sort_by: 'profit_percentage',
    sort_order: 'desc'
  })

  const [showFilters, setShowFilters] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  // Filter and sort opportunities
  const filteredOpportunities = React.useMemo(() => {
    let filtered = opportunities.filter(opp => {
      // Profit filter
      if (opp.profit_percentage < filters.min_profit || opp.profit_percentage > filters.max_profit) {
        return false
      }

      // Liquidity filter
      const minLiquidity = Math.min(opp.buy_liquidity_usd || 0, opp.sell_liquidity_usd || 0)
      if (minLiquidity < filters.min_liquidity) {
        return false
      }

      // Chain filter
      if (filters.chains.length > 0 && !filters.chains.includes(opp.chain_id)) {
        return false
      }

      // Type filter
      if (filters.opportunity_types.length > 0 && !filters.opportunity_types.includes(opp.type)) {
        return false
      }

      return true
    })

    // Sort opportunities
    filtered.sort((a, b) => {
      let aValue: number
      let bValue: number

      switch (filters.sort_by) {
        case 'profit_percentage':
          aValue = a.profit_percentage
          bValue = b.profit_percentage
          break
        case 'ml_score':
          aValue = a.ml_score?.total_score || 0
          bValue = b.ml_score?.total_score || 0
          break
        case 'liquidity':
          aValue = Math.min(a.buy_liquidity_usd || 0, a.sell_liquidity_usd || 0)
          bValue = Math.min(b.buy_liquidity_usd || 0, b.sell_liquidity_usd || 0)
          break
        case 'discovered_at':
          aValue = new Date(a.discovered_at).getTime()
          bValue = new Date(b.discovered_at).getTime()
          break
        default:
          aValue = a.profit_percentage
          bValue = b.profit_percentage
      }

      return filters.sort_order === 'desc' ? bValue - aValue : aValue - bValue
    })

    return filtered
  }, [opportunities, filters])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-tertiary">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-dark-900/80 backdrop-blur-lg border-b border-dark-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gradient">
                Crypto Arbitrage Bot
              </h1>
              <Badge variant="gradient" glow>
                v2.0
              </Badge>
            </div>

            <div className="flex items-center space-x-3">
              <Button
                variant={isScanning ? 'error' : 'success'}
                onClick={isScanning ? onStopScan : onStartScan}
                icon={isScanning ? <PauseIcon className="w-4 h-4" /> : <PlayIcon className="w-4 h-4" />}
                glow={isScanning}
              >
                {isScanning ? 'Stop Scanning' : 'Start Scanning'}
              </Button>

              <Button
                variant="outline"
                onClick={onConfigureScan}
                icon={<AdjustmentsHorizontalIcon className="w-4 h-4" />}
              >
                Configure
              </Button>

              <Button
                variant="ghost"
                onClick={() => setShowFilters(!showFilters)}
                icon={<FunnelIcon className="w-4 h-4" />}
              >
                Filters
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8"
        >
          {/* Stats Grid */}
          <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card variant="glass" glow>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Total Opportunities</p>
                    <p className="text-2xl font-bold text-white">{stats.total_opportunities}</p>
                  </div>
                  <ChartBarIcon className="w-8 h-8 text-neon-blue" />
                </div>
              </CardContent>
            </Card>

            <Card variant="glass" glow>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Avg Profit</p>
                    <p className="text-2xl font-bold text-success">
                      {formatPercentage(stats.avg_profit_percentage)}
                    </p>
                  </div>
                  <TrendingUpIcon className="w-8 h-8 text-success" />
                </div>
              </CardContent>
            </Card>

            <Card variant="glass" glow>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Volume Analyzed</p>
                    <p className="text-2xl font-bold text-neon-cyan">
                      {formatCurrency(stats.total_volume_analyzed, 'USD', true)}
                    </p>
                  </div>
                  <BanknotesIcon className="w-8 h-8 text-neon-cyan" />
                </div>
              </CardContent>
            </Card>

            <Card variant="glass" glow>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Active Chains</p>
                    <p className="text-2xl font-bold text-neon-purple">{stats.active_chains}</p>
                  </div>
                  <GlobeAltIcon className="w-8 h-8 text-neon-purple" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Scanning Status */}
          {isScanning && (
            <motion.div variants={itemVariants}>
              <Card variant="neon" glow>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-success rounded-full animate-pulse" />
                      <span className="text-white font-medium">Scanning for opportunities...</span>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <div className="flex items-center space-x-1">
                        <ClockIcon className="w-4 h-4" />
                        <span>Real-time</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <CpuChipIcon className="w-4 h-4" />
                        <span>ML Enhanced</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Filters Panel */}
          {showFilters && (
            <motion.div
              variants={itemVariants}
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <Card variant="glass">
                <CardHeader>
                  <CardTitle>Filter Opportunities</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                    <div>
                      <label className="block text-sm text-gray-400 mb-2">Min Profit %</label>
                      <input
                        type="number"
                        value={filters.min_profit}
                        onChange={(e) => setFilters(prev => ({ ...prev, min_profit: parseFloat(e.target.value) || 0 }))}
                        className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white focus:border-neon-blue focus:outline-none"
                        step="0.1"
                        min="0"
                      />
                    </div>

                    <div>
                      <label className="block text-sm text-gray-400 mb-2">Max Profit %</label>
                      <input
                        type="number"
                        value={filters.max_profit}
                        onChange={(e) => setFilters(prev => ({ ...prev, max_profit: parseFloat(e.target.value) || 100 }))}
                        className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white focus:border-neon-blue focus:outline-none"
                        step="0.1"
                        min="0"
                      />
                    </div>

                    <div>
                      <label className="block text-sm text-gray-400 mb-2">Min Liquidity</label>
                      <input
                        type="number"
                        value={filters.min_liquidity}
                        onChange={(e) => setFilters(prev => ({ ...prev, min_liquidity: parseFloat(e.target.value) || 0 }))}
                        className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white focus:border-neon-blue focus:outline-none"
                        step="1000"
                        min="0"
                      />
                    </div>

                    <div>
                      <label className="block text-sm text-gray-400 mb-2">Sort By</label>
                      <select
                        value={filters.sort_by}
                        onChange={(e) => setFilters(prev => ({ ...prev, sort_by: e.target.value as any }))}
                        className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white focus:border-neon-blue focus:outline-none"
                      >
                        <option value="profit_percentage">Profit %</option>
                        <option value="ml_score">ML Score</option>
                        <option value="liquidity">Liquidity</option>
                        <option value="discovered_at">Time</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm text-gray-400 mb-2">Order</label>
                      <select
                        value={filters.sort_order}
                        onChange={(e) => setFilters(prev => ({ ...prev, sort_order: e.target.value as any }))}
                        className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white focus:border-neon-blue focus:outline-none"
                      >
                        <option value="desc">Highest First</option>
                        <option value="asc">Lowest First</option>
                      </select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Opportunities Grid */}
          <motion.div variants={itemVariants}>
            <Card variant="glass">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>
                    Arbitrage Opportunities ({filteredOpportunities.length})
                  </CardTitle>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={viewMode === 'grid' ? 'primary' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                    >
                      Grid
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'primary' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                    >
                      List
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {filteredOpportunities.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                      {isScanning ? 'Scanning for opportunities...' : 'No opportunities found'}
                    </div>
                    {!isScanning && (
                      <Button variant="primary" onClick={onStartScan}>
                        Start Scanning
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className={
                    viewMode === 'grid' 
                      ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                      : 'space-y-4'
                  }>
                    {filteredOpportunities.map((opportunity, index) => (
                      <motion.div
                        key={opportunity.id || index}
                        variants={itemVariants}
                        transition={{ delay: index * 0.05 }}
                      >
                        <OpportunityCard
                          opportunity={opportunity}
                          onSelect={onSelectOpportunity}
                          compact={viewMode === 'list'}
                        />
                      </motion.div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}

export default Dashboard
