"""
API routes for arbitrage operations
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from backend.services.arbitrage_engine import arbitrage_engine
from backend.services.data_aggregator import data_aggregator
from backend.services.risk_validator import opportunity_validator
from backend.services.chain_manager import chain_manager
from backend.services.cex_integration import cex_manager
from backend.core.logging import get_logger
from backend.core.websocket import manager as ws_manager

logger = get_logger(__name__)
router = APIRouter()


class ScanConfig(BaseModel):
    """Configuration for arbitrage scanning"""
    networks: List[str] = Field(default=["ethereum", "bsc", "polygon"], description="Blockchain networks to scan")
    min_profit_percentage: float = Field(default=0.5, ge=0.1, le=100.0, description="Minimum profit percentage")
    max_profit_percentage: float = Field(default=50.0, ge=1.0, le=1000.0, description="Maximum profit percentage")
    min_liquidity_usd: float = Field(default=10000.0, ge=1000.0, description="Minimum liquidity in USD")
    min_volume_24h: float = Field(default=50000.0, ge=1000.0, description="Minimum 24h volume in USD")
    max_slippage_percentage: float = Field(default=2.0, ge=0.1, le=10.0, description="Maximum acceptable slippage")
    include_cex_arbitrage: bool = Field(default=True, description="Include CEX vs DEX arbitrage")
    include_cross_chain: bool = Field(default=False, description="Include cross-chain arbitrage")
    max_execution_time_minutes: int = Field(default=60, ge=5, le=300, description="Maximum execution time")
    amount_usd: float = Field(default=1000.0, ge=100.0, le=100000.0, description="Trade amount for calculations")


class ValidationConfig(BaseModel):
    """Configuration for opportunity validation"""
    max_slippage_percentage: float = Field(default=2.0, ge=0.1, le=10.0)
    min_liquidity_usd: float = Field(default=10000.0, ge=1000.0)
    max_gas_cost_percentage: float = Field(default=1.0, ge=0.1, le=5.0)
    max_mev_risk_level: str = Field(default="medium", regex="^(low|medium|high|very_high)$")


@router.post("/scan/start")
async def start_arbitrage_scan(
    config: ScanConfig,
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """Start arbitrage scanning session"""
    try:
        # Validate networks
        supported_networks = chain_manager.get_supported_chains()
        invalid_networks = [net for net in config.networks if net not in supported_networks]
        
        if invalid_networks:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported networks: {invalid_networks}. Supported: {supported_networks}"
            )
        
        # Start scanning session
        session_id = await arbitrage_engine.start_scanning(config.dict())
        
        # Start background scanning task
        background_tasks.add_task(
            _background_scan_task,
            session_id,
            config.dict()
        )
        
        return {
            "status": "success",
            "session_id": session_id,
            "message": "Arbitrage scanning started",
            "config": config.dict()
        }
        
    except Exception as e:
        logger.error(f"Error starting arbitrage scan: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/scan/stop")
async def stop_arbitrage_scan() -> Dict[str, Any]:
    """Stop current arbitrage scanning session"""
    try:
        await arbitrage_engine.stop_scanning()
        
        return {
            "status": "success",
            "message": "Arbitrage scanning stopped"
        }
        
    except Exception as e:
        logger.error(f"Error stopping arbitrage scan: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/opportunities")
async def get_opportunities(
    limit: int = 50,
    min_profit: float = 0.5,
    sort_by: str = "profit_percentage"
) -> Dict[str, Any]:
    """Get current arbitrage opportunities"""
    try:
        opportunities = arbitrage_engine.opportunities
        
        # Filter by minimum profit
        filtered_opportunities = [
            opp for opp in opportunities
            if opp.get("profit_percentage", 0) >= min_profit
        ]
        
        # Sort opportunities
        if sort_by == "profit_percentage":
            filtered_opportunities.sort(
                key=lambda x: x.get("profit_percentage", 0), 
                reverse=True
            )
        elif sort_by == "ml_score":
            filtered_opportunities.sort(
                key=lambda x: x.get("ml_score", {}).get("total_score", 0), 
                reverse=True
            )
        
        # Limit results
        limited_opportunities = filtered_opportunities[:limit]
        
        return {
            "status": "success",
            "opportunities": limited_opportunities,
            "total_count": len(filtered_opportunities),
            "returned_count": len(limited_opportunities),
            "session_id": arbitrage_engine.session_id,
            "is_scanning": arbitrage_engine.is_running
        }
        
    except Exception as e:
        logger.error(f"Error getting opportunities: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/opportunities/{opportunity_id}/validate")
async def validate_opportunity(
    opportunity_id: str,
    validation_config: Optional[ValidationConfig] = None
) -> Dict[str, Any]:
    """Validate a specific arbitrage opportunity"""
    try:
        # Find opportunity by ID (in real implementation, this would be stored with IDs)
        opportunities = arbitrage_engine.opportunities
        opportunity = None
        
        for i, opp in enumerate(opportunities):
            if str(i) == opportunity_id:  # Simple ID mapping for demo
                opportunity = opp
                break
        
        if not opportunity:
            raise HTTPException(status_code=404, detail="Opportunity not found")
        
        # Validate opportunity
        config = validation_config.dict() if validation_config else {}
        validation_result = await opportunity_validator.validate_opportunity(
            opportunity, config
        )
        
        return {
            "status": "success",
            "opportunity_id": opportunity_id,
            "validation_result": validation_result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating opportunity: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/chains")
async def get_supported_chains() -> Dict[str, Any]:
    """Get supported blockchain networks"""
    try:
        chains = chain_manager.get_active_chains()
        
        chain_info = []
        for chain in chains:
            chain_info.append({
                "chain_id": chain.chain_id,
                "name": chain.name,
                "chain_type": chain.chain_type.value,
                "native_token": chain.native_token,
                "explorer_url": chain.explorer_url,
                "major_dexes": chain.major_dexes,
                "avg_block_time_seconds": chain.avg_block_time_seconds,
                "avg_gas_price_gwei": chain.avg_gas_price_gwei
            })
        
        return {
            "status": "success",
            "supported_chains": chain_info,
            "total_count": len(chain_info)
        }
        
    except Exception as e:
        logger.error(f"Error getting supported chains: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/chains/{chain_id}/gas")
async def get_chain_gas_prices(chain_id: str) -> Dict[str, Any]:
    """Get current gas prices for a specific chain"""
    try:
        if chain_id not in chain_manager.get_supported_chains():
            raise HTTPException(status_code=404, detail="Chain not supported")
        
        gas_estimate = await chain_manager.estimate_gas_cost(chain_id, "swap")
        
        return {
            "status": "success",
            "chain_id": chain_id,
            "gas_estimate": gas_estimate
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting gas prices for {chain_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status")
async def get_arbitrage_status() -> Dict[str, Any]:
    """Get current arbitrage engine status"""
    try:
        return {
            "status": "success",
            "engine_status": {
                "is_running": arbitrage_engine.is_running,
                "session_id": arbitrage_engine.session_id,
                "opportunities_count": len(arbitrage_engine.opportunities),
                "supported_networks": chain_manager.get_supported_chains(),
                "websocket_connections": ws_manager.get_connection_stats()["total_connections"]
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting arbitrage status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _background_scan_task(session_id: str, config: Dict[str, Any]):
    """Background task for continuous arbitrage scanning"""
    try:
        logger.info(f"Starting background scan task for session {session_id}")
        
        # Initialize services
        await data_aggregator.initialize()
        await cex_manager.initialize()
        
        scan_count = 0
        
        while arbitrage_engine.is_running:
            scan_count += 1
            logger.info(f"Starting scan cycle {scan_count} for session {session_id}")
            
            try:
                # Scan for opportunities
                opportunities = await arbitrage_engine.scan_for_opportunities(config)
                
                # Broadcast updates via WebSocket
                await ws_manager.broadcast_to_topic({
                    "type": "opportunities_update",
                    "session_id": session_id,
                    "scan_count": scan_count,
                    "opportunities": opportunities[:10],  # Send top 10
                    "total_count": len(opportunities),
                    "timestamp": datetime.utcnow().isoformat()
                }, "arbitrage_updates")
                
                logger.info(f"Scan cycle {scan_count} completed. Found {len(opportunities)} opportunities")
                
            except Exception as e:
                logger.error(f"Error in scan cycle {scan_count}: {e}")
                
                # Broadcast error
                await ws_manager.broadcast_to_topic({
                    "type": "scan_error",
                    "session_id": session_id,
                    "scan_count": scan_count,
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }, "arbitrage_updates")
            
            # Wait for next scan cycle
            import asyncio
            await asyncio.sleep(config.get("scan_interval", 60))
        
        logger.info(f"Background scan task completed for session {session_id}")
        
    except Exception as e:
        logger.error(f"Error in background scan task: {e}")
    finally:
        # Ensure engine is stopped
        await arbitrage_engine.stop_scanning()
