"""
Configuration settings for the Crypto Arbitrage Bot
"""
from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application
    APP_NAME: str = "Crypto Arbitrage Bot"
    APP_VERSION: str = "2.0.0"
    DEBUG: bool = False
    
    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    RELOAD: bool = False
    
    # Database
    DATABASE_URL: str = "postgresql+asyncpg://user:password@localhost/arbitrage_bot"
    DATABASE_ECHO: bool = False
    
    # Redis Cache
    REDIS_URL: str = "redis://localhost:6379/0"
    CACHE_TTL: int = 300  # 5 minutes
    
    # API Keys
    COINGECKO_API_KEY: Optional[str] = None
    DEXTOOLS_API_KEY: Optional[str] = None
    MORALIS_API_KEY: Optional[str] = None
    BINANCE_API_KEY: Optional[str] = None
    BINANCE_SECRET_KEY: Optional[str] = None
    COINBASE_API_KEY: Optional[str] = None
    COINBASE_SECRET_KEY: Optional[str] = None
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # seconds
    
    # Scanning Configuration
    DEFAULT_SCAN_INTERVAL: int = 60  # seconds
    MAX_CONCURRENT_REQUESTS: int = 10
    REQUEST_TIMEOUT: int = 30
    
    # Arbitrage Settings
    MIN_PROFIT_THRESHOLD: float = 0.5  # 0.5%
    MAX_PROFIT_THRESHOLD: float = 100.0  # 100%
    MIN_LIQUIDITY_USD: float = 1000.0
    MIN_VOLUME_24H: float = 10000.0
    
    # Supported Networks
    SUPPORTED_NETWORKS: List[str] = [
        "ethereum", "bsc", "polygon", "arbitrum", "avalanche", "solana"
    ]
    
    # WebSocket
    WS_HEARTBEAT_INTERVAL: int = 30
    WS_MAX_CONNECTIONS: int = 100
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Machine Learning
    ML_MODEL_UPDATE_INTERVAL: int = 3600  # 1 hour
    SENTIMENT_ANALYSIS_ENABLED: bool = True
    PATTERN_RECOGNITION_ENABLED: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
