"""
Data aggregator service that combines multiple API sources
"""
import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from backend.services.api_client import APIClientFactory
from backend.core.logging import get_logger
from backend.core.cache import cache
from backend.config.settings import settings
import json

logger = get_logger(__name__)


class DataAggregator:
    """Aggregates data from multiple API sources with fallback mechanisms"""
    
    def __init__(self):
        self.clients = {}
        self.fallback_order = {
            "price_data": ["dexscreener", "coingecko", "binance"],
            "token_info": ["coingecko", "dexscreener"],
            "market_data": ["binance", "coingecko", "dexscreener"],
            "trending": ["coingecko", "dexscreener"]
        }
    
    async def initialize(self):
        """Initialize all API clients"""
        try:
            self.clients["dexscreener"] = await APIClientFactory.get_client("dexscreener")
            self.clients["coingecko"] = await APIClientFactory.get_client("coingecko")
            self.clients["binance"] = await APIClientFactory.get_client("binance")
            logger.info("Data aggregator initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing data aggregator: {e}")
            raise
    
    async def get_comprehensive_token_data(
        self, 
        token_symbol: str, 
        token_address: Optional[str] = None,
        chain_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get comprehensive token data from multiple sources"""
        
        cache_key = f"comprehensive_token:{token_symbol}:{token_address}:{chain_id}"
        cached_data = await cache.get(cache_key)
        if cached_data:
            return cached_data
        
        token_data = {
            "symbol": token_symbol,
            "address": token_address,
            "chain_id": chain_id,
            "sources": {},
            "aggregated": {},
            "last_updated": datetime.utcnow().isoformat()
        }
        
        # Gather data from all sources concurrently
        tasks = []
        
        # DexScreener data
        if token_address:
            tasks.append(self._get_dexscreener_data(token_address))
        else:
            tasks.append(self._search_dexscreener_data(token_symbol))
        
        # CoinGecko data
        tasks.append(self._get_coingecko_data(token_symbol))
        
        # Binance data (if applicable)
        binance_symbol = f"{token_symbol}USDT"
        tasks.append(self._get_binance_data(binance_symbol))
        
        # Execute all tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        source_names = ["dexscreener", "coingecko", "binance"]
        for i, result in enumerate(results):
            if isinstance(result, dict) and result:
                token_data["sources"][source_names[i]] = result
            elif isinstance(result, Exception):
                logger.warning(f"Error getting data from {source_names[i]}: {result}")
        
        # Aggregate data from all sources
        token_data["aggregated"] = self._aggregate_token_data(token_data["sources"])
        
        # Cache the result
        await cache.set(cache_key, token_data, ttl=300)  # Cache for 5 minutes
        
        return token_data
    
    async def _get_dexscreener_data(self, token_address: str) -> Dict[str, Any]:
        """Get data from DexScreener"""
        try:
            client = self.clients["dexscreener"]
            pairs = await client.get_token_pairs(token_address)
            
            if not pairs:
                return {}
            
            # Process and aggregate pair data
            total_liquidity = sum(pair.get("liquidity", {}).get("usd", 0) for pair in pairs)
            total_volume_24h = sum(pair.get("volume", {}).get("h24", 0) for pair in pairs)
            
            # Get best price (highest liquidity pair)
            best_pair = max(pairs, key=lambda p: p.get("liquidity", {}).get("usd", 0))
            
            return {
                "pairs_count": len(pairs),
                "total_liquidity": total_liquidity,
                "total_volume_24h": total_volume_24h,
                "best_price": best_pair.get("priceUsd"),
                "best_pair": best_pair,
                "all_pairs": pairs
            }
        except Exception as e:
            logger.error(f"Error getting DexScreener data: {e}")
            return {}
    
    async def _search_dexscreener_data(self, token_symbol: str) -> Dict[str, Any]:
        """Search DexScreener by symbol"""
        try:
            client = self.clients["dexscreener"]
            pairs = await client.search_pairs(token_symbol)
            
            if not pairs:
                return {}
            
            # Filter pairs for the exact symbol
            exact_pairs = [
                pair for pair in pairs
                if pair.get("baseToken", {}).get("symbol", "").lower() == token_symbol.lower()
            ]
            
            if not exact_pairs:
                return {}
            
            return await self._process_dexscreener_pairs(exact_pairs)
        except Exception as e:
            logger.error(f"Error searching DexScreener data: {e}")
            return {}
    
    async def _process_dexscreener_pairs(self, pairs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process DexScreener pairs data"""
        total_liquidity = sum(pair.get("liquidity", {}).get("usd", 0) for pair in pairs)
        total_volume_24h = sum(pair.get("volume", {}).get("h24", 0) for pair in pairs)
        
        # Get best pair by liquidity
        best_pair = max(pairs, key=lambda p: p.get("liquidity", {}).get("usd", 0))
        
        # Calculate average price change
        price_changes = [pair.get("priceChange", {}).get("h24", 0) for pair in pairs if pair.get("priceChange", {}).get("h24") is not None]
        avg_price_change = sum(price_changes) / len(price_changes) if price_changes else 0
        
        return {
            "pairs_count": len(pairs),
            "total_liquidity": total_liquidity,
            "total_volume_24h": total_volume_24h,
            "best_price": best_pair.get("priceUsd"),
            "avg_price_change_24h": avg_price_change,
            "best_pair": best_pair,
            "all_pairs": pairs
        }
    
    async def _get_coingecko_data(self, token_symbol: str) -> Dict[str, Any]:
        """Get data from CoinGecko"""
        try:
            client = self.clients["coingecko"]
            
            # Search for the token
            search_result = await client.search_coins(token_symbol)
            if not search_result or not search_result.get("coins"):
                return {}
            
            # Find best match
            coins = search_result["coins"]
            best_match = None
            for coin in coins:
                if coin.get("symbol", "").lower() == token_symbol.lower():
                    best_match = coin
                    break
            
            if not best_match:
                return {}
            
            # Get detailed coin data
            coin_id = best_match["id"]
            coin_data = await client.get_coin_data(coin_id)
            
            if not coin_data:
                return {}
            
            market_data = coin_data.get("market_data", {})
            
            return {
                "coin_id": coin_id,
                "name": coin_data.get("name"),
                "symbol": coin_data.get("symbol"),
                "current_price": market_data.get("current_price", {}).get("usd"),
                "market_cap": market_data.get("market_cap", {}).get("usd"),
                "market_cap_rank": coin_data.get("market_cap_rank"),
                "total_volume": market_data.get("total_volume", {}).get("usd"),
                "price_change_24h": market_data.get("price_change_percentage_24h"),
                "price_change_7d": market_data.get("price_change_percentage_7d"),
                "circulating_supply": market_data.get("circulating_supply"),
                "total_supply": market_data.get("total_supply"),
                "ath": market_data.get("ath", {}).get("usd"),
                "atl": market_data.get("atl", {}).get("usd"),
                "description": coin_data.get("description", {}).get("en", "")[:500],  # Truncate description
                "links": coin_data.get("links", {}),
                "community_data": coin_data.get("community_data", {}),
                "developer_data": coin_data.get("developer_data", {})
            }
        except Exception as e:
            logger.error(f"Error getting CoinGecko data: {e}")
            return {}
    
    async def _get_binance_data(self, symbol: str) -> Dict[str, Any]:
        """Get data from Binance"""
        try:
            client = self.clients["binance"]
            
            # Get 24hr ticker data
            ticker_data = await client.get_ticker_24hr(symbol)
            if not ticker_data:
                return {}
            
            ticker = ticker_data[0] if isinstance(ticker_data, list) else ticker_data
            
            # Get order book for liquidity analysis
            order_book = await client.get_order_book(symbol, limit=100)
            
            # Calculate bid-ask spread
            spread = 0
            if order_book and order_book.get("bids") and order_book.get("asks"):
                best_bid = float(order_book["bids"][0][0])
                best_ask = float(order_book["asks"][0][0])
                spread = (best_ask - best_bid) / best_ask * 100
            
            return {
                "symbol": symbol,
                "price": float(ticker.get("lastPrice", 0)),
                "price_change_24h": float(ticker.get("priceChangePercent", 0)),
                "volume_24h": float(ticker.get("volume", 0)),
                "quote_volume_24h": float(ticker.get("quoteVolume", 0)),
                "high_24h": float(ticker.get("highPrice", 0)),
                "low_24h": float(ticker.get("lowPrice", 0)),
                "bid_ask_spread": spread,
                "trade_count_24h": int(ticker.get("count", 0)),
                "order_book": order_book
            }
        except Exception as e:
            logger.error(f"Error getting Binance data: {e}")
            return {}
    
    def _aggregate_token_data(self, sources: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate data from multiple sources"""
        aggregated = {
            "price_usd": None,
            "volume_24h": None,
            "market_cap": None,
            "price_change_24h": None,
            "liquidity_total": None,
            "confidence_score": 0.0,
            "data_sources": list(sources.keys())
        }
        
        # Price aggregation with source priority
        price_sources = []
        for source in self.fallback_order["price_data"]:
            if source in sources:
                data = sources[source]
                if source == "dexscreener" and data.get("best_price"):
                    price_sources.append(("dexscreener", float(data["best_price"])))
                elif source == "coingecko" and data.get("current_price"):
                    price_sources.append(("coingecko", float(data["current_price"])))
                elif source == "binance" and data.get("price"):
                    price_sources.append(("binance", float(data["price"])))
        
        if price_sources:
            # Use weighted average if multiple sources, otherwise use the best source
            if len(price_sources) == 1:
                aggregated["price_usd"] = price_sources[0][1]
            else:
                # Weight: DexScreener=0.4, CoinGecko=0.4, Binance=0.2
                weights = {"dexscreener": 0.4, "coingecko": 0.4, "binance": 0.2}
                total_weight = sum(weights.get(source, 0.1) for source, _ in price_sources)
                weighted_price = sum(
                    price * weights.get(source, 0.1) 
                    for source, price in price_sources
                ) / total_weight
                aggregated["price_usd"] = weighted_price
        
        # Volume aggregation
        volumes = []
        if "dexscreener" in sources and sources["dexscreener"].get("total_volume_24h"):
            volumes.append(sources["dexscreener"]["total_volume_24h"])
        if "coingecko" in sources and sources["coingecko"].get("total_volume"):
            volumes.append(sources["coingecko"]["total_volume"])
        if "binance" in sources and sources["binance"].get("quote_volume_24h"):
            volumes.append(sources["binance"]["quote_volume_24h"])
        
        if volumes:
            aggregated["volume_24h"] = max(volumes)  # Use highest volume
        
        # Market cap (primarily from CoinGecko)
        if "coingecko" in sources and sources["coingecko"].get("market_cap"):
            aggregated["market_cap"] = sources["coingecko"]["market_cap"]
        
        # Price change aggregation
        price_changes = []
        if "dexscreener" in sources and sources["dexscreener"].get("avg_price_change_24h") is not None:
            price_changes.append(sources["dexscreener"]["avg_price_change_24h"])
        if "coingecko" in sources and sources["coingecko"].get("price_change_24h") is not None:
            price_changes.append(sources["coingecko"]["price_change_24h"])
        if "binance" in sources and sources["binance"].get("price_change_24h") is not None:
            price_changes.append(sources["binance"]["price_change_24h"])
        
        if price_changes:
            aggregated["price_change_24h"] = sum(price_changes) / len(price_changes)
        
        # Liquidity (from DexScreener)
        if "dexscreener" in sources and sources["dexscreener"].get("total_liquidity"):
            aggregated["liquidity_total"] = sources["dexscreener"]["total_liquidity"]
        
        # Calculate confidence score based on data availability
        confidence_factors = []
        if aggregated["price_usd"]:
            confidence_factors.append(0.3)
        if aggregated["volume_24h"]:
            confidence_factors.append(0.2)
        if aggregated["market_cap"]:
            confidence_factors.append(0.2)
        if len(sources) >= 2:
            confidence_factors.append(0.2)
        if "coingecko" in sources:  # CoinGecko adds legitimacy
            confidence_factors.append(0.1)
        
        aggregated["confidence_score"] = sum(confidence_factors)
        
        return aggregated
    
    async def get_trending_opportunities(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get trending tokens that might have arbitrage opportunities"""
        cache_key = f"trending_opportunities:{limit}"
        cached_data = await cache.get(cache_key)
        if cached_data:
            return cached_data
        
        trending_tokens = []
        
        # Get trending from multiple sources
        tasks = [
            self._get_coingecko_trending(),
            self._get_dexscreener_trending(),
            self._get_binance_trending()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Combine and deduplicate
        seen_symbols = set()
        for result in results:
            if isinstance(result, list):
                for token in result:
                    symbol = token.get("symbol", "").upper()
                    if symbol and symbol not in seen_symbols:
                        seen_symbols.add(symbol)
                        trending_tokens.append(token)
        
        # Sort by volume and limit
        trending_tokens.sort(key=lambda x: x.get("volume_24h", 0), reverse=True)
        result = trending_tokens[:limit]
        
        # Cache for 10 minutes
        await cache.set(cache_key, result, ttl=600)
        
        return result
    
    async def _get_coingecko_trending(self) -> List[Dict[str, Any]]:
        """Get trending tokens from CoinGecko"""
        try:
            client = self.clients["coingecko"]
            trending = await client.get_trending_coins()
            
            if not trending or not trending.get("coins"):
                return []
            
            trending_list = []
            for coin_data in trending["coins"]:
                coin = coin_data.get("item", {})
                trending_list.append({
                    "symbol": coin.get("symbol", "").upper(),
                    "name": coin.get("name", ""),
                    "market_cap_rank": coin.get("market_cap_rank"),
                    "source": "coingecko_trending"
                })
            
            return trending_list
        except Exception as e:
            logger.error(f"Error getting CoinGecko trending: {e}")
            return []
    
    async def _get_dexscreener_trending(self) -> List[Dict[str, Any]]:
        """Get trending tokens from DexScreener"""
        try:
            client = self.clients["dexscreener"]
            trending = await client.get_trending_pairs()
            
            if not trending:
                return []
            
            trending_list = []
            for pair in trending:
                base_token = pair.get("baseToken", {})
                trending_list.append({
                    "symbol": base_token.get("symbol", "").upper(),
                    "name": base_token.get("name", ""),
                    "volume_24h": pair.get("volume", {}).get("h24", 0),
                    "price_change_24h": pair.get("priceChange", {}).get("h24", 0),
                    "source": "dexscreener_trending"
                })
            
            return trending_list
        except Exception as e:
            logger.error(f"Error getting DexScreener trending: {e}")
            return []
    
    async def _get_binance_trending(self) -> List[Dict[str, Any]]:
        """Get trending tokens from Binance"""
        try:
            client = self.clients["binance"]
            top_volume = await client.get_top_volume_symbols(50)
            
            if not top_volume:
                return []
            
            trending_list = []
            for ticker in top_volume:
                symbol = ticker.get("symbol", "")
                if symbol.endswith("USDT"):
                    base_symbol = symbol[:-4]  # Remove USDT
                    trending_list.append({
                        "symbol": base_symbol,
                        "volume_24h": float(ticker.get("quoteVolume", 0)),
                        "price_change_24h": float(ticker.get("priceChangePercent", 0)),
                        "source": "binance_volume"
                    })
            
            return trending_list
        except Exception as e:
            logger.error(f"Error getting Binance trending: {e}")
            return []


# Global data aggregator instance
data_aggregator = DataAggregator()
