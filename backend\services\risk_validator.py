"""
Advanced risk assessment and opportunity validation
"""
import asyncio
import math
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from backend.core.logging import get_logger
from backend.core.cache import cache
from backend.services.chain_manager import chain_manager
from backend.config.settings import settings

logger = get_logger(__name__)


class LiquidityAnalyzer:
    """Analyze liquidity depth and market impact"""
    
    @staticmethod
    def calculate_slippage(
        order_book: Dict[str, Any], 
        trade_amount_usd: float, 
        side: str = "buy"
    ) -> Dict[str, Any]:
        """Calculate slippage for a given trade amount"""
        
        if side == "buy":
            orders = order_book.get("asks", [])
        else:
            orders = order_book.get("bids", [])
        
        if not orders:
            return {"slippage_percentage": 100.0, "executable": False}
        
        total_cost = 0
        total_quantity = 0
        remaining_amount = trade_amount_usd
        
        for price_str, quantity_str in orders:
            price = float(price_str)
            quantity = float(quantity_str)
            order_value = price * quantity
            
            if remaining_amount <= order_value:
                # This order can fulfill the remaining amount
                needed_quantity = remaining_amount / price
                total_cost += remaining_amount
                total_quantity += needed_quantity
                remaining_amount = 0
                break
            else:
                # Take the entire order
                total_cost += order_value
                total_quantity += quantity
                remaining_amount -= order_value
        
        if remaining_amount > 0:
            # Not enough liquidity
            return {
                "slippage_percentage": 100.0,
                "executable": False,
                "available_liquidity_usd": trade_amount_usd - remaining_amount
            }
        
        # Calculate average execution price
        avg_execution_price = total_cost / total_quantity if total_quantity > 0 else 0
        best_price = float(orders[0][0])
        
        slippage_percentage = abs((avg_execution_price - best_price) / best_price) * 100
        
        return {
            "slippage_percentage": slippage_percentage,
            "executable": True,
            "avg_execution_price": avg_execution_price,
            "best_price": best_price,
            "total_quantity": total_quantity,
            "price_impact": slippage_percentage
        }
    
    @staticmethod
    def analyze_liquidity_depth(pair_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze liquidity depth for a trading pair"""
        
        liquidity_usd = pair_data.get("liquidity", {}).get("usd", 0)
        volume_24h = pair_data.get("volume", {}).get("h24", 0)
        
        # Calculate liquidity metrics
        volume_to_liquidity_ratio = volume_24h / liquidity_usd if liquidity_usd > 0 else 0
        
        # Liquidity quality assessment
        if liquidity_usd > 1000000:  # $1M+
            liquidity_quality = "excellent"
        elif liquidity_usd > 500000:  # $500k+
            liquidity_quality = "good"
        elif liquidity_usd > 100000:  # $100k+
            liquidity_quality = "fair"
        elif liquidity_usd > 10000:   # $10k+
            liquidity_quality = "poor"
        else:
            liquidity_quality = "very_poor"
        
        # Volume quality assessment
        if volume_to_liquidity_ratio > 5:
            volume_quality = "excellent"
        elif volume_to_liquidity_ratio > 2:
            volume_quality = "good"
        elif volume_to_liquidity_ratio > 0.5:
            volume_quality = "fair"
        else:
            volume_quality = "poor"
        
        return {
            "liquidity_usd": liquidity_usd,
            "volume_24h": volume_24h,
            "volume_to_liquidity_ratio": volume_to_liquidity_ratio,
            "liquidity_quality": liquidity_quality,
            "volume_quality": volume_quality,
            "recommended_max_trade_usd": liquidity_usd * 0.02  # 2% of liquidity
        }


class GasFeeCalculator:
    """Calculate gas fees and transaction costs"""
    
    @staticmethod
    async def get_current_gas_prices() -> Dict[str, Dict[str, float]]:
        """Get current gas prices for all supported chains"""
        
        cache_key = "current_gas_prices"
        cached_prices = await cache.get(cache_key)
        if cached_prices:
            return cached_prices
        
        # In a real implementation, this would fetch from gas price APIs
        # For now, we'll use estimated values
        gas_prices = {
            "ethereum": {
                "slow": 20.0,      # gwei
                "standard": 30.0,   # gwei
                "fast": 50.0,      # gwei
                "instant": 80.0    # gwei
            },
            "bsc": {
                "slow": 3.0,
                "standard": 5.0,
                "fast": 8.0,
                "instant": 12.0
            },
            "polygon": {
                "slow": 20.0,
                "standard": 30.0,
                "fast": 50.0,
                "instant": 80.0
            },
            "arbitrum": {
                "slow": 0.1,
                "standard": 0.2,
                "fast": 0.5,
                "instant": 1.0
            },
            "avalanche": {
                "slow": 20.0,
                "standard": 25.0,
                "fast": 35.0,
                "instant": 50.0
            },
            "solana": {
                "slow": 0.000005,
                "standard": 0.000005,
                "fast": 0.000005,
                "instant": 0.000005
            }
        }
        
        # Cache for 1 minute
        await cache.set(cache_key, gas_prices, ttl=60)
        
        return gas_prices
    
    @staticmethod
    async def calculate_transaction_cost(
        chain_id: str, 
        transaction_type: str = "swap",
        priority: str = "standard"
    ) -> Dict[str, Any]:
        """Calculate transaction cost for specific chain and operation"""
        
        gas_prices = await GasFeeCalculator.get_current_gas_prices()
        chain_gas_prices = gas_prices.get(chain_id, {})
        
        if not chain_gas_prices:
            return {"error": f"Gas prices not available for {chain_id}"}
        
        gas_price_gwei = chain_gas_prices.get(priority, 30.0)
        
        # Gas estimates for different transaction types
        gas_estimates = {
            "swap": 150000,        # DEX swap
            "approve": 50000,      # Token approval
            "transfer": 21000,     # Simple transfer
            "bridge": 200000,      # Cross-chain bridge
            "complex_swap": 300000 # Complex multi-hop swap
        }
        
        estimated_gas = gas_estimates.get(transaction_type, 150000)
        
        # Calculate cost
        if chain_id == "solana":
            # Solana uses different fee structure
            gas_cost_native = gas_price_gwei  # Direct fee in SOL
        else:
            # EVM chains
            gas_cost_wei = estimated_gas * gas_price_gwei * 1e9
            gas_cost_native = gas_cost_wei / 1e18
        
        # Get native token price
        chain_config = chain_manager.get_chain_config(chain_id)
        native_token = chain_config.native_token if chain_config else "ETH"
        
        # Default native token prices (should be fetched from real API)
        native_token_prices = {
            "ETH": 2000,
            "BNB": 300,
            "MATIC": 0.8,
            "AVAX": 25,
            "SOL": 60
        }
        
        native_price_usd = native_token_prices.get(native_token, 100)
        gas_cost_usd = gas_cost_native * native_price_usd
        
        return {
            "chain_id": chain_id,
            "transaction_type": transaction_type,
            "priority": priority,
            "estimated_gas": estimated_gas,
            "gas_price_gwei": gas_price_gwei,
            "gas_cost_native": gas_cost_native,
            "gas_cost_usd": gas_cost_usd,
            "native_token": native_token,
            "native_token_price_usd": native_price_usd
        }


class MEVProtectionAnalyzer:
    """Analyze MEV (Maximum Extractable Value) risks"""
    
    @staticmethod
    def assess_mev_risk(
        opportunity: Dict[str, Any], 
        market_conditions: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Assess MEV risk for an arbitrage opportunity"""
        
        profit_percentage = opportunity.get("profit_percentage", 0)
        chain_id = opportunity.get("chain_id", "ethereum")
        trade_amount_usd = opportunity.get("amount_usd", 1000)
        
        # Base MEV risk factors
        risk_factors = []
        risk_score = 0
        
        # High profit opportunities attract more MEV
        if profit_percentage > 5:
            risk_factors.append("High profit margin attracts MEV bots")
            risk_score += 3
        elif profit_percentage > 2:
            risk_factors.append("Moderate profit margin may attract MEV")
            risk_score += 2
        elif profit_percentage > 1:
            risk_score += 1
        
        # Chain-specific MEV risk
        chain_mev_risk = {
            "ethereum": 3,    # High MEV activity
            "bsc": 2,         # Moderate MEV activity
            "polygon": 2,     # Moderate MEV activity
            "arbitrum": 1,    # Lower MEV activity
            "avalanche": 1,   # Lower MEV activity
            "solana": 1       # Different MEV landscape
        }
        
        chain_risk = chain_mev_risk.get(chain_id, 2)
        risk_score += chain_risk
        
        if chain_risk >= 3:
            risk_factors.append(f"High MEV activity on {chain_id}")
        elif chain_risk >= 2:
            risk_factors.append(f"Moderate MEV activity on {chain_id}")
        
        # Trade size impact
        if trade_amount_usd > 50000:
            risk_factors.append("Large trade size increases MEV risk")
            risk_score += 2
        elif trade_amount_usd > 10000:
            risk_factors.append("Medium trade size may attract MEV")
            risk_score += 1
        
        # Market volatility impact
        volatility = market_conditions.get("volatility_24h", 0)
        if volatility > 0.1:  # 10% volatility
            risk_factors.append("High market volatility increases MEV risk")
            risk_score += 2
        elif volatility > 0.05:  # 5% volatility
            risk_factors.append("Moderate volatility may increase MEV risk")
            risk_score += 1
        
        # Time sensitivity
        execution_time = opportunity.get("estimated_time_minutes", 30)
        if execution_time > 60:
            risk_factors.append("Long execution time increases MEV exposure")
            risk_score += 1
        
        # Risk level classification
        if risk_score <= 3:
            risk_level = "low"
        elif risk_score <= 6:
            risk_level = "medium"
        elif risk_score <= 9:
            risk_level = "high"
        else:
            risk_level = "very_high"
        
        # MEV protection recommendations
        protection_strategies = []
        
        if risk_level in ["high", "very_high"]:
            protection_strategies.extend([
                "Use private mempool (Flashbots, etc.)",
                "Split trade into smaller chunks",
                "Use MEV-protected RPC endpoints"
            ])
        
        if chain_id == "ethereum":
            protection_strategies.append("Consider using Flashbots Protect")
        
        if execution_time > 30:
            protection_strategies.append("Execute quickly to minimize exposure")
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "risk_factors": risk_factors,
            "protection_strategies": protection_strategies,
            "recommended_max_trade_usd": max(1000, 50000 - risk_score * 5000)
        }


class OpportunityValidator:
    """Comprehensive opportunity validation"""
    
    def __init__(self):
        self.liquidity_analyzer = LiquidityAnalyzer()
        self.gas_calculator = GasFeeCalculator()
        self.mev_analyzer = MEVProtectionAnalyzer()
    
    async def validate_opportunity(
        self, 
        opportunity: Dict[str, Any],
        validation_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Perform comprehensive validation of arbitrage opportunity"""
        
        if not validation_config:
            validation_config = {
                "max_slippage_percentage": 2.0,
                "min_liquidity_usd": 10000,
                "max_gas_cost_percentage": 1.0,
                "max_mev_risk_level": "medium"
            }
        
        validation_result = {
            "is_valid": False,
            "confidence_score": 0.0,
            "validation_errors": [],
            "warnings": [],
            "recommendations": [],
            "detailed_analysis": {}
        }
        
        try:
            # 1. Liquidity Analysis
            liquidity_analysis = await self._validate_liquidity(opportunity, validation_config)
            validation_result["detailed_analysis"]["liquidity"] = liquidity_analysis
            
            # 2. Gas Fee Analysis
            gas_analysis = await self._validate_gas_costs(opportunity, validation_config)
            validation_result["detailed_analysis"]["gas_costs"] = gas_analysis
            
            # 3. Slippage Analysis
            slippage_analysis = await self._validate_slippage(opportunity, validation_config)
            validation_result["detailed_analysis"]["slippage"] = slippage_analysis
            
            # 4. MEV Risk Analysis
            mev_analysis = await self._validate_mev_risk(opportunity, validation_config)
            validation_result["detailed_analysis"]["mev_risk"] = mev_analysis
            
            # 5. Market Conditions Analysis
            market_analysis = await self._validate_market_conditions(opportunity)
            validation_result["detailed_analysis"]["market_conditions"] = market_analysis
            
            # Aggregate validation results
            validation_result = self._aggregate_validation_results(
                validation_result, 
                [liquidity_analysis, gas_analysis, slippage_analysis, mev_analysis, market_analysis]
            )
            
        except Exception as e:
            logger.error(f"Error validating opportunity: {e}")
            validation_result["validation_errors"].append(f"Validation error: {str(e)}")
        
        return validation_result
    
    async def _validate_liquidity(
        self, 
        opportunity: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate liquidity requirements"""
        
        min_liquidity = config.get("min_liquidity_usd", 10000)
        
        # Get liquidity from opportunity data
        buy_liquidity = opportunity.get("buy_liquidity_usd", 0)
        sell_liquidity = opportunity.get("sell_liquidity_usd", 0)
        min_available_liquidity = min(buy_liquidity, sell_liquidity)
        
        is_valid = min_available_liquidity >= min_liquidity
        
        result = {
            "is_valid": is_valid,
            "buy_liquidity_usd": buy_liquidity,
            "sell_liquidity_usd": sell_liquidity,
            "min_liquidity_usd": min_available_liquidity,
            "required_min_liquidity": min_liquidity
        }
        
        if not is_valid:
            result["error"] = f"Insufficient liquidity: ${min_available_liquidity:,.0f} < ${min_liquidity:,.0f}"
        
        return result
    
    async def _validate_gas_costs(
        self, 
        opportunity: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate gas costs don't exceed threshold"""
        
        max_gas_percentage = config.get("max_gas_cost_percentage", 1.0)
        chain_id = opportunity.get("chain_id", "ethereum")
        trade_amount = opportunity.get("amount_usd", 1000)
        
        # Calculate gas costs
        gas_cost = await self.gas_calculator.calculate_transaction_cost(
            chain_id, "swap", "standard"
        )
        
        gas_cost_usd = gas_cost.get("gas_cost_usd", 0)
        gas_percentage = (gas_cost_usd / trade_amount) * 100 if trade_amount > 0 else 0
        
        is_valid = gas_percentage <= max_gas_percentage
        
        result = {
            "is_valid": is_valid,
            "gas_cost_usd": gas_cost_usd,
            "gas_percentage": gas_percentage,
            "max_allowed_percentage": max_gas_percentage,
            "gas_details": gas_cost
        }
        
        if not is_valid:
            result["error"] = f"Gas costs too high: {gas_percentage:.2f}% > {max_gas_percentage:.2f}%"
        
        return result
    
    async def _validate_slippage(
        self, 
        opportunity: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate slippage is within acceptable limits"""
        
        max_slippage = config.get("max_slippage_percentage", 2.0)
        
        # For now, estimate slippage based on liquidity
        # In a real implementation, this would use actual order book data
        liquidity = min(
            opportunity.get("buy_liquidity_usd", 0),
            opportunity.get("sell_liquidity_usd", 0)
        )
        trade_amount = opportunity.get("amount_usd", 1000)
        
        # Estimate slippage based on trade size vs liquidity
        if liquidity > 0:
            liquidity_ratio = trade_amount / liquidity
            estimated_slippage = liquidity_ratio * 100 * 0.5  # Rough estimation
        else:
            estimated_slippage = 100  # No liquidity = 100% slippage
        
        is_valid = estimated_slippage <= max_slippage
        
        result = {
            "is_valid": is_valid,
            "estimated_slippage_percentage": estimated_slippage,
            "max_allowed_slippage": max_slippage,
            "liquidity_ratio": liquidity_ratio if liquidity > 0 else 0
        }
        
        if not is_valid:
            result["error"] = f"Slippage too high: {estimated_slippage:.2f}% > {max_slippage:.2f}%"
        
        return result
    
    async def _validate_mev_risk(
        self, 
        opportunity: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate MEV risk is acceptable"""
        
        max_risk_level = config.get("max_mev_risk_level", "medium")
        
        # Mock market conditions for MEV analysis
        market_conditions = {
            "volatility_24h": 0.05  # 5% volatility
        }
        
        mev_analysis = self.mev_analyzer.assess_mev_risk(opportunity, market_conditions)
        
        risk_levels = ["low", "medium", "high", "very_high"]
        current_risk_index = risk_levels.index(mev_analysis["risk_level"])
        max_risk_index = risk_levels.index(max_risk_level)
        
        is_valid = current_risk_index <= max_risk_index
        
        result = {
            "is_valid": is_valid,
            "mev_risk_level": mev_analysis["risk_level"],
            "max_allowed_risk_level": max_risk_level,
            "mev_analysis": mev_analysis
        }
        
        if not is_valid:
            result["error"] = f"MEV risk too high: {mev_analysis['risk_level']} > {max_risk_level}"
        
        return result
    
    async def _validate_market_conditions(self, opportunity: Dict[str, Any]) -> Dict[str, Any]:
        """Validate current market conditions"""
        
        # This would analyze current market conditions
        # For now, return a basic analysis
        
        return {
            "is_valid": True,
            "market_volatility": "normal",
            "network_congestion": "low",
            "recommendations": ["Monitor market conditions during execution"]
        }
    
    def _aggregate_validation_results(
        self, 
        validation_result: Dict[str, Any], 
        analyses: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Aggregate all validation analyses into final result"""
        
        # Check if all validations passed
        all_valid = all(analysis.get("is_valid", False) for analysis in analyses)
        validation_result["is_valid"] = all_valid
        
        # Collect errors and warnings
        for analysis in analyses:
            if "error" in analysis:
                validation_result["validation_errors"].append(analysis["error"])
            if "warning" in analysis:
                validation_result["warnings"].append(analysis["warning"])
        
        # Calculate confidence score
        valid_count = sum(1 for analysis in analyses if analysis.get("is_valid", False))
        confidence_score = valid_count / len(analyses) if analyses else 0
        validation_result["confidence_score"] = confidence_score
        
        # Add recommendations
        if not all_valid:
            validation_result["recommendations"].extend([
                "Review validation errors before proceeding",
                "Consider adjusting trade parameters",
                "Monitor market conditions closely"
            ])
        
        return validation_result


# Global validator instance
opportunity_validator = OpportunityValidator()
