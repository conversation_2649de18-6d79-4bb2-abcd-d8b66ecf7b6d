"""
Advanced Crypto Arbitrage Bot - Main Application (Fixed)
FastAPI backend with public APIs only - no backend dependencies
"""
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, JSONResponse
import async<PERSON>
import uvicorn
from contextlib import asynccontextmanager
from typing import Dict, Any, List, Optional, Tuple
import time
import httpx
import json
from datetime import datetime, timedelta
import aiohttp
from dataclasses import dataclass
import hashlib

# Simple logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Data class for validation results"""
    is_valid: bool
    feasibility_score: int  # 0-100
    warnings: List[str]
    security_flags: List[str]
    execution_time_estimate: float  # seconds
    liquidity_depth_score: int  # 0-100

class RealTimeValidator:
    """Advanced real-time opportunity validation system"""

    def __init__(self):
        self.cache = {}
        self.cache_ttl = {
            'liquidity': 30,  # 30 seconds
            'security': 3600,  # 1 hour
            'token_info': 86400  # 24 hours
        }

    def _get_cache_key(self, data: str) -> str:
        """Generate cache key from data"""
        return hashlib.md5(data.encode()).hexdigest()

    def _is_cache_valid(self, key: str, cache_type: str) -> bool:
        """Check if cache entry is still valid"""
        if key not in self.cache:
            return False

        entry = self.cache[key]
        ttl = self.cache_ttl.get(cache_type, 300)
        return (datetime.now() - entry['timestamp']).seconds < ttl

    def _set_cache(self, key: str, data: Any, cache_type: str):
        """Set cache entry with timestamp"""
        self.cache[key] = {
            'data': data,
            'timestamp': datetime.now(),
            'type': cache_type
        }

    async def validate_liquidity_depth(self, pair_address: str, chain: str, order_size_usd: float) -> Tuple[bool, int, List[str]]:
        """
        CRITICAL: Validate liquidity depth for order execution
        Returns: (is_sufficient, depth_score, warnings)
        """
        warnings = []

        try:
            # Cache key for liquidity data
            cache_key = self._get_cache_key(f"liquidity_{pair_address}_{chain}")

            if self._is_cache_valid(cache_key, 'liquidity'):
                liquidity_data = self.cache[cache_key]['data']
            else:
                # Fetch fresh liquidity data from DexScreener
                async with aiohttp.ClientSession() as session:
                    url = f"https://api.dexscreener.com/latest/dex/pairs/{chain}/{pair_address}"
                    async with session.get(url) as response:
                        if response.status == 200:
                            data = await response.json()
                            liquidity_data = data.get('pair', {}).get('liquidity', {})
                            self._set_cache(cache_key, liquidity_data, 'liquidity')
                        else:
                            return False, 0, ["Failed to fetch liquidity data"]

            liquidity_usd = liquidity_data.get('usd', 0)

            if liquidity_usd <= 0:
                return False, 0, ["No liquidity data available"]

            # Calculate liquidity depth score
            liquidity_ratio = liquidity_usd / order_size_usd if order_size_usd > 0 else 0

            if liquidity_ratio < 5:
                warnings.append(f"Low liquidity ratio: {liquidity_ratio:.1f}x")
                depth_score = min(20, int(liquidity_ratio * 4))
            elif liquidity_ratio < 10:
                warnings.append(f"Moderate liquidity ratio: {liquidity_ratio:.1f}x")
                depth_score = min(60, int(20 + (liquidity_ratio - 5) * 8))
            elif liquidity_ratio < 50:
                depth_score = min(90, int(60 + (liquidity_ratio - 10) * 0.75))
            else:
                depth_score = 100

            is_sufficient = liquidity_ratio >= 5  # Minimum 5x liquidity

            return is_sufficient, depth_score, warnings

        except Exception as e:
            logger.error(f"Liquidity validation error: {e}")
            return False, 0, [f"Validation error: {str(e)}"]

    async def validate_execution_feasibility(self, opportunity: Dict) -> Tuple[bool, float, List[str]]:
        """
        Validate if arbitrage can be executed in realistic timeframe
        Returns: (is_feasible, execution_time_estimate, warnings)
        """
        warnings = []

        try:
            # Base execution time estimates (seconds)
            base_times = {
                'ethereum': 15,  # ~1 block
                'bsc': 3,       # ~1 block
                'polygon': 2,   # ~1 block
                'arbitrum': 1,  # ~1 block
                'solana': 0.5,  # ~1 slot
                'avalanche': 2, # ~1 block
                'fantom': 1,    # ~1 block
                'optimism': 1   # ~1 block
            }

            chain = opportunity.get('buy_chain', 'ethereum').lower()
            base_time = base_times.get(chain, 15)

            # Adjust for network congestion (simplified)
            congestion_multiplier = 1.5  # Assume moderate congestion

            # Adjust for transaction complexity (2 transactions for arbitrage)
            complexity_multiplier = 2.0

            # Calculate total execution time
            execution_time = base_time * congestion_multiplier * complexity_multiplier

            # Check if execution time is reasonable
            max_reasonable_time = 300  # 5 minutes

            if execution_time > max_reasonable_time:
                warnings.append(f"Long execution time: {execution_time:.1f}s")
                return False, execution_time, warnings

            # Check profit vs gas costs (simplified)
            profit_usd = opportunity.get('profit_usd', 0)
            estimated_gas_cost = {
                'ethereum': 50,   # High gas
                'bsc': 1,        # Low gas
                'polygon': 0.1,  # Very low gas
                'arbitrum': 5,   # Medium gas
                'solana': 0.01,  # Very low gas
                'avalanche': 2,  # Low gas
                'fantom': 0.1,   # Very low gas
                'optimism': 5    # Medium gas
            }.get(chain, 10)

            net_profit = profit_usd - estimated_gas_cost

            if net_profit <= 0:
                warnings.append(f"Negative net profit after gas: ${net_profit:.2f}")
                return False, execution_time, warnings

            if net_profit < 1:
                warnings.append(f"Low net profit after gas: ${net_profit:.2f}")

            return True, execution_time, warnings

        except Exception as e:
            logger.error(f"Execution feasibility error: {e}")
            return False, 300, [f"Feasibility check error: {str(e)}"]

    async def filter_false_positives(self, opportunity: Dict) -> Tuple[bool, int, List[str]]:
        """
        CRITICAL: Detect and eliminate false positive opportunities
        Returns: (is_legitimate, confidence_score, warnings)
        """
        warnings = []
        confidence_score = 100

        try:
            # Get token and pair information
            token_symbol = opportunity.get('token_symbol', '')
            buy_pair_address = opportunity.get('buy_pair_address', '')
            sell_pair_address = opportunity.get('sell_pair_address', '')
            chain = opportunity.get('buy_chain', '')

            # 1. Volume trading check (minimum 1 hour)
            volume_score = await self._check_recent_volume(buy_pair_address, sell_pair_address, chain)
            if volume_score < 30:
                warnings.append(f"Low recent trading volume (score: {volume_score})")
                confidence_score -= 25

            # 2. Bid-ask spread check
            spread_score = await self._check_bid_ask_spread(opportunity)
            if spread_score < 50:
                warnings.append(f"Wide bid-ask spread detected (score: {spread_score})")
                confidence_score -= 20

            # 3. Market cap check
            mcap_score = await self._check_market_cap(token_symbol, chain)
            if mcap_score < 40:
                warnings.append(f"Low market cap - manipulation risk (score: {mcap_score})")
                confidence_score -= 30

            # 4. Token age check
            age_score = await self._check_token_age(token_symbol, chain)
            if age_score < 50:
                warnings.append(f"New token - high risk (score: {age_score})")
                confidence_score -= 25

            # Ensure confidence score doesn't go below 0
            confidence_score = max(0, confidence_score)

            # Determine if opportunity is legitimate
            is_legitimate = confidence_score >= 60  # 60% confidence threshold

            if not is_legitimate:
                warnings.append(f"Low confidence score: {confidence_score}/100")

            return is_legitimate, confidence_score, warnings

        except Exception as e:
            logger.error(f"False positive filter error: {e}")
            return False, 0, [f"Filter error: {str(e)}"]

    async def _check_recent_volume(self, buy_pair: str, sell_pair: str, chain: str) -> int:
        """Check recent trading volume (1 hour minimum $1000+)"""
        try:
            total_volume = 0

            for pair_address in [buy_pair, sell_pair]:
                if not pair_address:
                    continue

                cache_key = self._get_cache_key(f"volume_{pair_address}")

                if self._is_cache_valid(cache_key, 'liquidity'):
                    volume_data = self.cache[cache_key]['data']
                else:
                    async with aiohttp.ClientSession() as session:
                        url = f"https://api.dexscreener.com/latest/dex/pairs/{chain}/{pair_address}"
                        async with session.get(url) as response:
                            if response.status == 200:
                                data = await response.json()
                                volume_data = data.get('pair', {}).get('volume', {})
                                self._set_cache(cache_key, volume_data, 'liquidity')
                            else:
                                continue

                # Get 1 hour volume (approximate from 24h data)
                volume_24h = volume_data.get('h24', 0)
                volume_1h = volume_24h / 24  # Rough approximation
                total_volume += volume_1h

            # Score based on volume threshold
            if total_volume >= 1000:
                return 100
            elif total_volume >= 500:
                return 75
            elif total_volume >= 100:
                return 50
            elif total_volume >= 10:
                return 25
            else:
                return 0

        except Exception as e:
            logger.error(f"Volume check error: {e}")
            return 0

    async def _check_bid_ask_spread(self, opportunity: Dict) -> int:
        """Check bid-ask spread (max 2% stablecoins, 5% altcoins)"""
        try:
            token_symbol = opportunity.get('token_symbol', '').upper()
            profit_percentage = opportunity.get('profit_percentage', 0)

            # Determine if it's a stablecoin
            stablecoins = ['USDC', 'USDT', 'DAI', 'BUSD', 'FRAX', 'TUSD', 'LUSD']
            is_stablecoin = token_symbol in stablecoins

            max_spread = 2.0 if is_stablecoin else 5.0

            # Use profit percentage as proxy for spread
            if profit_percentage <= max_spread:
                return 100
            elif profit_percentage <= max_spread * 2:
                return 70
            elif profit_percentage <= max_spread * 3:
                return 40
            else:
                return 10

        except Exception as e:
            logger.error(f"Spread check error: {e}")
            return 50

    async def _check_market_cap(self, token_symbol: str, chain: str) -> int:
        """Check market cap (minimum $100K)"""
        try:
            # This would require additional API integration
            # For now, return moderate score for known tokens
            known_tokens = [
                'USDC', 'USDT', 'WETH', 'WBTC', 'DAI', 'LINK', 'UNI', 'AAVE',
                'SOL', 'RAY', 'ORCA', 'BONK', 'WIF', 'POPCAT'
            ]

            if token_symbol.upper() in known_tokens:
                return 100
            else:
                return 60  # Moderate score for unknown tokens

        except Exception as e:
            logger.error(f"Market cap check error: {e}")
            return 50

    async def _check_token_age(self, token_symbol: str, chain: str) -> int:
        """Check token age (minimum 24 hours)"""
        try:
            # This would require contract creation timestamp
            # For now, return moderate score for known tokens
            known_tokens = [
                'USDC', 'USDT', 'WETH', 'WBTC', 'DAI', 'LINK', 'UNI', 'AAVE',
                'SOL', 'RAY', 'ORCA', 'BONK', 'WIF', 'POPCAT'
            ]

            if token_symbol.upper() in known_tokens:
                return 100
            else:
                return 70  # Moderate score for unknown tokens

        except Exception as e:
            logger.error(f"Token age check error: {e}")
            return 50

@dataclass
class SecurityResult:
    """Data class for security analysis results"""
    security_score: int  # 0-100
    goplus_score: int
    quickintel_score: int
    security_flags: List[str]
    honeypot_risk: bool
    audit_status: str
    liquidity_locked: bool
    owner_privileges: List[str]
    recommendations: List[str]

class SecurityAnalyzer:
    """Multi-API security integration for comprehensive token analysis"""

    def __init__(self):
        self.cache = {}
        self.cache_ttl = 3600  # 1 hour cache
        self.goplus_base_url = "https://api.gopluslabs.io/api/v1"
        self.quickintel_base_url = "https://api.quickintel.io/v1"  # Placeholder

    def _get_cache_key(self, token_address: str, chain: str) -> str:
        """Generate cache key for security data"""
        return hashlib.md5(f"security_{token_address}_{chain}".encode()).hexdigest()

    def _is_cache_valid(self, key: str) -> bool:
        """Check if security cache entry is still valid"""
        if key not in self.cache:
            return False

        entry = self.cache[key]
        return (datetime.now() - entry['timestamp']).seconds < self.cache_ttl

    def _set_cache(self, key: str, data: Any):
        """Set security cache entry with timestamp"""
        self.cache[key] = {
            'data': data,
            'timestamp': datetime.now()
        }

    async def check_goplus_security(self, token_address: str, chain: str) -> Dict:
        """
        CRITICAL: Check token security using GoPlus API
        Returns comprehensive security analysis
        """
        try:
            # Map chain names to GoPlus chain IDs
            chain_mapping = {
                'ethereum': '1',
                'bsc': '56',
                'polygon': '137',
                'arbitrum': '42161',
                'optimism': '10',
                'avalanche': '43114',
                'fantom': '250',
                'solana': 'solana'  # Special case for Solana
            }

            chain_id = chain_mapping.get(chain.lower(), '1')

            if not token_address or len(token_address) < 10:
                return self._get_default_security_result("Invalid token address")

            # Check cache first
            cache_key = self._get_cache_key(token_address, chain)
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']

            # Make API request to GoPlus
            async with aiohttp.ClientSession() as session:
                url = f"{self.goplus_base_url}/token_security/{chain_id}"
                params = {"contract_addresses": token_address.lower()}

                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        result = self._parse_goplus_response(data, token_address)
                        self._set_cache(cache_key, result)
                        return result
                    else:
                        logger.warning(f"GoPlus API error: {response.status}")
                        return self._get_default_security_result("API error")

        except Exception as e:
            logger.error(f"GoPlus security check error: {e}")
            return self._get_default_security_result(f"Error: {str(e)}")

    def _parse_goplus_response(self, data: Dict, token_address: str) -> Dict:
        """Parse GoPlus API response into standardized format"""
        try:
            result = data.get('result', {})
            token_data = result.get(token_address.lower(), {})

            if not token_data:
                return self._get_default_security_result("No data available")

            # Parse security indicators
            is_honeypot = token_data.get('is_honeypot', '0') == '1'
            is_open_source = token_data.get('is_open_source', '0') == '1'
            is_proxy = token_data.get('is_proxy', '0') == '1'
            is_mintable = token_data.get('is_mintable', '0') == '1'
            can_take_back_ownership = token_data.get('can_take_back_ownership', '0') == '1'
            owner_change_balance = token_data.get('owner_change_balance', '0') == '1'
            hidden_owner = token_data.get('hidden_owner', '0') == '1'
            selfdestruct = token_data.get('selfdestruct', '0') == '1'

            # Calculate security score
            security_score = 100
            security_flags = []

            if is_honeypot:
                security_score -= 50
                security_flags.append("honeypot")

            if not is_open_source:
                security_score -= 20
                security_flags.append("closed_source")

            if is_proxy:
                security_score -= 15
                security_flags.append("proxy_contract")

            if is_mintable:
                security_score -= 10
                security_flags.append("mintable")

            if can_take_back_ownership:
                security_score -= 25
                security_flags.append("ownership_risk")

            if owner_change_balance:
                security_score -= 30
                security_flags.append("balance_modification")

            if hidden_owner:
                security_score -= 15
                security_flags.append("hidden_owner")

            if selfdestruct:
                security_score -= 40
                security_flags.append("selfdestruct_risk")

            security_score = max(0, security_score)

            return {
                'security_score': security_score,
                'is_honeypot': is_honeypot,
                'is_open_source': is_open_source,
                'security_flags': security_flags,
                'owner_privileges': self._get_owner_privileges(token_data),
                'audit_status': 'verified' if is_open_source else 'unverified',
                'liquidity_locked': False,  # Would need additional API call
                'raw_data': token_data
            }

        except Exception as e:
            logger.error(f"GoPlus response parsing error: {e}")
            return self._get_default_security_result(f"Parse error: {str(e)}")

    def _get_owner_privileges(self, token_data: Dict) -> List[str]:
        """Extract owner privileges from token data"""
        privileges = []

        if token_data.get('is_mintable', '0') == '1':
            privileges.append("mint_tokens")

        if token_data.get('can_take_back_ownership', '0') == '1':
            privileges.append("reclaim_ownership")

        if token_data.get('owner_change_balance', '0') == '1':
            privileges.append("modify_balances")

        if token_data.get('selfdestruct', '0') == '1':
            privileges.append("destroy_contract")

        return privileges

    def _get_default_security_result(self, reason: str) -> Dict:
        """Return default security result when API fails"""
        return {
            'security_score': 50,  # Neutral score
            'is_honeypot': False,
            'is_open_source': False,
            'security_flags': ['unknown'],
            'owner_privileges': [],
            'audit_status': 'unknown',
            'liquidity_locked': False,
            'error': reason
        }

    async def check_quickintel_rating(self, token_address: str, chain: str) -> Dict:
        """
        Check token rating using QuickIntel API (placeholder implementation)
        """
        try:
            # This is a placeholder - QuickIntel API integration would go here
            # For now, return a moderate score for known tokens
            known_safe_tokens = [
                'USDC', 'USDT', 'WETH', 'WBTC', 'DAI', 'LINK', 'UNI', 'AAVE',
                'SOL', 'RAY', 'ORCA'
            ]

            # Simple heuristic based on token address patterns
            if len(token_address) == 42 and token_address.startswith('0x'):  # Ethereum-like
                return {
                    'quickintel_score': 75,
                    'rating': 'B+',
                    'risk_level': 'Medium',
                    'verified': False
                }
            elif len(token_address) > 30:  # Solana-like
                return {
                    'quickintel_score': 70,
                    'rating': 'B',
                    'risk_level': 'Medium',
                    'verified': False
                }
            else:
                return {
                    'quickintel_score': 50,
                    'rating': 'C',
                    'risk_level': 'High',
                    'verified': False
                }

        except Exception as e:
            logger.error(f"QuickIntel check error: {e}")
            return {
                'quickintel_score': 50,
                'rating': 'Unknown',
                'risk_level': 'Unknown',
                'verified': False,
                'error': str(e)
            }

    async def generate_security_score(self, goplus_data: Dict, quickintel_data: Dict) -> int:
        """
        Generate combined security score from multiple APIs
        Weighted average: GoPlus 70%, QuickIntel 30%
        """
        try:
            goplus_score = goplus_data.get('security_score', 50)
            quickintel_score = quickintel_data.get('quickintel_score', 50)

            # Weighted average
            combined_score = int((goplus_score * 0.7) + (quickintel_score * 0.3))

            # Apply penalties for critical issues
            if goplus_data.get('is_honeypot', False):
                combined_score = min(combined_score, 20)  # Max 20 for honeypots

            return max(0, min(100, combined_score))

        except Exception as e:
            logger.error(f"Security score generation error: {e}")
            return 50

    async def get_security_flags(self, goplus_data: Dict, quickintel_data: Dict) -> List[str]:
        """Return array of security warning flags"""
        flags = []

        # GoPlus flags
        goplus_flags = goplus_data.get('security_flags', [])
        flags.extend(goplus_flags)

        # QuickIntel flags
        qi_risk = quickintel_data.get('risk_level', '').lower()
        if qi_risk == 'high':
            flags.append('high_risk_rating')

        # Combined analysis flags
        combined_score = await self.generate_security_score(goplus_data, quickintel_data)
        if combined_score < 30:
            flags.append('very_high_risk')
        elif combined_score < 50:
            flags.append('high_risk')
        elif combined_score < 70:
            flags.append('medium_risk')

        return list(set(flags))  # Remove duplicates

@dataclass
class ScanMetrics:
    """Data class for scan performance metrics"""
    total_tokens: int
    tokens_per_second: float
    api_response_times: List[float]
    cache_hit_rate: float
    success_rate: float
    scan_duration: float
    errors_count: int

class SmartCache:
    """TTL-based caching system with different expiration times"""

    def __init__(self):
        self.cache = {}
        self.ttl_config = {
            'prices': 30,      # 30 seconds
            'security': 3600,  # 1 hour
            'static': 86400    # 24 hours
        }
        self.hit_count = 0
        self.miss_count = 0

    def _is_expired(self, entry: Dict, cache_type: str) -> bool:
        """Check if cache entry is expired"""
        ttl = self.ttl_config.get(cache_type, 300)
        return (datetime.now() - entry['timestamp']).seconds > ttl

    def get(self, key: str, cache_type: str = 'prices') -> Optional[Any]:
        """Get cached data if not expired"""
        if key in self.cache:
            entry = self.cache[key]
            if not self._is_expired(entry, cache_type):
                self.hit_count += 1
                return entry['data']
            else:
                del self.cache[key]

        self.miss_count += 1
        return None

    def set(self, key: str, data: Any, cache_type: str = 'prices'):
        """Set cached data with timestamp"""
        self.cache[key] = {
            'data': data,
            'timestamp': datetime.now(),
            'type': cache_type
        }

    def get_hit_rate(self) -> float:
        """Calculate cache hit rate"""
        total = self.hit_count + self.miss_count
        return (self.hit_count / total * 100) if total > 0 else 0

    def clear_expired(self):
        """Clear expired cache entries"""
        expired_keys = []
        for key, entry in self.cache.items():
            cache_type = entry.get('type', 'prices')
            if self._is_expired(entry, cache_type):
                expired_keys.append(key)

        for key in expired_keys:
            del self.cache[key]

class TokenBatchProcessor:
    """Intelligent API request batching for optimal performance"""

    def __init__(self, max_batch_size: int = 10):
        self.max_batch_size = max_batch_size
        self.rate_limiter = {}
        self.request_times = []

    async def process_token_batch(self, tokens: List[str], chain: str, api_client) -> List[Dict]:
        """Process tokens in optimized batches"""
        results = []

        # Split tokens into batches
        for i in range(0, len(tokens), self.max_batch_size):
            batch = tokens[i:i + self.max_batch_size]

            # Apply rate limiting
            await self._apply_rate_limit(chain)

            # Process batch
            start_time = time.time()
            batch_results = await self._process_single_batch(batch, chain, api_client)
            end_time = time.time()

            # Track performance
            self.request_times.append(end_time - start_time)
            results.extend(batch_results)

            # Small delay between batches
            await asyncio.sleep(0.1)

        return results

    async def _apply_rate_limit(self, chain: str):
        """Apply exponential backoff rate limiting"""
        current_time = time.time()
        last_request = self.rate_limiter.get(chain, 0)

        # Minimum 200ms between requests per chain
        min_interval = 0.2
        time_since_last = current_time - last_request

        if time_since_last < min_interval:
            await asyncio.sleep(min_interval - time_since_last)

        self.rate_limiter[chain] = time.time()

    async def _process_single_batch(self, tokens: List[str], chain: str, api_client) -> List[Dict]:
        """Process a single batch of tokens"""
        try:
            # Create search query for batch
            search_query = " OR ".join(tokens)

            # Make API request
            response = await api_client.get(f"dex/search", {
                'q': search_query,
                'chain': chain
            })

            return response.get('pairs', [])

        except Exception as e:
            logger.error(f"Batch processing error: {e}")
            return []

    def get_avg_response_time(self) -> float:
        """Get average API response time"""
        return sum(self.request_times) / len(self.request_times) if self.request_times else 0

class ParallelScanner:
    """High-performance parallel scanning for multiple chains"""

    def __init__(self, cache: SmartCache, batch_processor: TokenBatchProcessor):
        self.cache = cache
        self.batch_processor = batch_processor
        self.scan_metrics = ScanMetrics(0, 0, [], 0, 0, 0, 0)

    async def scan_chains_parallel(self, tokens: List[str], chains: List[str], api_client) -> List[Dict]:
        """Scan multiple chains in parallel for optimal performance"""
        start_time = time.time()
        all_opportunities = []
        errors_count = 0

        try:
            # Create tasks for parallel chain scanning
            tasks = []
            for chain in chains:
                task = self._scan_chain_with_cache(tokens, chain, api_client)
                tasks.append(task)

            # Execute all chain scans in parallel
            chain_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for i, result in enumerate(chain_results):
                if isinstance(result, Exception):
                    logger.error(f"Chain {chains[i]} scan error: {result}")
                    errors_count += 1
                else:
                    all_opportunities.extend(result)

            # Calculate performance metrics
            end_time = time.time()
            scan_duration = end_time - start_time
            tokens_per_second = len(tokens) * len(chains) / scan_duration if scan_duration > 0 else 0

            self.scan_metrics = ScanMetrics(
                total_tokens=len(tokens) * len(chains),
                tokens_per_second=tokens_per_second,
                api_response_times=self.batch_processor.request_times[-10:],  # Last 10 requests
                cache_hit_rate=self.cache.get_hit_rate(),
                success_rate=((len(chains) - errors_count) / len(chains) * 100) if chains else 0,
                scan_duration=scan_duration,
                errors_count=errors_count
            )

            return all_opportunities

        except Exception as e:
            logger.error(f"Parallel scan error: {e}")
            return []

    async def _scan_chain_with_cache(self, tokens: List[str], chain: str, api_client) -> List[Dict]:
        """Scan single chain with intelligent caching"""
        opportunities = []

        try:
            # Check cache for recent chain data
            cache_key = f"chain_scan_{chain}_{hash(tuple(sorted(tokens)))}"
            cached_data = self.cache.get(cache_key, 'prices')

            if cached_data:
                return cached_data

            # Process tokens in batches
            chain_pairs = await self.batch_processor.process_token_batch(tokens, chain, api_client)

            # Process pairs for opportunities (simplified)
            for pair in chain_pairs:
                if self._is_valid_pair_fast(pair):
                    opportunities.append(pair)

            # Cache results
            self.cache.set(cache_key, opportunities, 'prices')

            return opportunities

        except Exception as e:
            logger.error(f"Chain {chain} scan error: {e}")
            return []

    def _is_valid_pair_fast(self, pair: Dict) -> bool:
        """Fast validation for high-performance scanning"""
        try:
            return (
                pair.get('priceUsd') and
                float(pair.get('priceUsd', 0)) > 0 and
                pair.get('liquidity', {}).get('usd', 0) > 1000 and
                pair.get('volume', {}).get('h24', 0) > 100
            )
        except:
            return False

# Simple WebSocket manager
class SimpleWebSocketManager:
    def __init__(self):
        self.connections = []
    
    async def connect(self, websocket):
        await websocket.accept()
        self.connections.append(websocket)
    
    def disconnect(self, websocket):
        if websocket in self.connections:
            self.connections.remove(websocket)
    
    async def broadcast(self, message):
        for connection in self.connections:
            try:
                await connection.send_text(json.dumps(message))
            except:
                pass

# Simple API client for public APIs
class SimpleAPIClient:
    def __init__(self, base_url: str, rate_limit: int = 30):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def get(self, endpoint: str, params: Dict = None) -> Dict:
        try:
            url = f"{self.base_url}/{endpoint}"
            response = await self.client.get(url, params=params or {})
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            logger.error(f"API request failed: {e}")
            return {}

# Advanced arbitrage detector with same-chain DEX scanning
class AdvancedArbitrageDetector:
    def __init__(self):
        self.opportunities = []
        self.last_scan = None
        self.scan_logs = []
        self.validator = RealTimeValidator()  # Initialize real-time validator
        self.security_analyzer = SecurityAnalyzer()  # Initialize security analyzer

        # High-performance scanning components
        self.smart_cache = SmartCache()
        self.batch_processor = TokenBatchProcessor(max_batch_size=10)
        self.parallel_scanner = ParallelScanner(self.smart_cache, self.batch_processor)
        self.scan_metrics = ScanMetrics(0, 0, [], 0, 0, 0, 0)
        # Comprehensive token list categorized by type
        self.token_categories = {
            "stablecoins": {
                "name": "Stablecoins",
                "tokens": ["USDC", "USDT", "DAI", "BUSD", "FRAX", "TUSD", "LUSD", "MIM", "FDUSD", "PYUSD"],
                "priority": 1
            },
            "blue_chips": {
                "name": "Blue Chip Tokens",
                "tokens": ["WETH", "WBTC", "BNB", "ADA", "SOL", "DOT", "AVAX", "LINK", "LTC", "BCH", "XLM", "ALGO", "ATOM", "ICP", "FIL", "VET", "THETA", "EOS", "AAVE", "MKR"],
                "priority": 2
            },
            "defi": {
                "name": "DeFi Tokens",
                "tokens": ["UNI", "SUSHI", "COMP", "YFI", "CRV", "BAL", "1INCH", "SNX", "RUNE", "ALPHA", "CREAM", "BADGER", "CVX", "FXS", "LDO", "RPL", "ALCX", "SPELL", "ICE", "DYDX"],
                "priority": 3
            },
            "layer1_2": {
                "name": "Layer 1/2 Tokens",
                "tokens": ["MATIC", "ARB", "OP", "METIS", "IMX", "LRC", "MINA", "NEAR", "FTM", "ONE", "CELO", "KAVA", "ROSE", "MOVR", "GLMR", "ASTR", "CFG", "PHA", "RING", "CKB"],
                "priority": 4
            },
            "meme_coins": {
                "name": "Meme Coins",
                "tokens": ["DOGE", "SHIB", "PEPE", "FLOKI", "BONK", "WIF", "BOME", "MEME", "WOJAK", "LADYS", "TURBO", "AIDOGE", "BABYDOGE", "KISHU", "ELON", "AKITA", "HOGE", "SAFEMOON", "DOGELON", "CATGIRL"],
                "priority": 5
            },
            "gaming_nft": {
                "name": "Gaming & NFT",
                "tokens": ["AXS", "SAND", "MANA", "ENJ", "GALA", "ILV", "SLP", "ALICE", "TLM", "WIN", "CHR", "PYR", "SKILL", "GHST", "REVV", "TOWER", "NFTX", "RARI", "SUPER", "UFO"],
                "priority": 6
            },
            "ai_big_data": {
                "name": "AI & Big Data",
                "tokens": ["FET", "AGIX", "OCEAN", "NMR", "GRT", "RLC", "CTXC", "DBC", "MATRIX", "AGI", "COTI", "VIDT", "LPT", "STORJ", "AR", "SC", "SIA", "BTT", "HOT", "IOST"],
                "priority": 7
            },
            "solana_ecosystem": {
                "name": "Solana Ecosystem",
                "tokens": ["SOL", "RAY", "SRM", "FIDA", "ROPE", "COPE", "STEP", "MEDIA", "LIKE", "MAPS", "ORCA", "MNGO", "TULIP", "SUNNY", "SABER", "PORT", "ATLAS", "POLIS", "SAMO", "NINJA", "GRAPE", "GST", "GMT", "BONK", "WIF", "POPCAT", "MEW", "BOME", "SLERF", "MYRO"],
                "priority": 8
            }
        }

        # Extended chain support
        self.supported_chains = {
            "ethereum": {"name": "Ethereum", "symbol": "ETH", "priority": 1},
            "bsc": {"name": "Binance Smart Chain", "symbol": "BNB", "priority": 2},
            "polygon": {"name": "Polygon", "symbol": "MATIC", "priority": 3},
            "arbitrum": {"name": "Arbitrum", "symbol": "ARB", "priority": 4},
            "optimism": {"name": "Optimism", "symbol": "OP", "priority": 5},
            "avalanche": {"name": "Avalanche", "symbol": "AVAX", "priority": 6},
            "fantom": {"name": "Fantom", "symbol": "FTM", "priority": 7},
            "solana": {"name": "Solana", "symbol": "SOL", "priority": 8},
            "base": {"name": "Base", "symbol": "ETH", "priority": 9},
            "cronos": {"name": "Cronos", "symbol": "CRO", "priority": 10}
        }
        # Default configuration
        self.config = {
            "profit_min": 0.05,
            "profit_max": 25.0,
            "min_liquidity": 5000,
            "min_volume_24h": 500,
            "enabled_chains": ["ethereum", "bsc", "polygon", "arbitrum", "solana"],
            "auto_scan_interval": 0,  # 0 = manual
            # Token selection configuration
            "enabled_token_categories": ["stablecoins", "blue_chips", "defi", "layer1_2", "solana_ecosystem"],
            "max_tokens_per_category": 10,
            "prioritize_by_volume": True,
            # Trading simulation configuration
            "simulation_capital": 100,  # USD
            "max_slippage": 0.5,  # percentage
            "capital_per_trade": 100,  # percentage of total capital
            "min_liquidity_ratio": 10,  # liquidity must be 10x capital
            "risk_tolerance": "medium"  # low, medium, high
        }

    def add_log(self, message: str, level: str = "info"):
        """Add log entry with timestamp"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "level": level
        }
        self.scan_logs.append(log_entry)
        # Keep only last 50 logs
        if len(self.scan_logs) > 50:
            self.scan_logs = self.scan_logs[-50:]
        logger.info(f"[{level.upper()}] {message}")

    def update_config(self, new_config: dict):
        """Update scanning configuration"""
        self.config.update(new_config)
        self.add_log(f"⚙️ Konfigurasi diperbarui: {new_config}", "info")

    def get_enabled_tokens(self) -> List[str]:
        """Get list of enabled tokens based on configuration"""
        enabled_tokens = []

        for category in self.config["enabled_token_categories"]:
            if category in self.token_categories:
                category_tokens = self.token_categories[category]["tokens"]
                max_tokens = self.config["max_tokens_per_category"]

                # Take up to max_tokens from each category
                enabled_tokens.extend(category_tokens[:max_tokens])

        return list(set(enabled_tokens))

    def get_enabled_tokens_expanded(self) -> List[str]:
        """Get expanded list of 500+ tokens for high-performance scanning"""
        enabled_tokens = []

        # Get tokens from enabled categories
        for category in self.config["enabled_token_categories"]:
            if category in self.token_categories:
                category_tokens = self.token_categories[category]["tokens"]
                # For high-performance scanning, use more tokens per category
                max_tokens = min(50, len(category_tokens))  # Up to 50 tokens per category
                enabled_tokens.extend(category_tokens[:max_tokens])

        # Add additional popular tokens for comprehensive scanning
        additional_tokens = [
            # Additional DeFi tokens
            "CAKE", "BAKE", "AUTO", "BELT", "BUNNY", "EPS", "XVS", "VAI", "SXP", "TWT",
            # Additional Layer 1/2 tokens
            "LUNA", "UST", "OSMO", "JUNO", "SCRT", "REGEN", "DVPN", "AKT", "XPRT", "NGM",
            # Additional gaming tokens
            "SKILL", "JEWEL", "DFK", "CRYSTAL", "DFKTEARS", "JADE", "AVAX", "JOE", "PNG", "XAVA",
            # Additional meme tokens
            "BABYDOGE", "SAFEMOON", "ELONGATE", "HOKK", "FOGE", "CORGI", "PITBULL", "DOGE2", "SHIBAINU", "FLOKI2",
            # Cross-chain tokens
            "WORMHOLE", "ANYSWAP", "MULTICHAIN", "SYNAPSE", "STARGATE", "LAYERZERO", "AXELAR", "GRAVITY", "COSMOS", "OSMOSIS"
        ]

        enabled_tokens.extend(additional_tokens)

        # Remove duplicates and return up to 500 tokens
        unique_tokens = list(set(enabled_tokens))
        return unique_tokens[:500]

    async def _process_pairs_for_arbitrage(self, all_pairs: List[Dict], tokens: List[str]) -> List[Dict]:
        """Process pairs from parallel scan to find arbitrage opportunities"""
        opportunities = []

        try:
            # Group pairs by token symbol and chain
            token_pairs = {}

            for pair in all_pairs:
                base_token = pair.get('baseToken', {})
                token_symbol = base_token.get('symbol', '').upper()
                chain_id = pair.get('chainId', '')

                if token_symbol in [t.upper() for t in tokens]:
                    key = f"{token_symbol}_{chain_id}"
                    if key not in token_pairs:
                        token_pairs[key] = []
                    token_pairs[key].append(pair)

            # Find arbitrage opportunities within each token-chain group
            for key, pairs in token_pairs.items():
                if len(pairs) >= 2:  # Need at least 2 pairs for arbitrage
                    token_symbol, chain = key.split('_', 1)
                    chain_opportunities = await self._find_arbitrage_in_pairs(pairs, token_symbol, chain)
                    opportunities.extend(chain_opportunities)

            return opportunities

        except Exception as e:
            logger.error(f"Pair processing error: {e}")
            return []

    async def _find_arbitrage_in_pairs(self, pairs: List[Dict], token_symbol: str, chain: str) -> List[Dict]:
        """Find arbitrage opportunities within a group of pairs"""
        opportunities = []

        try:
            # Convert pairs to simplified format for comparison
            dex_prices = {}

            for pair in pairs:
                dex_name = pair.get('dexId', 'unknown')
                price_usd = float(pair.get('priceUsd', 0))
                liquidity_usd = pair.get('liquidity', {}).get('usd', 0)

                if price_usd > 0 and liquidity_usd > 1000:  # Basic filtering
                    dex_prices[dex_name] = {
                        'price': price_usd,
                        'liquidity': liquidity_usd,
                        'pair_address': pair.get('pairAddress', ''),
                        'pair_data': pair
                    }

            # Find arbitrage opportunities between DEXs
            dex_list = list(dex_prices.items())
            for i, (dex1_id, dex1_data) in enumerate(dex_list):
                for j, (dex2_id, dex2_data) in enumerate(dex_list[i+1:], i+1):

                    price1 = dex1_data["price"]
                    price2 = dex2_data["price"]

                    if price1 <= 0 or price2 <= 0:
                        continue

                    # Calculate profit percentage
                    if price2 > price1:
                        profit_pct = ((price2 - price1) / price1) * 100
                        buy_dex = dex1_id
                        sell_dex = dex2_id
                        buy_price = price1
                        sell_price = price2
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                    else:
                        profit_pct = ((price1 - price2) / price2) * 100
                        buy_dex = dex2_id
                        sell_dex = dex1_id
                        buy_price = price2
                        sell_price = price1
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])

                    # Check if profitable
                    if (self.config["profit_min"] < profit_pct <= self.config["profit_max"] and
                        min_liquidity > self.config["min_liquidity"]):

                        opportunity = {
                            "id": f"{token_symbol}_{chain}_{int(time.time())}_{i}_{j}",
                            "token_symbol": token_symbol,
                            "buy_exchange": f"{chain}_{buy_dex}",
                            "sell_exchange": f"{chain}_{sell_dex}",
                            "buy_price": round(buy_price, 6),
                            "sell_price": round(sell_price, 6),
                            "profit_percentage": round(profit_pct, 4),
                            "profit_usd": round(profit_pct * 10, 2),
                            "min_liquidity": round(min_liquidity, 2),
                            "buy_chain": chain,
                            "sell_chain": chain,
                            "buy_dex_name": buy_dex,
                            "sell_dex_name": sell_dex,
                            "buy_pair_address": dex_prices[buy_dex].get("pair_address"),
                            "sell_pair_address": dex_prices[sell_dex].get("pair_address"),
                            "timestamp": datetime.now().isoformat(),
                            "type": "high_performance_scan"
                        }

                        opportunities.append(opportunity)

            return opportunities

        except Exception as e:
            logger.error(f"Arbitrage finding error: {e}")
            return []

    async def _fallback_detailed_scan(self, tokens: List[str]) -> List[Dict]:
        """Fallback to detailed scanning if high-performance scan yields few results"""
        opportunities = []

        try:
            self.add_log("🔍 Running detailed fallback scan for better accuracy...", "info")

            # Use original detailed scanning method for a subset of tokens
            for chain in self.config["enabled_chains"]:
                for token in tokens[:10]:  # Limit to 10 tokens for fallback
                    token_opportunities = await self.scan_token_same_chain(token, chain)
                    opportunities.extend(token_opportunities)
                    await asyncio.sleep(0.1)  # Small delay

            return opportunities

        except Exception as e:
            logger.error(f"Fallback scan error: {e}")
            return []  # Remove duplicates

    def calculate_slippage_estimate(self, order_size_usd: float, liquidity_usd: float) -> float:
        """Estimate slippage based on order size vs liquidity"""
        if liquidity_usd <= 0:
            return 100.0  # 100% slippage if no liquidity

        # Simple slippage model: slippage increases quadratically with order size
        liquidity_ratio = order_size_usd / liquidity_usd

        if liquidity_ratio <= 0.001:  # < 0.1% of pool
            return 0.01  # ~0.01% slippage
        elif liquidity_ratio <= 0.01:  # < 1% of pool
            return 0.1 + (liquidity_ratio * 10)  # 0.1-1.1% slippage
        elif liquidity_ratio <= 0.05:  # < 5% of pool
            return 1.0 + (liquidity_ratio * 20)  # 1-2% slippage
        else:
            return min(50.0, 2.0 + (liquidity_ratio * 100))  # 2-50% slippage

    def calculate_risk_level(self, liquidity_ratio: float, slippage: float) -> str:
        """Calculate risk level based on liquidity ratio and slippage"""
        if liquidity_ratio >= 20 and slippage <= 0.5:
            return "Low"
        elif liquidity_ratio >= 10 and slippage <= 1.0:
            return "Medium"
        else:
            return "High"

    async def filter_opportunities_by_simulation(self, opportunities: List[Dict]) -> List[Dict]:
        """Filter opportunities based on simulation parameters with enhanced validation"""
        filtered_opportunities = []
        capital = self.config["simulation_capital"]
        max_slippage = self.config["max_slippage"]
        capital_per_trade = (self.config["capital_per_trade"] / 100) * capital
        min_liquidity_ratio = self.config["min_liquidity_ratio"]

        # Process opportunities with enhanced validation
        for opp in opportunities:
            min_liquidity = opp.get("min_liquidity", 0)

            # Check if liquidity is sufficient (min_liquidity_ratio * capital)
            required_liquidity = capital_per_trade * min_liquidity_ratio
            if min_liquidity < required_liquidity:
                continue

            # Calculate slippage estimates
            buy_slippage = self.calculate_slippage_estimate(capital_per_trade, min_liquidity)
            sell_slippage = self.calculate_slippage_estimate(capital_per_trade, min_liquidity)
            total_slippage = buy_slippage + sell_slippage

            # Filter by max slippage
            if total_slippage > max_slippage:
                continue

            # Calculate simulation-specific metrics
            liquidity_ratio = min_liquidity / capital_per_trade
            risk_level = self.calculate_risk_level(liquidity_ratio, total_slippage)

            # Calculate profit in USD based on simulation capital
            profit_percentage = opp.get("profit_percentage", 0)
            estimated_profit_usd = (profit_percentage / 100) * capital_per_trade

            # Add simulation data to opportunity
            opp.update({
                "simulation_capital": capital_per_trade,
                "estimated_profit_usd": round(estimated_profit_usd, 2),
                "buy_slippage": round(buy_slippage, 3),
                "sell_slippage": round(sell_slippage, 3),
                "total_slippage": round(total_slippage, 3),
                "liquidity_ratio": round(liquidity_ratio, 1),
                "risk_level": risk_level,
                "recommended_order_size": round(min(capital_per_trade, min_liquidity * 0.01), 2)  # Max 1% of liquidity
            })

            # ENHANCED: Apply real-time validation
            enhanced_opp = await self.enhance_opportunity_with_validation(opp)

            # Only include opportunities that pass enhanced validation
            validation_result = enhanced_opp.get('validation_result', {})
            if validation_result.get('is_valid', False) and validation_result.get('feasibility_score', 0) >= 60:
                filtered_opportunities.append(enhanced_opp)
            else:
                # Log why opportunity was filtered out
                warnings = validation_result.get('warnings', [])
                self.add_log(f"🚫 Filtered out {opp.get('token_symbol', 'Unknown')}: {', '.join(warnings[:2])}", "warning")

        return filtered_opportunities

    async def scan_opportunities(self) -> List[Dict[str, Any]]:
        """HIGH-PERFORMANCE: Scan untuk peluang arbitrase dengan parallel processing"""
        self.add_log("🚀 Memulai enhanced high-performance scanning...", "info")
        opportunities = []
        scan_start_time = time.time()

        try:
            # Clear expired cache entries
            self.smart_cache.clear_expired()

            # Get enabled tokens based on configuration (expanded to 500+)
            enabled_tokens = self.get_enabled_tokens_expanded()
            enabled_chains = self.config["enabled_chains"]

            self.add_log(f"🎯 High-performance scan: {len(enabled_tokens)} tokens across {len(enabled_chains)} chains", "info")

            # ENHANCED: Use parallel scanning for optimal performance
            all_pairs = await self.parallel_scanner.scan_chains_parallel(
                enabled_tokens, enabled_chains, dexscreener
            )

            # Update scan metrics
            self.scan_metrics = self.parallel_scanner.scan_metrics

            self.add_log(f"⚡ Parallel scan completed: {self.scan_metrics.tokens_per_second:.1f} tokens/sec", "info")
            self.add_log(f"📊 Cache hit rate: {self.scan_metrics.cache_hit_rate:.1f}%", "info")

            # Process pairs for arbitrage opportunities
            opportunities = await self._process_pairs_for_arbitrage(all_pairs, enabled_tokens)

            # Legacy fallback for detailed analysis (if needed)
            if len(opportunities) < 5:  # If not enough opportunities found
                self.add_log("🔄 Running detailed fallback scan...", "info")
                fallback_opportunities = await self._fallback_detailed_scan(enabled_tokens[:20])  # Limit to 20 for fallback
                opportunities.extend(fallback_opportunities)

            # Remove duplicates and sort by profit
            unique_opportunities = self.deduplicate_opportunities(opportunities)
            unique_opportunities.sort(key=lambda x: x.get('profit_percentage', 0), reverse=True)

            # Apply enhanced simulation filtering with real-time validation
            filtered_opportunities = await self.filter_opportunities_by_simulation(unique_opportunities)

            self.opportunities = filtered_opportunities[:20]  # Keep top 20
            self.last_scan = datetime.now().isoformat()

            total_found = len(unique_opportunities)
            feasible_count = len(filtered_opportunities)

            # Generate safety summary
            safety_summary = self.create_safe_opportunity_summary(filtered_opportunities)

            self.add_log(f"✅ Pemindaian selesai! Ditemukan {total_found} peluang, {feasible_count} layak dengan modal simulasi", "success")
            self.add_log(f"🔒 Safety: {safety_summary['validated_opportunities']}/{feasible_count} validated ({safety_summary['validation_rate']}%), Safety Score: {safety_summary['safety_score']}", "info")

            if safety_summary['high_risk_opportunities'] > 0:
                self.add_log(f"⚠️ WARNING: {safety_summary['high_risk_opportunities']} opportunities flagged as HIGH RISK - verify manually!", "warning")

        except Exception as e:
            self.add_log(f"❌ Error pemindaian: {str(e)}", "error")
            logger.error(f"Scan error: {e}")

        return self.opportunities

    async def scan_token_same_chain(self, token_symbol: str, target_chain: str) -> List[Dict[str, Any]]:
        """Scan token untuk arbitrase antar DEX dalam blockchain yang sama"""
        opportunities = []

        try:
            # Search for token pairs on DexScreener untuk chain tertentu
            search_data = await dexscreener.get("dex/search", {"q": token_symbol})

            if "pairs" not in search_data or not search_data["pairs"]:
                return opportunities

            # Filter pairs hanya untuk target chain
            chain_pairs = [
                pair for pair in search_data["pairs"]
                if pair.get("chainId", "").lower() == target_chain.lower()
            ]

            if len(chain_pairs) < 2:
                self.add_log(f"⚠️ Tidak cukup DEX untuk {token_symbol} di {target_chain}", "warning")
                return opportunities

            self.add_log(f"📈 Ditemukan {len(chain_pairs)} pairs {token_symbol} di {target_chain}", "info")

            # ENHANCED: Group pairs by DEX dengan validasi token identity yang ketat
            dex_prices = {}
            valid_pairs = []

            # First pass: Collect valid pairs dengan token validation
            for pair in chain_pairs:
                try:
                    if not self.is_valid_pair(pair):
                        continue

                    # CRITICAL: Validate token symbol matches exactly
                    base_token = pair.get("baseToken", {})
                    pair_symbol = base_token.get("symbol", "").upper()

                    if pair_symbol != token_symbol.upper():
                        self.add_log(f"⚠️ SKIPPED: Symbol mismatch - Expected {token_symbol.upper()}, got {pair_symbol}", "warning")
                        continue

                    valid_pairs.append(pair)

                except (ValueError, TypeError):
                    continue

            if len(valid_pairs) < 2:
                self.add_log(f"⚠️ Insufficient valid pairs for {token_symbol} di {target_chain} after validation", "warning")
                return opportunities

            # Second pass: Group by DEX dengan additional validation
            for pair in valid_pairs:
                try:
                    dex_id = pair.get("dexId", "unknown")
                    price_usd = float(pair.get("priceUsd", 0))
                    liquidity = pair.get("liquidity", {}).get("usd", 0)
                    volume_24h = pair.get("volume", {}).get("h24", 0)

                    # Keep the pair with highest liquidity for each DEX
                    if dex_id not in dex_prices or liquidity > dex_prices[dex_id]["liquidity"]:
                        dex_prices[dex_id] = {
                            "price": price_usd,
                            "liquidity": liquidity,
                            "volume_24h": volume_24h,
                            "chain": target_chain,
                            "dex": dex_id,
                            "pair_address": pair.get("pairAddress"),
                            "base_token": pair.get("baseToken", {}).get("symbol", token_symbol),
                            "token_address": pair.get("baseToken", {}).get("address", ""),
                            "token_name": pair.get("baseToken", {}).get("name", ""),
                            "pair_data": pair  # Store full pair data for validation
                        }

                except (ValueError, TypeError):
                    continue

            # ENHANCED: Find arbitrage opportunities dengan strict token validation
            dex_list = list(dex_prices.items())

            for i, (dex1_id, dex1_data) in enumerate(dex_list):
                for j, (dex2_id, dex2_data) in enumerate(dex_list[i+1:], i+1):

                    # CRITICAL: Validate that both pairs represent the same token
                    pair1 = dex1_data.get("pair_data")
                    pair2 = dex2_data.get("pair_data")

                    if not pair1 or not pair2:
                        self.add_log(f"⚠️ Missing pair data for comparison", "warning")
                        continue

                    if not self.is_same_token(pair1, pair2, token_symbol):
                        self.add_log(f"🚫 BLOCKED: Token identity validation failed for {dex1_id} vs {dex2_id}", "error")
                        continue

                    price1 = dex1_data["price"]
                    price2 = dex2_data["price"]

                    if price1 <= 0 or price2 <= 0:
                        continue

                    # Additional price sanity check
                    price_ratio = max(price1, price2) / min(price1, price2)
                    if price_ratio > 5:  # 500% difference is suspicious
                        self.add_log(f"⚠️ SUSPICIOUS: Large price difference {price1} vs {price2} (ratio: {price_ratio:.2f})", "warning")
                        # Continue but flag as high risk

                    # Calculate profit percentage
                    if price2 > price1:
                        profit_pct = ((price2 - price1) / price1) * 100
                        buy_dex = dex1_id
                        sell_dex = dex2_id
                        buy_price = price1
                        sell_price = price2
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                        buy_dex_data = dex1_data
                        sell_dex_data = dex2_data
                    else:
                        profit_pct = ((price1 - price2) / price2) * 100
                        buy_dex = dex2_id
                        sell_dex = dex1_id
                        buy_price = price2
                        sell_price = price1
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                        buy_dex_data = dex2_data
                        sell_dex_data = dex1_data

                    # Check if profitable dengan konfigurasi yang dapat diubah
                    if (self.config["profit_min"] < profit_pct <= self.config["profit_max"] and
                        min_liquidity > self.config["min_liquidity"]):

                        # Enhanced opportunity data dengan validation info
                        opportunity = {
                            "id": f"{token_symbol}_{target_chain}_{int(time.time())}_{i}_{j}",
                            "token_symbol": token_symbol,
                            "buy_exchange": f"{target_chain}_{buy_dex}",
                            "sell_exchange": f"{target_chain}_{sell_dex}",
                            "buy_price": round(buy_price, 6),
                            "sell_price": round(sell_price, 6),
                            "profit_percentage": round(profit_pct, 4),
                            "profit_usd": round(profit_pct * 10, 2),
                            "min_liquidity": round(min_liquidity, 2),
                            "buy_chain": target_chain,
                            "sell_chain": target_chain,  # Same chain
                            "buy_dex_name": buy_dex,
                            "sell_dex_name": sell_dex,
                            "buy_pair_address": buy_dex_data.get("pair_address"),
                            "sell_pair_address": sell_dex_data.get("pair_address"),
                            "timestamp": datetime.now().isoformat(),
                            "type": "same_chain_arbitrage",
                            # ENHANCED: Token validation info
                            "token_validation": {
                                "buy_token_address": buy_dex_data.get("token_address", ""),
                                "sell_token_address": sell_dex_data.get("token_address", ""),
                                "buy_token_name": buy_dex_data.get("token_name", ""),
                                "sell_token_name": sell_dex_data.get("token_name", ""),
                                "price_ratio": round(price_ratio, 2),
                                "validated": True,
                                "validation_timestamp": datetime.now().isoformat()
                            }
                        }

                        # Add risk flag for high price ratios
                        if price_ratio > 2:
                            opportunity["risk_flags"] = ["high_price_difference"]
                            opportunity["risk_level"] = "High"

                        opportunities.append(opportunity)

            if opportunities:
                self.add_log(f"💰 Ditemukan {len(opportunities)} peluang arbitrase {token_symbol} di {target_chain}", "success")

        except Exception as e:
            self.add_log(f"❌ Error scanning {token_symbol} di {target_chain}: {str(e)}", "error")

        return opportunities

    async def scan_token_across_dexs(self, token_symbol: str) -> List[Dict[str, Any]]:
        """Scan a specific token across all DEXs for arbitrage opportunities"""
        opportunities = []

        try:
            # Search for token pairs on DexScreener
            search_data = await dexscreener.get("dex/search", {"q": token_symbol})

            if "pairs" not in search_data or not search_data["pairs"]:
                self.add_log(f"⚠️ No pairs found for {token_symbol}", "warning")
                return opportunities

            pairs = search_data["pairs"]
            self.add_log(f"📈 Found {len(pairs)} pairs for {token_symbol}", "info")

            # Group pairs by DEX and chain
            dex_prices = {}

            for pair in pairs:
                try:
                    # Filter valid pairs
                    if not self.is_valid_pair(pair):
                        continue

                    chain_id = pair.get("chainId", "unknown")
                    dex_id = pair.get("dexId", "unknown")
                    price_usd = float(pair.get("priceUsd", 0))
                    liquidity = pair.get("liquidity", {}).get("usd", 0)
                    volume_24h = pair.get("volume", {}).get("h24", 0)

                    key = f"{chain_id}_{dex_id}"

                    # Keep the pair with highest liquidity for each DEX
                    if key not in dex_prices or liquidity > dex_prices[key]["liquidity"]:
                        dex_prices[key] = {
                            "price": price_usd,
                            "liquidity": liquidity,
                            "volume_24h": volume_24h,
                            "chain": chain_id,
                            "dex": dex_id,
                            "pair_address": pair.get("pairAddress"),
                            "base_token": pair.get("baseToken", {}).get("symbol", token_symbol)
                        }

                except (ValueError, TypeError) as e:
                    continue

            # Find arbitrage opportunities between DEXs
            dex_list = list(dex_prices.items())

            for i, (dex1_key, dex1_data) in enumerate(dex_list):
                for j, (dex2_key, dex2_data) in enumerate(dex_list[i+1:], i+1):

                    price1 = dex1_data["price"]
                    price2 = dex2_data["price"]

                    if price1 <= 0 or price2 <= 0:
                        continue

                    # Calculate profit percentage
                    if price2 > price1:
                        profit_pct = ((price2 - price1) / price1) * 100
                        buy_dex = dex1_key
                        sell_dex = dex2_key
                        buy_price = price1
                        sell_price = price2
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                    else:
                        profit_pct = ((price1 - price2) / price2) * 100
                        buy_dex = dex2_key
                        sell_dex = dex1_key
                        buy_price = price2
                        sell_price = price1
                        min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])

                    # Check if profitable dengan konfigurasi yang dapat diubah
                    if (self.config["profit_min"] < profit_pct <= self.config["profit_max"] and
                        min_liquidity > self.config["min_liquidity"]):
                        # Get DEX data for links
                        buy_dex_data = dex_prices[buy_dex]
                        sell_dex_data = dex_prices[sell_dex]

                        opportunities.append({
                            "id": f"{token_symbol}_{int(time.time())}_{i}_{j}",
                            "token_symbol": token_symbol,
                            "buy_exchange": buy_dex,
                            "sell_exchange": sell_dex,
                            "buy_price": round(buy_price, 6),
                            "sell_price": round(sell_price, 6),
                            "profit_percentage": round(profit_pct, 4),
                            "profit_usd": round(profit_pct * 10, 2),  # Assuming $1000 trade
                            "min_liquidity": round(min_liquidity, 2),
                            "buy_chain": buy_dex_data["chain"],
                            "sell_chain": sell_dex_data["chain"],
                            "buy_dex_name": buy_dex_data["dex"],
                            "sell_dex_name": sell_dex_data["dex"],
                            "buy_pair_address": buy_dex_data.get("pair_address"),
                            "sell_pair_address": sell_dex_data.get("pair_address"),
                            "timestamp": datetime.now().isoformat(),
                            "type": "dex_arbitrage"
                        })

            if opportunities:
                self.add_log(f"💰 Ditemukan {len(opportunities)} peluang arbitrase {token_symbol}", "success")
            else:
                self.add_log(f"📊 Tidak ada peluang arbitrase valid untuk {token_symbol}", "info")

        except Exception as e:
            self.add_log(f"❌ Error scanning {token_symbol}: {str(e)}", "error")

        return opportunities

    async def scan_trending_pairs(self) -> List[Dict[str, Any]]:
        """Scan trending pairs for additional opportunities"""
        opportunities = []

        try:
            # Get some popular pairs directly
            trending_searches = ["ETH", "BTC", "MATIC", "ARB", "OP"]

            for search_term in trending_searches:
                pairs_data = await dexscreener.get("dex/search", {"q": search_term})

                if "pairs" in pairs_data and pairs_data["pairs"]:
                    # Take top 5 pairs by volume
                    top_pairs = sorted(
                        pairs_data["pairs"][:10],
                        key=lambda x: x.get("volume", {}).get("h24", 0),
                        reverse=True
                    )[:5]

                    for pair in top_pairs:
                        if self.is_valid_pair(pair):
                            token_symbol = pair.get("baseToken", {}).get("symbol", search_term)
                            token_opportunities = await self.scan_token_across_dexs(token_symbol)
                            opportunities.extend(token_opportunities)

                await asyncio.sleep(0.3)  # Rate limiting

        except Exception as e:
            self.add_log(f"❌ Error scanning trending pairs: {str(e)}", "error")

        return opportunities

    def is_valid_pair(self, pair: Dict) -> bool:
        """Check if a pair is valid for arbitrage berdasarkan konfigurasi"""
        try:
            price_usd = float(pair.get("priceUsd", 0))
            liquidity = pair.get("liquidity", {}).get("usd", 0)
            volume_24h = pair.get("volume", {}).get("h24", 0)

            # Validation criteria menggunakan konfigurasi
            return (
                price_usd > 0 and
                liquidity > self.config["min_liquidity"] and
                volume_24h > self.config["min_volume_24h"] and
                pair.get("chainId") and
                pair.get("dexId") and
                pair.get("baseToken", {}).get("symbol")
            )
        except (ValueError, TypeError):
            return False

    def is_same_token(self, pair1: Dict, pair2: Dict, target_symbol: str) -> bool:
        """
        CRITICAL: Validate that two pairs represent the SAME token
        This prevents dangerous mismatches like WETH/SOL vs different tokens
        """
        try:
            # Get token information from both pairs
            token1 = pair1.get("baseToken", {})
            token2 = pair2.get("baseToken", {})

            # Primary validation: Contract addresses must match (most reliable)
            addr1 = token1.get("address", "").lower()
            addr2 = token2.get("address", "").lower()

            # If both have addresses and they're different, it's NOT the same token
            if addr1 and addr2 and addr1 != addr2:
                self.add_log(f"⚠️ DANGER: Different token addresses detected! {addr1} vs {addr2}", "error")
                return False

            # Secondary validation: Symbol matching
            symbol1 = token1.get("symbol", "").upper()
            symbol2 = token2.get("symbol", "").upper()
            target_upper = target_symbol.upper()

            # All symbols must match the target
            if symbol1 != target_upper or symbol2 != target_upper:
                self.add_log(f"⚠️ DANGER: Symbol mismatch! Expected {target_upper}, got {symbol1} vs {symbol2}", "error")
                return False

            # Tertiary validation: Name similarity check
            name1 = token1.get("name", "").lower()
            name2 = token2.get("name", "").lower()

            # If names are very different, flag as suspicious
            if name1 and name2 and name1 != name2:
                # Allow some variation but flag major differences
                if len(name1) > 3 and len(name2) > 3:
                    # Simple similarity check
                    common_chars = set(name1) & set(name2)
                    if len(common_chars) < min(len(name1), len(name2)) * 0.5:
                        self.add_log(f"⚠️ WARNING: Suspicious name difference: '{name1}' vs '{name2}'", "warning")
                        return False

            # Additional validation: Price sanity check
            price1 = float(pair1.get("priceUsd", 0))
            price2 = float(pair2.get("priceUsd", 0))

            if price1 > 0 and price2 > 0:
                # If prices differ by more than 1000%, it's likely different tokens
                price_ratio = max(price1, price2) / min(price1, price2)
                if price_ratio > 10:  # 1000% difference
                    self.add_log(f"⚠️ DANGER: Extreme price difference detected! {price1} vs {price2} (ratio: {price_ratio:.2f})", "error")
                    return False

            return True

        except Exception as e:
            self.add_log(f"❌ Error validating token identity: {str(e)}", "error")
            return False

    async def validate_token_by_address(self, token_address: str, chain: str) -> Dict:
        """
        Additional validation: Lookup token info by contract address
        This provides extra verification for token identity
        """
        try:
            if not token_address or len(token_address) < 10:
                return {}

            # Use DexScreener token endpoint for additional validation
            token_data = await dexscreener.get(f"dex/tokens/{token_address}")

            if "pairs" in token_data and token_data["pairs"]:
                # Get token info from the first pair
                first_pair = token_data["pairs"][0]
                base_token = first_pair.get("baseToken", {})

                return {
                    "address": base_token.get("address", ""),
                    "symbol": base_token.get("symbol", ""),
                    "name": base_token.get("name", ""),
                    "verified": True
                }

            return {}

        except Exception as e:
            self.add_log(f"⚠️ Token address validation failed: {str(e)}", "warning")
            return {}

    async def enhance_opportunity_with_validation(self, opportunity: Dict) -> Dict:
        """
        CRITICAL: Enhance opportunity with comprehensive real-time validation
        """
        try:
            # Get basic opportunity data
            buy_pair_address = opportunity.get('buy_pair_address', '')
            sell_pair_address = opportunity.get('sell_pair_address', '')
            chain = opportunity.get('buy_chain', '')
            capital_per_trade = self.config.get('simulation_capital', 100)

            # 1. Validate liquidity depth
            buy_liquidity_valid, buy_depth_score, buy_warnings = await self.validator.validate_liquidity_depth(
                buy_pair_address, chain, capital_per_trade
            )
            sell_liquidity_valid, sell_depth_score, sell_warnings = await self.validator.validate_liquidity_depth(
                sell_pair_address, chain, capital_per_trade
            )

            # 2. Validate execution feasibility
            execution_feasible, execution_time, execution_warnings = await self.validator.validate_execution_feasibility(opportunity)

            # 3. Filter false positives
            is_legitimate, confidence_score, fp_warnings = await self.validator.filter_false_positives(opportunity)

            # Calculate overall feasibility score
            liquidity_score = min(buy_depth_score, sell_depth_score)
            feasibility_score = int((liquidity_score + confidence_score) / 2)

            # Collect all warnings
            all_warnings = buy_warnings + sell_warnings + execution_warnings + fp_warnings

            # Determine overall validation status
            is_valid = (buy_liquidity_valid and sell_liquidity_valid and
                       execution_feasible and is_legitimate and
                       feasibility_score >= 60)

            # 4. ENHANCED: Security analysis
            buy_token_address = opportunity.get('token_validation', {}).get('buy_token_address', '')
            sell_token_address = opportunity.get('token_validation', {}).get('sell_token_address', '')

            security_data = {}
            if buy_token_address and len(buy_token_address) > 10:
                # Check security for buy token
                goplus_data = await self.security_analyzer.check_goplus_security(buy_token_address, chain)
                quickintel_data = await self.security_analyzer.check_quickintel_rating(buy_token_address, chain)

                # Generate combined security score
                combined_security_score = await self.security_analyzer.generate_security_score(goplus_data, quickintel_data)
                security_flags = await self.security_analyzer.get_security_flags(goplus_data, quickintel_data)

                security_data = {
                    'security_score': combined_security_score,
                    'goplus_data': goplus_data,
                    'quickintel_data': quickintel_data,
                    'security_flags': security_flags,
                    'is_honeypot': goplus_data.get('is_honeypot', False),
                    'audit_status': goplus_data.get('audit_status', 'unknown'),
                    'owner_privileges': goplus_data.get('owner_privileges', [])
                }

                # Apply security-based filtering
                if combined_security_score < 30:
                    is_valid = False
                    all_warnings.append(f"CRITICAL: Very low security score ({combined_security_score}/100)")

                if goplus_data.get('is_honeypot', False):
                    is_valid = False
                    all_warnings.append("CRITICAL: Token flagged as honeypot")

                # Adjust feasibility score based on security
                security_penalty = max(0, (70 - combined_security_score) // 2)
                feasibility_score = max(0, feasibility_score - security_penalty)

            # Create validation result
            validation_result = ValidationResult(
                is_valid=is_valid,
                feasibility_score=feasibility_score,
                warnings=all_warnings,
                security_flags=security_data.get('security_flags', []),
                execution_time_estimate=execution_time,
                liquidity_depth_score=liquidity_score
            )

            # Enhance opportunity with validation data
            opportunity.update({
                'validation_result': {
                    'is_valid': validation_result.is_valid,
                    'feasibility_score': validation_result.feasibility_score,
                    'warnings': validation_result.warnings,
                    'execution_time_estimate': validation_result.execution_time_estimate,
                    'liquidity_depth_score': validation_result.liquidity_depth_score,
                    'confidence_score': confidence_score,
                    'validation_timestamp': datetime.now().isoformat()
                },
                'enhanced_validation': True,
                'security_data': security_data
            })

            # Update risk level based on validation and security
            security_score = security_data.get('security_score', 50)

            if not is_valid or feasibility_score < 40 or security_score < 30:
                opportunity['risk_level'] = 'High'
                risk_flags = opportunity.get('risk_flags', []) + ['validation_failed']
                if security_score < 30:
                    risk_flags.append('security_risk')
                opportunity['risk_flags'] = risk_flags
            elif feasibility_score < 70 or security_score < 60:
                opportunity['risk_level'] = 'Medium'
                if security_score < 60:
                    opportunity['risk_flags'] = opportunity.get('risk_flags', []) + ['moderate_security_risk']
            else:
                opportunity['risk_level'] = 'Low'

            return opportunity

        except Exception as e:
            logger.error(f"Enhanced validation error: {e}")
            opportunity['validation_result'] = {
                'is_valid': False,
                'feasibility_score': 0,
                'warnings': [f"Validation error: {str(e)}"],
                'execution_time_estimate': 300,
                'liquidity_depth_score': 0,
                'confidence_score': 0,
                'validation_timestamp': datetime.now().isoformat()
            }
            opportunity['risk_level'] = 'High'
            opportunity['risk_flags'] = opportunity.get('risk_flags', []) + ['validation_error']
            return opportunity

    def create_safe_opportunity_summary(self, opportunities: List[Dict]) -> Dict:
        """
        Create a summary with safety information about detected opportunities
        """
        total_opportunities = len(opportunities)
        validated_opportunities = len([opp for opp in opportunities if opp.get("token_validation", {}).get("validated", False)])
        high_risk_opportunities = len([opp for opp in opportunities if opp.get("risk_flags")])

        return {
            "total_opportunities": total_opportunities,
            "validated_opportunities": validated_opportunities,
            "high_risk_opportunities": high_risk_opportunities,
            "validation_rate": round((validated_opportunities / total_opportunities) * 100, 1) if total_opportunities > 0 else 0,
            "safety_score": "High" if high_risk_opportunities == 0 else "Medium" if high_risk_opportunities < total_opportunities * 0.3 else "Low"
        }

    def deduplicate_opportunities(self, opportunities: List[Dict]) -> List[Dict]:
        """Remove duplicate opportunities"""
        seen = set()
        unique_opportunities = []

        for opp in opportunities:
            # Create a key based on token, exchanges, and similar profit
            key = (
                opp.get("token_symbol"),
                tuple(sorted([opp.get("buy_exchange"), opp.get("sell_exchange")])),
                round(opp.get("profit_percentage", 0), 2)
            )

            if key not in seen:
                seen.add(key)
                unique_opportunities.append(opp)

        return unique_opportunities

    def get_dexscreener_link(self, chain: str, dex: str, pair_address: str = None) -> str:
        """Generate DexScreener link for a specific DEX pair"""
        base_url = "https://dexscreener.com"

        # Chain mapping for DexScreener URLs
        chain_mapping = {
            "ethereum": "ethereum",
            "bsc": "bsc",
            "polygon": "polygon",
            "arbitrum": "arbitrum",
            "avalanche": "avalanche",
            "solana": "solana",
            "optimism": "optimism"
        }

        mapped_chain = chain_mapping.get(chain.lower(), chain.lower())

        if pair_address:
            return f"{base_url}/{mapped_chain}/{pair_address}"
        else:
            # If no pair address, link to DEX page
            return f"{base_url}/{mapped_chain}"

    async def get_btc_prices(self) -> Dict[str, float]:
        """Get BTC prices from different sources"""
        prices = {}
        
        # CoinGecko
        try:
            cg_data = await coingecko.get("simple/price", {
                "ids": "bitcoin",
                "vs_currencies": "usd"
            })
            if "bitcoin" in cg_data and "usd" in cg_data["bitcoin"]:
                prices["coingecko"] = float(cg_data["bitcoin"]["usd"])
        except:
            pass
        
        # Binance
        try:
            binance_data = await binance.get("ticker/24hr", {"symbol": "BTCUSDT"})
            if "lastPrice" in binance_data:
                prices["binance"] = float(binance_data["lastPrice"])
        except:
            pass
        
        # DexScreener (search for WBTC)
        try:
            dex_data = await dexscreener.get("dex/search", {"q": "WBTC"})
            if "pairs" in dex_data and dex_data["pairs"]:
                # Get first valid pair
                for pair in dex_data["pairs"][:3]:
                    if pair.get("priceUsd"):
                        prices[f"dex_{pair.get('dexId', 'unknown')}"] = float(pair["priceUsd"])
                        break
        except:
            pass
        
        return prices

# Global instances
websocket_manager = SimpleWebSocketManager()
coingecko = SimpleAPIClient("https://api.coingecko.com/api/v3", 10)
dexscreener = SimpleAPIClient("https://api.dexscreener.com/latest", 60)
binance = SimpleAPIClient("https://api.binance.com/api/v3", 300)
detector = AdvancedArbitrageDetector()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Enhanced Crypto Arbitrage Bot v2.0...")

    try:
        logger.info("✅ Application startup complete - Enhanced version with token categories and simulation")
        yield
        
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down application...")
        
        # Cleanup HTTP clients
        await coingecko.client.aclose()
        await dexscreener.client.aclose()
        await binance.client.aclose()
        logger.info("HTTP clients cleaned up")
        
        logger.info("✅ Application shutdown complete")

# Create FastAPI app
app = FastAPI(
    title="Crypto Arbitrage Bot v2.0",
    description="Advanced cryptocurrency arbitrage detection with public APIs only",
    version="2.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the frontend application"""
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Crypto Arbitrage Bot v2.0 - iOS Futuristic</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            @import url('https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap');

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                min-height: 100vh;
                color: #1f2937;
                overflow-x: hidden;
            }

            .background-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
                z-index: -1;
            }

            .container {
                max-width: 1400px;
                margin: 0 auto;
                padding: 20px;
                position: relative;
                z-index: 1;
            }

            .header {
                text-align: center;
                margin-bottom: 40px;
                padding: 40px 20px;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border-radius: 30px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }

            .header h1 {
                font-size: 3.5rem;
                font-weight: 700;
                background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 10px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .header p {
                font-size: 1.2rem;
                color: #6b7280;
                font-weight: 300;
            }

            .grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
                margin-bottom: 30px;
            }

            .card {
                background: rgba(227, 242, 253, 0.7);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 30px;
                border: 1px solid rgba(0, 0, 0, 0.08);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, #ff9a9e, #fecfef, #fecfef);
                opacity: 0.7;
            }

            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            }

            .card h2 {
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 20px;
                color: #fecfef;
            }

            .button-group {
                display: flex;
                gap: 15px;
                flex-wrap: wrap;
            }

            .btn {
                background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 15px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 0.95rem;
                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
                position: relative;
                overflow: hidden;
            }

            .btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }

            .btn:hover::before {
                left: 100%;
            }

            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            }

            .btn-scan {
                background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
                box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);
            }

            .btn-scan:hover {
                box-shadow: 0 8px 25px rgba(255, 154, 158, 0.6);
            }

            .opportunities-container {
                grid-column: 1 / -1;
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 30px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                min-height: 400px;
            }

            .opportunity {
                background: rgba(255, 255, 255, 0.8);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                padding: 20px;
                margin: 15px 0;
                border: 1px solid rgba(0, 0, 0, 0.08);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                transition: all 0.3s ease;
                position: relative;
            }

            .opportunity:hover {
                transform: scale(1.02);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }

            .opportunity h3 {
                color: #1f2937;
                font-size: 1.3rem;
                margin-bottom: 10px;
                font-weight: 600;
            }

            .profit-positive {
                color: #10b981;
                font-weight: 700;
                font-size: 1.1rem;
                text-shadow: 0 1px 2px rgba(16, 185, 129, 0.2);
            }

            .profit-negative {
                color: #ef4444;
                font-weight: 700;
                font-size: 1.1rem;
                text-shadow: 0 1px 2px rgba(239, 68, 68, 0.2);
            }

            .logs-container {
                background: rgba(248, 250, 252, 0.8);
                backdrop-filter: blur(15px);
                border-radius: 20px;
                padding: 25px;
                border: 1px solid rgba(0, 0, 0, 0.08);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                max-height: 400px;
                overflow-y: auto;
            }

            .log-entry {
                display: flex;
                align-items: center;
                padding: 8px 12px;
                margin: 5px 0;
                border-radius: 12px;
                font-size: 0.9rem;
                transition: all 0.2s ease;
                border-left: 3px solid transparent;
            }

            .log-entry:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .log-entry.info {
                border-left-color: #60a5fa;
                background: rgba(219, 234, 254, 0.7);
                color: #1e40af;
            }

            .log-entry.success {
                border-left-color: #34d399;
                background: rgba(232, 245, 232, 0.7);
                color: #065f46;
            }

            .log-entry.warning {
                border-left-color: #fbbf24;
                background: rgba(255, 243, 224, 0.7);
                color: #92400e;
            }

            .log-entry.error {
                border-left-color: #f87171;
                background: rgba(255, 235, 238, 0.7);
                color: #991b1b;
            }

            .log-time {
                font-size: 0.8rem;
                opacity: 0.7;
                margin-right: 10px;
                min-width: 60px;
            }

            .status-indicator {
                display: inline-block;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin-right: 10px;
                animation: pulse 2s infinite;
            }

            .status-online {
                background: #34d399;
                box-shadow: 0 0 10px rgba(52, 211, 153, 0.5);
            }

            .status-scanning {
                background: #fbbf24;
                box-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }

            .stat-card {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 20px;
                text-align: center;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .stat-value {
                font-size: 2rem;
                font-weight: 700;
                color: #fecfef;
                margin-bottom: 5px;
            }

            .stat-label {
                font-size: 0.9rem;
                opacity: 0.8;
            }

            @media (max-width: 768px) {
                .grid {
                    grid-template-columns: 1fr;
                }

                .header h1 {
                    font-size: 2.5rem;
                }

                .button-group {
                    justify-content: center;
                }

                .container {
                    padding: 15px;
                }
            }

            /* Custom scrollbar */
            ::-webkit-scrollbar {
                width: 8px;
            }

            ::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
            }

            ::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 10px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: rgba(255, 255, 255, 0.5);
            }

            /* DexScreener link styling */
            .dex-link {
                display: inline-block;
                color: #60a5fa;
                text-decoration: none;
                font-size: 0.9rem;
                font-weight: 500;
                padding: 6px 12px;
                background: rgba(96, 165, 250, 0.1);
                border-radius: 8px;
                border: 1px solid rgba(96, 165, 250, 0.3);
                transition: all 0.3s ease;
                margin-top: 8px;
            }

            .dex-link:hover {
                background: rgba(96, 165, 250, 0.2);
                border-color: rgba(96, 165, 250, 0.5);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }

            .dex-link:active {
                transform: translateY(0);
            }

            /* Author Profile Styling */
            .author-profile {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                cursor: pointer;
            }

            .author-avatar {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 700;
                font-size: 1.2rem;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
                transition: all 0.3s ease;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }

            .author-avatar:hover {
                transform: scale(1.1);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            }

            /* Modal Styling */
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(10px);
                display: none;
                align-items: center;
                justify-content: center;
                z-index: 2000;
                animation: fadeIn 0.3s ease;
            }

            .modal-content {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 40px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                text-align: center;
                max-width: 400px;
                width: 90%;
                animation: scaleIn 0.3s ease;
            }

            .modal-avatar {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 700;
                font-size: 2rem;
                margin: 0 auto 20px;
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }

            .modal-title {
                font-size: 1.8rem;
                font-weight: 700;
                color: #fecfef;
                margin-bottom: 10px;
            }

            .modal-subtitle {
                font-size: 1rem;
                opacity: 0.8;
                margin-bottom: 30px;
            }

            .social-links {
                display: flex;
                justify-content: center;
                gap: 20px;
                margin-bottom: 30px;
            }

            .social-link {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 50px;
                height: 50px;
                border-radius: 15px;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                text-decoration: none;
                font-size: 1.5rem;
                transition: all 0.3s ease;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .social-link:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
                background: rgba(255, 255, 255, 0.2);
            }

            .close-modal {
                background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 15px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .close-modal:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(255, 154, 158, 0.4);
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes scaleIn {
                from { transform: scale(0.8); opacity: 0; }
                to { transform: scale(1); opacity: 1; }
            }

            /* Configuration Panel Styling */
            .config-panel {
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 25px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin-bottom: 30px;
            }

            .config-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                cursor: pointer;
            }

            .config-content {
                display: none;
                animation: slideDown 0.3s ease;
            }

            .config-content.expanded {
                display: block;
            }

            .config-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }

            .config-item {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 20px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .config-label {
                display: flex;
                align-items: center;
                font-weight: 600;
                margin-bottom: 10px;
                color: #fecfef;
            }

            .config-input {
                width: 100%;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                padding: 10px 15px;
                color: white;
                font-size: 0.9rem;
            }

            .config-input:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
            }

            .checkbox-group {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .checkbox-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .checkbox-item input[type="checkbox"] {
                width: 18px;
                height: 18px;
                accent-color: #667eea;
            }

            @keyframes slideDown {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            /* Token Categories Styling */
            .token-categories {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 15px 0;
            }

            .token-category {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
                padding: 15px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            .token-category h4 {
                margin: 0 0 10px 0;
                color: #fecfef;
                font-size: 0.9rem;
                font-weight: 600;
            }

            .token-list {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }

            .token-chip {
                background: rgba(102, 126, 234, 0.2);
                color: #a5b4fc;
                padding: 4px 8px;
                border-radius: 8px;
                font-size: 0.8rem;
                border: 1px solid rgba(102, 126, 234, 0.3);
            }

            /* Simulation Panel Styling */
            .simulation-panel {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 15px;
                padding: 20px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin-top: 20px;
            }

            .simulation-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 15px 0;
            }

            .simulation-summary {
                background: rgba(34, 197, 94, 0.1);
                border-radius: 12px;
                padding: 15px;
                border: 1px solid rgba(34, 197, 94, 0.3);
                margin-top: 15px;
            }

            .summary-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 5px 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            .summary-item:last-child {
                border-bottom: none;
            }

            .summary-value {
                font-weight: 600;
                color: #a7f3d0;
            }

            /* Risk Level Indicators */
            .risk-low { color: #10b981; }
            .risk-medium { color: #f59e0b; }
            .risk-high { color: #ef4444; }

            /* Tooltip Styling */
            .tooltip {
                position: relative;
                cursor: help;
            }

            .tooltip::after {
                content: attr(data-tooltip);
                position: absolute;
                bottom: 125%;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 8px 12px;
                border-radius: 8px;
                font-size: 0.8rem;
                white-space: nowrap;
                opacity: 0;
                pointer-events: none;
                transition: opacity 0.3s;
                z-index: 1000;
            }

            .tooltip:hover::after {
                opacity: 1;
            }

            /* Enhanced Opportunity Cards */
            .opportunity-enhanced {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                padding: 25px;
                margin: 20px 0;
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                position: relative;
            }

            .opportunity-enhanced:hover {
                transform: scale(1.02);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            }

            .opportunity-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }

            .risk-badge {
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 0.8rem;
                font-weight: 600;
                text-transform: uppercase;
            }

            .risk-badge.low {
                background: rgba(16, 185, 129, 0.2);
                color: #10b981;
                border: 1px solid rgba(16, 185, 129, 0.3);
            }

            .risk-badge.medium {
                background: rgba(245, 158, 11, 0.2);
                color: #f59e0b;
                border: 1px solid rgba(245, 158, 11, 0.3);
            }

            .risk-badge.high {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .simulation-metrics {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
                margin: 15px 0;
                padding: 15px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
            }

            .metric-item {
                text-align: center;
            }

            .metric-value {
                font-size: 1.2rem;
                font-weight: 700;
                color: #fecfef;
                margin-bottom: 5px;
            }

            .metric-label {
                font-size: 0.8rem;
                opacity: 0.8;
            }

            /* Solana-specific styling */
            .solana-highlight {
                background: linear-gradient(135deg, #9945FF 0%, #14F195 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                font-weight: 600;
            }

            .solana-badge {
                background: linear-gradient(135deg, #9945FF 0%, #14F195 100%);
                color: white;
                padding: 4px 8px;
                border-radius: 8px;
                font-size: 0.8rem;
                font-weight: 600;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            }

            .chain-solana {
                background: linear-gradient(135deg, rgba(153, 69, 255, 0.2) 0%, rgba(20, 241, 149, 0.2) 100%);
                border: 1px solid rgba(153, 69, 255, 0.3);
            }

            /* Enhanced Validation Styling */
            .validation-badge {
                display: inline-block;
                padding: 4px 8px;
                border-radius: 8px;
                font-size: 0.8rem;
                font-weight: 600;
                margin: 2px;
            }

            .validation-valid {
                background: rgba(34, 197, 94, 0.2);
                color: #10b981;
                border: 1px solid rgba(34, 197, 94, 0.3);
            }

            .validation-warning {
                background: rgba(245, 158, 11, 0.2);
                color: #f59e0b;
                border: 1px solid rgba(245, 158, 11, 0.3);
            }

            .validation-error {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .feasibility-score {
                display: inline-block;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.8rem;
                font-weight: 700;
                min-width: 40px;
                text-align: center;
            }

            .feasibility-high {
                background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
                color: white;
            }

            .feasibility-medium {
                background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
                color: white;
            }

            .feasibility-low {
                background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
                color: white;
            }

            .validation-details {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                padding: 10px;
                margin: 10px 0;
                border-left: 3px solid #3b82f6;
            }

            .validation-warning-list {
                list-style: none;
                padding: 0;
                margin: 5px 0;
            }

            .validation-warning-list li {
                padding: 2px 0;
                font-size: 0.85rem;
                color: #fbbf24;
            }

            .validation-warning-list li::before {
                content: "⚠️ ";
                margin-right: 5px;
            }

            /* Security Analysis Styling */
            .security-badges {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
                margin: 10px 0;
            }

            .security-badge {
                padding: 4px 8px;
                border-radius: 8px;
                font-size: 0.8rem;
                font-weight: 600;
                display: inline-block;
            }

            .goplus-high {
                background: rgba(34, 197, 94, 0.2);
                color: #10b981;
                border: 1px solid rgba(34, 197, 94, 0.3);
            }

            .goplus-medium {
                background: rgba(245, 158, 11, 0.2);
                color: #f59e0b;
                border: 1px solid rgba(245, 158, 11, 0.3);
            }

            .goplus-low {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .quickintel-a, .quickintel-b {
                background: rgba(34, 197, 94, 0.2);
                color: #10b981;
                border: 1px solid rgba(34, 197, 94, 0.3);
            }

            .quickintel-c {
                background: rgba(245, 158, 11, 0.2);
                color: #f59e0b;
                border: 1px solid rgba(245, 158, 11, 0.3);
            }

            .quickintel-d, .quickintel-f {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .security-details {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                padding: 12px;
                margin: 10px 0;
                border-left: 3px solid #8b5cf6;
            }

            .security-flag {
                display: inline-block;
                padding: 2px 6px;
                border-radius: 6px;
                font-size: 0.7rem;
                margin: 2px;
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .security-flag.honeypot {
                background: rgba(220, 38, 127, 0.2);
                color: #ec4899;
                border: 1px solid rgba(220, 38, 127, 0.3);
            }

            .security-flag.closed_source {
                background: rgba(245, 158, 11, 0.2);
                color: #f59e0b;
                border: 1px solid rgba(245, 158, 11, 0.3);
            }

            .honeypot-warning {
                background: rgba(220, 38, 127, 0.2);
                border: 1px solid rgba(220, 38, 127, 0.3);
                border-radius: 8px;
                padding: 10px;
                margin: 10px 0;
                color: #ec4899;
                font-weight: 600;
                text-align: center;
            }
        </style>
    </head>
    <body>
        <div class="background-overlay"></div>
        <div class="container">
            <!-- Author Profile -->
            <div class="author-profile" onclick="showAuthorModal()">
                <div class="author-avatar">BC</div>
            </div>

            <div class="header">
                <h1>🚀 Enhanced Arbitrage Bot v2.0</h1>
                <p><span class="status-indicator status-online"></span>Token Categories • Trading Simulation • Solana Support • Enhanced Filtering</p>
            </div>

            <!-- Configuration Panel -->
            <div class="config-panel">
                <div class="config-header" onclick="toggleConfig()">
                    <h2>⚙️ Panel Konfigurasi</h2>
                    <span id="config-toggle">▼</span>
                </div>
                <div class="config-content" id="config-content">
                    <div class="config-grid">
                        <div class="config-item">
                            <div class="config-label">💰 Profit Minimum (%)</div>
                            <input type="number" class="config-input" id="profit-min" value="0.05" step="0.01" min="0.01" max="100">
                        </div>
                        <div class="config-item">
                            <div class="config-label">📈 Profit Maksimal (%)</div>
                            <input type="number" class="config-input" id="profit-max" value="25" step="0.1" min="0.1" max="100">
                        </div>
                        <div class="config-item">
                            <div class="config-label">💧 Minimum Liquidity ($)</div>
                            <input type="number" class="config-input" id="min-liquidity" value="5000" step="100" min="100">
                        </div>
                        <div class="config-item">
                            <div class="config-label">📊 Volume 24h Minimum ($)</div>
                            <input type="number" class="config-input" id="min-volume" value="500" step="50" min="50">
                        </div>
                        <div class="config-item">
                            <div class="config-label">� Blockchain Aktif</div>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chain-ethereum" checked>
                                    <label for="chain-ethereum">Ethereum</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chain-bsc" checked>
                                    <label for="chain-bsc">BSC</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chain-polygon" checked>
                                    <label for="chain-polygon">Polygon</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chain-arbitrum" checked>
                                    <label for="chain-arbitrum">Arbitrum</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chain-solana" checked>
                                    <label for="chain-solana">Solana</label>
                                </div>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">⏱️ Auto-Scan Interval (detik)</div>
                            <input type="number" class="config-input" id="auto-scan" value="0" step="10" min="0" placeholder="0 = Manual">
                        </div>
                    </div>

                    <!-- Token Categories Configuration -->
                    <h3 style="color: #fecfef; margin: 30px 0 15px 0;">🎯 Kategori Token</h3>
                    <div class="token-categories" id="token-categories">
                        <div class="token-category">
                            <h4>💰 Stablecoins</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-stablecoins" checked>
                                <label for="cat-stablecoins">Enable (USDC, USDT, DAI, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>💎 Blue Chips</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-blue-chips" checked>
                                <label for="cat-blue-chips">Enable (WETH, WBTC, BNB, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>🔥 DeFi Tokens</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-defi" checked>
                                <label for="cat-defi">Enable (UNI, SUSHI, AAVE, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>⚡ Layer 1/2</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-layer1-2" checked>
                                <label for="cat-layer1-2">Enable (MATIC, ARB, OP, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>🐕 Meme Coins</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-meme-coins">
                                <label for="cat-meme-coins">Enable (DOGE, SHIB, PEPE, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>🎮 Gaming & NFT</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-gaming-nft">
                                <label for="cat-gaming-nft">Enable (AXS, SAND, MANA, dll)</label>
                            </div>
                        </div>
                        <div class="token-category">
                            <h4>☀️ Solana Ecosystem</h4>
                            <div class="checkbox-item">
                                <input type="checkbox" id="cat-solana-ecosystem" checked>
                                <label for="cat-solana-ecosystem">Enable (SOL, RAY, ORCA, BONK, dll)</label>
                            </div>
                        </div>
                    </div>

                    <!-- Trading Simulation Configuration -->
                    <div class="simulation-panel">
                        <h3 style="color: #fecfef; margin: 0 0 15px 0;">💼 Simulasi Modal Trading</h3>
                        <div class="simulation-grid">
                            <div class="config-item">
                                <div class="config-label">💵 Modal Simulasi ($)</div>
                                <input type="number" class="config-input" id="simulation-capital" value="100" step="10" min="10" max="10000">
                            </div>
                            <div class="config-item">
                                <div class="config-label">📉 Max Slippage (%)</div>
                                <input type="number" class="config-input" id="max-slippage" value="0.5" step="0.1" min="0.1" max="5">
                            </div>
                            <div class="config-item">
                                <div class="config-label">💰 Modal per Trade (%)</div>
                                <input type="number" class="config-input" id="capital-per-trade" value="100" step="10" min="10" max="100">
                            </div>
                            <div class="config-item">
                                <div class="config-label">🔒 Min Liquidity Ratio</div>
                                <input type="number" class="config-input" id="min-liquidity-ratio" value="10" step="1" min="1" max="100">
                            </div>
                        </div>

                        <!-- Simulation Summary -->
                        <div class="simulation-summary" id="simulation-summary">
                            <h4 style="margin: 0 0 10px 0; color: #a7f3d0;">📊 Ringkasan Simulasi</h4>
                            <div class="summary-item">
                                <span>Total Modal:</span>
                                <span class="summary-value" id="summary-capital">$100</span>
                            </div>
                            <div class="summary-item">
                                <span>Peluang Layak:</span>
                                <span class="summary-value" id="summary-opportunities">0</span>
                            </div>
                            <div class="summary-item">
                                <span>Profit Potensial:</span>
                                <span class="summary-value" id="summary-profit">$0</span>
                            </div>
                            <div class="summary-item">
                                <span>Rata-rata Slippage:</span>
                                <span class="summary-value" id="summary-slippage">0%</span>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 20px; text-align: center;">
                        <button class="btn" onclick="saveConfig()">💾 Simpan Konfigurasi</button>
                        <button class="btn" onclick="resetConfig()">🔄 Reset Default</button>
                        <button class="btn" onclick="updateSimulationSummary()">📊 Update Simulasi</button>
                    </div>
                </div>
            </div>

            <div class="grid">
                <div class="card">
                    <h2>🎯 Enhanced Control Center</h2>
                    <div class="button-group">
                        <button class="btn btn-scan" onclick="scanOpportunities()">🔍 Scan with Simulation</button>
                        <button class="btn" onclick="checkStatus()">📊 Status</button>
                        <button class="btn" onclick="updateSimulationSummary()">� Update Simulation</button>
                        <button class="btn" onclick="clearLogs()">🗑️ Hapus Log</button>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="opportunities-count">0</div>
                            <div class="stat-label">Peluang Layak</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="total-profit">$0</div>
                            <div class="stat-label">Profit Potensial</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="avg-slippage">0%</div>
                            <div class="stat-label">Avg Slippage</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="safety-score">-</div>
                            <div class="stat-label">Safety Score</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="scan-status">Siap</div>
                            <div class="stat-label">Status</div>
                        </div>
                    </div>

                    <!-- Performance Metrics Card -->
                    <div class="card" style="margin-top: 20px;">
                        <h2>⚡ Performance Monitoring</h2>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value" id="scan-speed">0</div>
                                <div class="stat-label">Tokens/Sec</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="api-response-time">0ms</div>
                                <div class="stat-label">Avg Response</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="cache-hit-rate">0%</div>
                                <div class="stat-label">Cache Hit Rate</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="success-rate">0%</div>
                                <div class="stat-label">Success Rate</div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div style="margin: 15px 0;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                <span style="font-size: 0.9rem; color: #a7f3d0;">Scan Progress</span>
                                <span style="font-size: 0.9rem; color: #a7f3d0;" id="progress-text">Ready</span>
                            </div>
                            <div style="background: rgba(255, 255, 255, 0.1); border-radius: 10px; height: 8px; overflow: hidden;">
                                <div id="progress-bar" style="background: linear-gradient(90deg, #10b981, #34d399); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2>� Live Scan Logs</h2>
                    <div class="logs-container" id="logs-container">
                        <div class="log-entry info">
                            <span class="log-time">Ready</span>
                            <span>🤖 Bot initialized and ready for scanning</span>
                        </div>
                    </div>
                </div>

                <div class="opportunities-container">
                    <h2>💰 Enhanced Arbitrage Opportunities</h2>
                    <div id="opportunities">
                        <div style="text-align: center; padding: 60px 20px; opacity: 0.7;">
                            <div style="font-size: 3rem; margin-bottom: 20px;">🔍</div>
                            <p style="font-size: 1.2rem; margin-bottom: 10px;">Ready for Enhanced Arbitrage Scanning</p>
                            <p style="opacity: 0.8;">Click "Scan with Simulation" to start analysis with token categories and trading simulation</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h2>🌐 Data Sources & Coverage</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">🦎</div>
                        <div class="stat-label">CoinGecko<br><small>Market Data</small></div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">📊</div>
                        <div class="stat-label">DexScreener<br><small>50+ DEXs</small></div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">🔶</div>
                        <div class="stat-label">Binance<br><small>CEX Data</small></div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">15+</div>
                        <div class="stat-label">Popular Tokens<br><small>Monitored</small></div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 20px; background: rgba(255, 255, 255, 0.05); border-radius: 15px;">
                    <h3 style="margin-bottom: 15px; color: #fecfef;">🔍 Scanning Strategy</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <strong>Multi-DEX Analysis:</strong> Compares prices across Uniswap, PancakeSwap, SushiSwap, QuickSwap, and more
                        </li>
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <strong>Cross-Chain Opportunities:</strong> Ethereum, BSC, Polygon, Arbitrum, Avalanche
                        </li>
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <strong>Liquidity Filtering:</strong> Only shows opportunities with sufficient liquidity (>$5k)
                        </li>
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <strong>Profit Range Filter:</strong> 0.05% - 200% to avoid false signals
                        </li>
                        <li style="padding: 8px 0;">
                            <strong>Real-time Updates:</strong> Continuous monitoring with live profit calculations
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Author Modal -->
        <div class="modal-overlay" id="author-modal">
            <div class="modal-content">
                <div class="modal-avatar">BC</div>
                <div class="modal-title">BOBACHEESE</div>
                <div class="modal-subtitle">Author</div>

                <div class="social-links">
                    <a href="https://github.com/bobacheese" target="_blank" class="social-link" title="GitHub">
                        🐙
                    </a>
                    <a href="https://youtube.com/@bobacheese?si=5M2leEilS3_VmNS6" target="_blank" class="social-link" title="YouTube">
                        📺
                    </a>
                    <a href="https://coff.ee/amarullohzd" target="_blank" class="social-link" title="Ko-fi">
                        ☕
                    </a>
                </div>

                <button class="close-modal" onclick="hideAuthorModal()">Tutup</button>
            </div>
        </div>

        <script>
            let scanInterval;
            let isScanning = false;

            // Function to generate DexScreener links
            function generateDexScreenerLink(chain, dex, pairAddress) {
                const baseUrl = "https://dexscreener.com";

                // Chain mapping for DexScreener URLs
                const chainMapping = {
                    "ethereum": "ethereum",
                    "bsc": "bsc",
                    "polygon": "polygon",
                    "arbitrum": "arbitrum",
                    "avalanche": "avalanche",
                    "solana": "solana",
                    "optimism": "optimism"
                };

                const mappedChain = chainMapping[chain?.toLowerCase()] || chain?.toLowerCase() || "ethereum";

                if (pairAddress) {
                    return `${baseUrl}/${mappedChain}/${pairAddress}`;
                } else {
                    // If no pair address, link to chain page
                    return `${baseUrl}/${mappedChain}`;
                }
            }

            async function scanOpportunities() {
                if (isScanning) return;

                isScanning = true;
                updateScanStatus('Memindai...');
                updateProgressBar(10, 'Initializing high-performance scan...');

                // Show enhanced scanning animation
                document.getElementById('opportunities').innerHTML = `
                    <div style="text-align: center; padding: 60px 20px;">
                        <div style="font-size: 3rem; margin-bottom: 20px; animation: pulse 1s infinite;">🔍</div>
                        <p style="font-size: 1.2rem; margin-bottom: 10px;">Enhanced scanning with simulation filtering...</p>
                        <p style="opacity: 0.8;">Analyzing token categories and calculating trading simulation</p>
                    </div>
                `;

                try {
                    updateProgressBar(30, 'Fetching market data...');
                    const response = await fetch('/api/scan');
                    updateProgressBar(70, 'Processing opportunities...');
                    const data = await response.json();
                    updateProgressBar(90, 'Finalizing results...');

                    // Update opportunities count
                    document.getElementById('opportunities-count').textContent = data.count || 0;

                    if (data.opportunities && data.opportunities.length > 0) {
                        let html = '';
                        data.opportunities.forEach((opp, index) => {
                            const profitClass = opp.profit_percentage > 0 ? 'profit-positive' : 'profit-negative';
                            const exchangeDisplay = (exchange) => {
                                const parts = exchange.split('_');
                                return parts.length > 1 ? `${parts[1]} (${parts[0]})` : exchange;
                            };

                            // Generate DexScreener links
                            const buyLink = generateDexScreenerLink(opp.buy_chain, opp.buy_dex_name, opp.buy_pair_address);
                            const sellLink = generateDexScreenerLink(opp.sell_chain, opp.sell_dex_name, opp.sell_pair_address);

                            const riskClass = opp.risk_level ? `risk-${opp.risk_level.toLowerCase()}` : 'risk-medium';
                            const isSolana = opp.buy_chain === 'solana';
                            const chainClass = isSolana ? 'chain-solana' : '';
                            const chainEmoji = isSolana ? '☀️' : '💎';

                            html += `
                                <div class="opportunity-enhanced ${chainClass}" style="animation-delay: ${index * 0.1}s;">
                                    <div class="opportunity-header">
                                        <h3>${chainEmoji} ${opp.token_symbol} ${isSolana ? 'Solana' : 'Enhanced'} Opportunity</h3>
                                        <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                                            ${isSolana ? '<span class="solana-badge">☀️ Solana</span>' : ''}
                                            <span class="risk-badge ${opp.risk_level ? opp.risk_level.toLowerCase() : 'medium'}">${opp.risk_level || 'Medium'} Risk</span>
                                            ${opp.validation_result ? `
                                                <span class="feasibility-score feasibility-${opp.validation_result.feasibility_score >= 80 ? 'high' : opp.validation_result.feasibility_score >= 60 ? 'medium' : 'low'}">
                                                    ${opp.validation_result.feasibility_score}/100
                                                </span>
                                                <span class="validation-badge ${opp.validation_result.is_valid ? 'validation-valid' : 'validation-error'}">
                                                    ${opp.validation_result.is_valid ? '✅ Validated' : '❌ Failed'}
                                                </span>
                                            ` : ''}
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0;">
                                        <div style="background: rgba(248, 113, 113, 0.1); padding: 15px; border-radius: 12px; border-left: 3px solid #f87171;">
                                            <p><strong>🛒 Buy:</strong> ${exchangeDisplay(opp.buy_exchange)}</p>
                                            <p style="font-size: 1.2rem; color: #fca5a5; font-weight: 600; margin: 8px 0;">$${opp.buy_price}</p>
                                            <a href="${buyLink}" target="_blank" class="dex-link">📊 DexScreener</a>
                                        </div>
                                        <div style="background: rgba(52, 211, 153, 0.1); padding: 15px; border-radius: 12px; border-left: 3px solid #34d399;">
                                            <p><strong>💰 Sell:</strong> ${exchangeDisplay(opp.sell_exchange)}</p>
                                            <p style="font-size: 1.2rem; color: #a7f3d0; font-weight: 600; margin: 8px 0;">$${opp.sell_price}</p>
                                            <a href="${sellLink}" target="_blank" class="dex-link">📊 DexScreener</a>
                                        </div>
                                    </div>

                                    <div class="simulation-metrics">
                                        <div class="metric-item">
                                            <div class="metric-value">$${opp.estimated_profit_usd || 0}</div>
                                            <div class="metric-label">Estimated Profit</div>
                                        </div>
                                        <div class="metric-item">
                                            <div class="metric-value">${opp.total_slippage || 0}%</div>
                                            <div class="metric-label">Total Slippage</div>
                                        </div>
                                        <div class="metric-item">
                                            <div class="metric-value">${opp.liquidity_ratio || 0}x</div>
                                            <div class="metric-label">Liquidity Ratio</div>
                                        </div>
                                        <div class="metric-item">
                                            <div class="metric-value">$${opp.recommended_order_size || 0}</div>
                                            <div class="metric-label">Recommended Size</div>
                                        </div>
                                    </div>

                                    <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 12px; margin: 10px 0;">
                                        <p><strong>📊 Profit:</strong> <span class="profit-positive">${opp.profit_percentage}%</span></p>
                                        <p><strong>💧 Min Liquidity:</strong> $${opp.min_liquidity?.toLocaleString()}</p>
                                        <p><strong>🔗 Blockchain:</strong> ${isSolana ? '<span class="solana-highlight">☀️ Solana</span>' : opp.buy_chain}</p>
                                        <p><strong>⏰ Detected:</strong> ${new Date(opp.timestamp).toLocaleString()}</p>

                                        ${opp.token_validation ? `
                                            <div style="margin-top: 10px; padding: 8px; background: rgba(34, 197, 94, 0.2); border-radius: 8px; border-left: 3px solid #22c55e;">
                                                <p style="margin: 0; font-size: 0.9rem; color: #a7f3d0;">
                                                    <strong>✅ Token Validated:</strong> Same contract addresses confirmed
                                                </p>
                                                ${opp.token_validation.price_ratio > 2 ? `
                                                    <p style="margin: 5px 0 0 0; font-size: 0.8rem; color: #fbbf24;">
                                                        ⚠️ Price ratio: ${opp.token_validation.price_ratio}x - Verify manually
                                                    </p>
                                                ` : ''}
                                            </div>
                                        ` : ''}

                                        ${opp.risk_flags && opp.risk_flags.includes('high_price_difference') ? `
                                            <div style="margin-top: 10px; padding: 8px; background: rgba(239, 68, 68, 0.2); border-radius: 8px; border-left: 3px solid #ef4444;">
                                                <p style="margin: 0; font-size: 0.9rem; color: #fca5a5;">
                                                    <strong>⚠️ HIGH RISK:</strong> Large price difference detected
                                                </p>
                                                <p style="margin: 5px 0 0 0; font-size: 0.8rem; color: #fca5a5;">
                                                    Please verify token contracts manually before trading
                                                </p>
                                            </div>
                                        ` : ''}

                                        ${opp.validation_result ? `
                                            <div class="validation-details">
                                                <p style="margin: 0 0 8px 0; font-weight: 600; color: #3b82f6;">
                                                    🔍 Enhanced Validation Results
                                                </p>
                                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; margin: 8px 0;">
                                                    <div style="text-align: center;">
                                                        <div style="font-weight: bold; color: #fecfef;">${opp.validation_result.liquidity_depth_score}/100</div>
                                                        <div style="font-size: 0.8rem;">Liquidity Depth</div>
                                                    </div>
                                                    <div style="text-align: center;">
                                                        <div style="font-weight: bold; color: #fecfef;">${opp.validation_result.confidence_score || 0}/100</div>
                                                        <div style="font-size: 0.8rem;">Confidence</div>
                                                    </div>
                                                    <div style="text-align: center;">
                                                        <div style="font-weight: bold; color: #fecfef;">${opp.validation_result.execution_time_estimate?.toFixed(1) || 0}s</div>
                                                        <div style="font-size: 0.8rem;">Est. Execution</div>
                                                    </div>
                                                </div>
                                                ${opp.validation_result.warnings && opp.validation_result.warnings.length > 0 ? `
                                                    <ul class="validation-warning-list">
                                                        ${opp.validation_result.warnings.slice(0, 3).map(warning => `<li>${warning}</li>`).join('')}
                                                    </ul>
                                                ` : ''}
                                            </div>
                                        ` : ''}

                                        ${opp.security_data && Object.keys(opp.security_data).length > 0 ? `
                                            <div class="security-details">
                                                <p style="margin: 0 0 8px 0; font-weight: 600; color: #8b5cf6;">
                                                    🛡️ Security Analysis
                                                </p>

                                                <div class="security-badges">
                                                    <span class="security-badge goplus-${opp.security_data.security_score >= 70 ? 'high' : opp.security_data.security_score >= 50 ? 'medium' : 'low'}">
                                                        GoPlus: ${opp.security_data.security_score}/100
                                                    </span>
                                                    ${opp.security_data.quickintel_data ? `
                                                        <span class="security-badge quickintel-${opp.security_data.quickintel_data.rating?.toLowerCase() || 'unknown'}">
                                                            QuickIntel: ${opp.security_data.quickintel_data.rating || 'Unknown'}
                                                        </span>
                                                    ` : ''}
                                                </div>

                                                ${opp.security_data.is_honeypot ? `
                                                    <div class="honeypot-warning">
                                                        🚨 HONEYPOT DETECTED - DO NOT TRADE! 🚨
                                                    </div>
                                                ` : ''}

                                                ${opp.security_data.security_flags && opp.security_data.security_flags.length > 0 ? `
                                                    <div style="margin: 8px 0;">
                                                        <p style="margin: 0 0 5px 0; font-size: 0.9rem; color: #fbbf24;">Security Flags:</p>
                                                        ${opp.security_data.security_flags.slice(0, 4).map(flag => `
                                                            <span class="security-flag ${flag}">${flag.replace(/_/g, ' ')}</span>
                                                        `).join('')}
                                                    </div>
                                                ` : ''}

                                                ${opp.security_data.owner_privileges && opp.security_data.owner_privileges.length > 0 ? `
                                                    <div style="margin: 8px 0;">
                                                        <p style="margin: 0 0 5px 0; font-size: 0.9rem; color: #f87171;">Owner Privileges:</p>
                                                        <p style="font-size: 0.8rem; color: #fca5a5;">
                                                            ${opp.security_data.owner_privileges.join(', ').replace(/_/g, ' ')}
                                                        </p>
                                                    </div>
                                                ` : ''}
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            `;
                        });
                        document.getElementById('opportunities').innerHTML = html;

                        // Update enhanced summary statistics with safety info
                        updateSummaryStats(data.opportunities, data.safety_summary);

                        // Update performance metrics
                        updatePerformanceMetrics();

                        updateProgressBar(100, 'Scan completed!');

                    } else {
                        document.getElementById('opportunities').innerHTML = `
                            <div style="text-align: center; padding: 60px 20px; opacity: 0.7;">
                                <div style="font-size: 3rem; margin-bottom: 20px;">😔</div>
                                <p style="font-size: 1.2rem; margin-bottom: 10px;">Tidak ada peluang menguntungkan ditemukan</p>
                                <p style="opacity: 0.8;">Kondisi pasar mungkin tidak menguntungkan untuk arbitrase saat ini</p>
                                <button class="btn" onclick="scanOpportunities()" style="margin-top: 20px;">🔄 Pindai Lagi</button>
                            </div>
                        `;
                    }

                    // Fetch and display logs
                    await updateLogs();

                } catch (error) {
                    document.getElementById('opportunities').innerHTML = `
                        <div style="text-align: center; padding: 60px 20px;">
                            <div style="font-size: 3rem; margin-bottom: 20px;">❌</div>
                            <p style="font-size: 1.2rem; margin-bottom: 10px;">Error Pemindaian</p>
                            <p style="opacity: 0.8;">${error.message}</p>
                            <button class="btn" onclick="scanOpportunities()" style="margin-top: 20px;">🔄 Coba Lagi</button>
                        </div>
                    `;
                    addLogEntry('error', `Pemindaian gagal: ${error.message}`);
                }

                isScanning = false;
                updateScanStatus('Siap');
            }

            async function updateLogs() {
                try {
                    const response = await fetch('/api/logs');
                    const data = await response.json();

                    if (data.logs && data.logs.length > 0) {
                        const logsContainer = document.getElementById('logs-container');
                        let html = '';

                        data.logs.slice(-20).forEach(log => {
                            const time = new Date(log.timestamp).toLocaleTimeString();
                            html += `
                                <div class="log-entry ${log.level}">
                                    <span class="log-time">${time}</span>
                                    <span>${log.message}</span>
                                </div>
                            `;
                        });

                        logsContainer.innerHTML = html;
                        logsContainer.scrollTop = logsContainer.scrollHeight;
                    }
                } catch (error) {
                    console.error('Failed to update logs:', error);
                }
            }

            function addLogEntry(level, message) {
                const logsContainer = document.getElementById('logs-container');
                const time = new Date().toLocaleTimeString();

                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${level}`;
                logEntry.innerHTML = `
                    <span class="log-time">${time}</span>
                    <span>${message}</span>
                `;

                logsContainer.appendChild(logEntry);
                logsContainer.scrollTop = logsContainer.scrollHeight;
            }

            function updateScanStatus(status) {
                document.getElementById('scan-status').textContent = status;
                const indicator = document.querySelector('.status-indicator');

                if (status === 'Memindai...') {
                    indicator.className = 'status-indicator status-scanning';
                } else {
                    indicator.className = 'status-indicator status-online';
                }
            }

            // Configuration Panel Functions
            function toggleConfig() {
                const content = document.getElementById('config-content');
                const toggle = document.getElementById('config-toggle');

                if (content.classList.contains('expanded')) {
                    content.classList.remove('expanded');
                    toggle.textContent = '▼';
                } else {
                    content.classList.add('expanded');
                    toggle.textContent = '▲';
                }
            }

            function saveConfig() {
                const config = {
                    profit_min: parseFloat(document.getElementById('profit-min').value),
                    profit_max: parseFloat(document.getElementById('profit-max').value),
                    min_liquidity: parseFloat(document.getElementById('min-liquidity').value),
                    min_volume_24h: parseFloat(document.getElementById('min-volume').value),
                    enabled_chains: getEnabledChains(),
                    auto_scan_interval: parseInt(document.getElementById('auto-scan').value),
                    // Enhanced configuration
                    enabled_token_categories: getEnabledTokenCategories(),
                    simulation_capital: parseFloat(document.getElementById('simulation-capital').value),
                    max_slippage: parseFloat(document.getElementById('max-slippage').value),
                    capital_per_trade: parseFloat(document.getElementById('capital-per-trade').value),
                    min_liquidity_ratio: parseFloat(document.getElementById('min-liquidity-ratio').value)
                };

                // Validate configuration
                if (config.profit_min >= config.profit_max) {
                    alert('❌ Profit minimum harus lebih kecil dari profit maksimal');
                    return;
                }

                if (config.profit_max > 100) {
                    alert('❌ Profit maksimal tidak boleh lebih dari 100%');
                    return;
                }

                if (config.enabled_chains.length === 0) {
                    alert('❌ Pilih minimal satu blockchain');
                    return;
                }

                // Save to localStorage
                localStorage.setItem('arbitrage_config', JSON.stringify(config));

                // Send to backend
                fetch('/api/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(config)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        addLogEntry('success', '✅ Konfigurasi berhasil disimpan');
                        alert('✅ Konfigurasi berhasil disimpan!');
                    } else {
                        addLogEntry('error', '❌ Gagal menyimpan konfigurasi');
                        alert('❌ Gagal menyimpan konfigurasi');
                    }
                })
                .catch(error => {
                    addLogEntry('error', `❌ Error: ${error.message}`);
                    alert('❌ Error menyimpan konfigurasi');
                });
            }

            function resetConfig() {
                document.getElementById('profit-min').value = 0.05;
                document.getElementById('profit-max').value = 25;
                document.getElementById('min-liquidity').value = 5000;
                document.getElementById('min-volume').value = 500;
                document.getElementById('auto-scan').value = 0;

                // Reset checkboxes
                document.getElementById('chain-ethereum').checked = true;
                document.getElementById('chain-bsc').checked = true;
                document.getElementById('chain-polygon').checked = true;
                document.getElementById('chain-arbitrum').checked = true;

                // Clear localStorage
                localStorage.removeItem('arbitrage_config');

                addLogEntry('info', '🔄 Konfigurasi direset ke default');
                alert('🔄 Konfigurasi direset ke default');
            }

            function getEnabledChains() {
                const chains = [];
                if (document.getElementById('chain-ethereum').checked) chains.push('ethereum');
                if (document.getElementById('chain-bsc').checked) chains.push('bsc');
                if (document.getElementById('chain-polygon').checked) chains.push('polygon');
                if (document.getElementById('chain-arbitrum').checked) chains.push('arbitrum');
                if (document.getElementById('chain-solana').checked) chains.push('solana');
                return chains;
            }

            function loadConfig() {
                const saved = localStorage.getItem('arbitrage_config');
                if (saved) {
                    const config = JSON.parse(saved);

                    document.getElementById('profit-min').value = config.profit_min || 0.05;
                    document.getElementById('profit-max').value = config.profit_max || 25;
                    document.getElementById('min-liquidity').value = config.min_liquidity || 5000;
                    document.getElementById('min-volume').value = config.min_volume_24h || 500;
                    document.getElementById('auto-scan').value = config.auto_scan_interval || 0;

                    // Set blockchain checkboxes
                    document.getElementById('chain-ethereum').checked = config.enabled_chains.includes('ethereum');
                    document.getElementById('chain-bsc').checked = config.enabled_chains.includes('bsc');
                    document.getElementById('chain-polygon').checked = config.enabled_chains.includes('polygon');
                    document.getElementById('chain-arbitrum').checked = config.enabled_chains.includes('arbitrum');
                    document.getElementById('chain-solana').checked = config.enabled_chains.includes('solana');

                    // Set enhanced configuration
                    if (config.enabled_token_categories) {
                        document.getElementById('cat-stablecoins').checked = config.enabled_token_categories.includes('stablecoins');
                        document.getElementById('cat-blue-chips').checked = config.enabled_token_categories.includes('blue_chips');
                        document.getElementById('cat-defi').checked = config.enabled_token_categories.includes('defi');
                        document.getElementById('cat-layer1-2').checked = config.enabled_token_categories.includes('layer1_2');
                        document.getElementById('cat-meme-coins').checked = config.enabled_token_categories.includes('meme_coins');
                        document.getElementById('cat-gaming-nft').checked = config.enabled_token_categories.includes('gaming_nft');
                        document.getElementById('cat-solana-ecosystem').checked = config.enabled_token_categories.includes('solana_ecosystem');
                    }

                    // Set simulation configuration
                    document.getElementById('simulation-capital').value = config.simulation_capital || 100;
                    document.getElementById('max-slippage').value = config.max_slippage || 0.5;
                    document.getElementById('capital-per-trade').value = config.capital_per_trade || 100;
                    document.getElementById('min-liquidity-ratio').value = config.min_liquidity_ratio || 10;

                    // Update summary display
                    document.getElementById('summary-capital').textContent = `$${config.simulation_capital || 100}`;
                }
            }

            // Enhanced Functions
            function updateSummaryStats(opportunities, safetySummary = null) {
                const totalProfit = opportunities.reduce((sum, opp) => sum + (opp.estimated_profit_usd || 0), 0);
                const avgSlippage = opportunities.length > 0 ?
                    opportunities.reduce((sum, opp) => sum + (opp.total_slippage || 0), 0) / opportunities.length : 0;

                document.getElementById('total-profit').textContent = `$${totalProfit.toFixed(2)}`;
                document.getElementById('avg-slippage').textContent = `${avgSlippage.toFixed(2)}%`;

                // Update safety score
                if (safetySummary) {
                    const safetyElement = document.getElementById('safety-score');
                    const safetyScore = safetySummary.safety_score;
                    safetyElement.textContent = safetyScore;

                    // Color code safety score
                    safetyElement.style.color =
                        safetyScore === 'High' ? '#10b981' :
                        safetyScore === 'Medium' ? '#f59e0b' : '#ef4444';

                    // Show validation rate in tooltip or subtitle
                    const validationRate = safetySummary.validation_rate;
                    safetyElement.title = `${safetySummary.validated_opportunities}/${opportunities.length} validated (${validationRate}%)`;

                    // Log safety information
                    if (safetySummary.high_risk_opportunities > 0) {
                        addLogEntry('warning', `⚠️ ${safetySummary.high_risk_opportunities} opportunities flagged as HIGH RISK`);
                    }
                } else {
                    document.getElementById('safety-score').textContent = '-';
                }
            }

            // Performance Monitoring Functions
            async function updatePerformanceMetrics() {
                try {
                    const response = await fetch('/api/performance');
                    const data = await response.json();

                    if (data.status === 'success') {
                        const metrics = data.metrics;

                        document.getElementById('scan-speed').textContent = metrics.tokens_per_second.toFixed(1);
                        document.getElementById('api-response-time').textContent = `${metrics.avg_response_time}ms`;
                        document.getElementById('cache-hit-rate').textContent = `${metrics.cache_hit_rate}%`;
                        document.getElementById('success-rate').textContent = `${metrics.success_rate}%`;

                        // Color code performance metrics
                        const speedElement = document.getElementById('scan-speed');
                        speedElement.style.color = metrics.tokens_per_second > 10 ? '#10b981' :
                                                  metrics.tokens_per_second > 5 ? '#f59e0b' : '#ef4444';

                        const cacheElement = document.getElementById('cache-hit-rate');
                        cacheElement.style.color = metrics.cache_hit_rate > 70 ? '#10b981' :
                                                  metrics.cache_hit_rate > 40 ? '#f59e0b' : '#ef4444';

                        const successElement = document.getElementById('success-rate');
                        successElement.style.color = metrics.success_rate > 80 ? '#10b981' :
                                                    metrics.success_rate > 60 ? '#f59e0b' : '#ef4444';
                    }
                } catch (error) {
                    console.error('Performance metrics update error:', error);
                }
            }

            function updateProgressBar(progress, text) {
                const progressBar = document.getElementById('progress-bar');
                const progressText = document.getElementById('progress-text');

                if (progressBar && progressText) {
                    progressBar.style.width = `${progress}%`;
                    progressText.textContent = text;
                }
            }

            // Auto-update performance metrics every 5 seconds
            setInterval(updatePerformanceMetrics, 5000);

            async function updateSimulationSummary() {
                try {
                    const response = await fetch('/api/simulation-summary');
                    const data = await response.json();

                    if (data.summary) {
                        const summary = data.summary;
                        document.getElementById('summary-capital').textContent = `$${summary.total_capital}`;
                        document.getElementById('summary-opportunities').textContent = summary.feasible_opportunities;
                        document.getElementById('summary-profit').textContent = `$${summary.total_potential_profit}`;
                        document.getElementById('summary-slippage').textContent = `${summary.average_slippage}%`;

                        alert(`💼 Simulation Summary:\n` +
                              `💰 Total Capital: $${summary.total_capital}\n` +
                              `🎯 Feasible Opportunities: ${summary.feasible_opportunities}\n` +
                              `📈 Total Potential Profit: $${summary.total_potential_profit}\n` +
                              `📉 Average Slippage: ${summary.average_slippage}%`);
                    }
                } catch (error) {
                    alert('❌ Error fetching simulation summary');
                }
            }

            async function updateSimulationConfig() {
                const capital = parseFloat(document.getElementById('simulation-capital').value);
                const maxSlippage = parseFloat(document.getElementById('max-slippage').value);
                const capitalPerTrade = parseFloat(document.getElementById('capital-per-trade').value);
                const minLiquidityRatio = parseFloat(document.getElementById('min-liquidity-ratio').value);

                try {
                    const response = await fetch('/api/config', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            simulation_capital: capital,
                            max_slippage: maxSlippage,
                            capital_per_trade: capitalPerTrade,
                            min_liquidity_ratio: minLiquidityRatio
                        })
                    });

                    if (response.ok) {
                        document.getElementById('summary-capital').textContent = `$${capital}`;
                        addLogEntry('info', '✅ Simulation configuration updated!');
                    }
                } catch (error) {
                    addLogEntry('error', '❌ Error updating simulation configuration');
                }
            }

            function getEnabledTokenCategories() {
                const categories = [];
                if (document.getElementById('cat-stablecoins').checked) categories.push('stablecoins');
                if (document.getElementById('cat-blue-chips').checked) categories.push('blue_chips');
                if (document.getElementById('cat-defi').checked) categories.push('defi');
                if (document.getElementById('cat-layer1-2').checked) categories.push('layer1_2');
                if (document.getElementById('cat-meme-coins').checked) categories.push('meme_coins');
                if (document.getElementById('cat-gaming-nft').checked) categories.push('gaming_nft');
                if (document.getElementById('cat-solana-ecosystem').checked) categories.push('solana_ecosystem');
                return categories;
            }

            // Author Modal Functions
            function showAuthorModal() {
                document.getElementById('author-modal').style.display = 'flex';
            }

            function hideAuthorModal() {
                document.getElementById('author-modal').style.display = 'none';
            }

            // Close modal when clicking outside
            document.addEventListener('click', function(event) {
                const modal = document.getElementById('author-modal');
                if (event.target === modal) {
                    hideAuthorModal();
                }
            });

            async function checkStatus() {
                try {
                    const response = await fetch('/api/status');
                    const data = await response.json();

                    const statusInfo = `
                        ✅ Status: ${data.status}
                        📊 Versi: ${data.version}
                        🔄 Mode: ${data.mode}
                        💰 Peluang: ${data.opportunities_count}
                        🕒 Scan Terakhir: ${data.last_scan ? new Date(data.last_scan).toLocaleString() : 'Belum pernah'}
                        ⏰ Waktu Sekarang: ${new Date().toLocaleString()}
                    `;

                    alert(statusInfo);
                } catch (error) {
                    alert('❌ Error: ' + error.message);
                }
            }

            function clearLogs() {
                document.getElementById('logs-container').innerHTML = `
                    <div class="log-entry info">
                        <span class="log-time">Sekarang</span>
                        <span>🗑️ Log dibersihkan</span>
                    </div>
                `;
            }

            function viewDocs() {
                window.open('/docs', '_blank');
            }

            // Auto-update logs every 5 seconds
            setInterval(updateLogs, 5000);

            // Load saved configuration on page load
            document.addEventListener('DOMContentLoaded', function() {
                loadConfig();
            });

            // Initial log
            addLogEntry('info', '🚀 UI dimuat dan siap untuk pemindaian');
        </script>
    </body>
    </html>
    """, status_code=200)

@app.get("/api/status")
async def get_status():
    """Get application status"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "mode": "public_apis_only",
        "timestamp": datetime.now().isoformat(),
        "last_scan": detector.last_scan,
        "opportunities_count": len(detector.opportunities)
    }

@app.get("/api/scan")
async def scan_opportunities():
    """Scan for arbitrage opportunities with enhanced safety validation"""
    try:
        opportunities = await detector.scan_opportunities()
        safety_summary = detector.create_safe_opportunity_summary(opportunities)

        return {
            "status": "success",
            "opportunities": opportunities,
            "count": len(opportunities),
            "timestamp": datetime.now().isoformat(),
            "safety_summary": safety_summary
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/opportunities")
async def get_opportunities():
    """Get current opportunities"""
    return {
        "status": "success",
        "opportunities": detector.opportunities,
        "count": len(detector.opportunities),
        "last_scan": detector.last_scan
    }

@app.get("/api/performance")
async def get_performance_metrics():
    """Get real-time performance metrics"""
    try:
        metrics = detector.scan_metrics

        return {
            "status": "success",
            "metrics": {
                "total_tokens": metrics.total_tokens,
                "tokens_per_second": round(metrics.tokens_per_second, 2),
                "avg_response_time": round(sum(metrics.api_response_times) / len(metrics.api_response_times) * 1000, 1) if metrics.api_response_times else 0,
                "max_response_time": round(max(metrics.api_response_times) * 1000, 1) if metrics.api_response_times else 0,
                "cache_hit_rate": round(detector.smart_cache.get_hit_rate(), 1),
                "success_rate": round(metrics.success_rate, 1),
                "scan_duration": round(metrics.scan_duration, 2),
                "errors_count": metrics.errors_count,
                "cache_size": len(detector.smart_cache.cache)
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "error",
            "metrics": {
                "total_tokens": 0,
                "tokens_per_second": 0,
                "avg_response_time": 0,
                "max_response_time": 0,
                "cache_hit_rate": 0,
                "success_rate": 0,
                "scan_duration": 0,
                "errors_count": 0,
                "cache_size": 0
            },
            "error": str(e)
        }

@app.get("/api/logs")
async def get_logs():
    """Get scan logs"""
    return {
        "status": "success",
        "logs": detector.scan_logs,
        "count": len(detector.scan_logs)
    }


@app.get("/api/dexscreener-link")
async def get_dexscreener_link(chain: str, dex: str, pair_address: str = None):
    """Get DexScreener link for a specific DEX pair"""
    link = detector.get_dexscreener_link(chain, dex, pair_address)
    return {
        "status": "success",
        "link": link,
        "chain": chain,
        "dex": dex
    }


@app.get("/api/config")
async def get_config():
    """Get current configuration"""
    return {
        "status": "success",
        "config": detector.config
    }


@app.post("/api/config")
async def update_config(config_data: dict):
    """Update scanning configuration"""
    try:
        # Validate configuration
        valid_keys = [
            "profit_min", "profit_max", "min_liquidity", "min_volume_24h",
            "enabled_chains", "auto_scan_interval", "enabled_token_categories",
            "max_tokens_per_category", "simulation_capital", "max_slippage",
            "capital_per_trade", "min_liquidity_ratio", "risk_tolerance"
        ]
        filtered_config = {k: v for k, v in config_data.items() if k in valid_keys}

        # Update configuration
        detector.update_config(filtered_config)

        return {
            "status": "success",
            "message": "Konfigurasi berhasil diperbarui",
            "config": detector.config
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error updating config: {str(e)}")


@app.get("/api/token-categories")
async def get_token_categories():
    """Get available token categories"""
    return {
        "status": "success",
        "categories": detector.token_categories,
        "chains": detector.supported_chains
    }


@app.get("/api/simulation-summary")
async def get_simulation_summary():
    """Get simulation summary statistics"""
    try:
        opportunities = detector.opportunities
        config = detector.config

        if not opportunities:
            return {
                "status": "success",
                "summary": {
                    "total_capital": config["simulation_capital"],
                    "feasible_opportunities": 0,
                    "total_potential_profit": 0,
                    "average_slippage": 0,
                    "risk_distribution": {"Low": 0, "Medium": 0, "High": 0}
                }
            }

        # Calculate summary statistics
        total_capital = config["simulation_capital"]
        feasible_count = len(opportunities)
        total_potential_profit = sum(opp.get("estimated_profit_usd", 0) for opp in opportunities)
        average_slippage = sum(opp.get("total_slippage", 0) for opp in opportunities) / feasible_count if feasible_count > 0 else 0

        # Risk distribution
        risk_distribution = {"Low": 0, "Medium": 0, "High": 0}
        for opp in opportunities:
            risk_level = opp.get("risk_level", "High")
            risk_distribution[risk_level] += 1

        return {
            "status": "success",
            "summary": {
                "total_capital": total_capital,
                "feasible_opportunities": feasible_count,
                "total_potential_profit": round(total_potential_profit, 2),
                "average_slippage": round(average_slippage, 3),
                "risk_distribution": risk_distribution,
                "capital_utilization": round((total_potential_profit / total_capital) * 100, 2) if total_capital > 0 else 0
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating summary: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            
            if data == "ping":
                await websocket.send_text("pong")
            elif data.startswith("scan"):
                opportunities = await detector.scan_opportunities()
                await websocket.send_text(json.dumps({
                    "type": "opportunities",
                    "data": opportunities
                }))
                
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)

if __name__ == "__main__":
    print("🚀 Starting Enhanced Crypto Arbitrage Bot v2.0")
    print("📊 Features: Token Categories, Trading Simulation, Solana Support, Enhanced Filtering")
    print("🌐 Web interface: http://localhost:8000")
    print("📚 API docs: http://localhost:8000/docs")
    print("=" * 60)
    
    uvicorn.run(
        "main_fixed:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
