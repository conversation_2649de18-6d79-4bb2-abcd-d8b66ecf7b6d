# app.py
# Untuk menjalankan aplikasi ini:
# 1. Pastikan Anda memiliki Python dan Flask terinstal.
#    pip install Flask requests
# 2. Simpan kode ini sebagai file bernama `app.py`.
# 3. Jalankan aplikasi dari terminal: python app.py
# 4. Buka browser Anda dan kunjungi http://127.0.0.1:5000

from flask import Flask, render_template_string, jsonify, request
import requests
import threading
import time
from datetime import datetime, timedelta, timezone
from collections import deque
import logging

# --- Konfigurasi Aplikasi Flask ---
app = Flask(__name__)
# Menonaktifkan log standar Flask untuk tampilan konsol yang lebih bersih
log = logging.getLogger('werkzeug')
log.setLevel(logging.ERROR)


# --- State Management Sederhana ---
# Variabel global untuk menyimpan status dan hasil dari bot
app_state = {
    'status': 'Idle',
    'is_running': False,
    'opportunities': [],
    'scan_count': 0,
    'last_scan_time': None,
    'logs': deque(maxlen=200),
    'simulation_capital': 100.0,
    'max_profit_threshold': 25.0
}

# --- Logika Inti Bot Arbitrase ---

DEXSCREENER_API_BASE_URL = "https://api.dexscreener.com/latest/dex"

def add_log(message):
    """Fungsi helper untuk menambahkan pesan log ke state aplikasi."""
    timestamp = datetime.now().strftime('%H:%M:%S')
    log_entry = f"[{timestamp}] {message}"
    app_state['logs'].appendleft(log_entry)
    print(log_entry) # Menambahkan log ke konsol server juga

def find_potential_tokens():
    """
    Tahap 1: Menemukan token yang berpotensi menggunakan logika multi-strategi yang kompleks dan luas.
    """
    add_log("Memulai siklus baru: Mencari pasangan yang berpotensi...")
    app_state['status'] = 'Mencari pasangan yang berpotensi...'
    
    # Kueri pencarian yang luas untuk mendapatkan sampel pasar yang besar
    search_queries = ['weth', 'sol', 'usdc', 'usdt', 'bnb', 'avax', 'matic']
    
    potential_token_addresses = set()
    
    add_log("[Strategi Terpadu] Memindai pasar untuk aktivitas...")
    
    for query in search_queries:
        if not app_state['is_running']: break
        add_log(f"Menganalisis pasar melalui kueri '{query}'...")
        try:
            search_url = f"{DEXSCREENER_API_BASE_URL}/search?q={query}"
            response = requests.get(search_url, timeout=15)
            response.raise_for_status()
            pairs = response.json().get('pairs', [])
            if not pairs: continue

            fifteen_minutes_ago = datetime.now(timezone.utc) - timedelta(minutes=15)

            for pair in pairs:
                # Ekstrak data yang relevan dengan aman
                volume_h24 = pair.get('volume', {}).get('h24', 0)
                price_change_h24 = abs(pair.get('priceChange', {}).get('h24', 0))
                txns_h1 = pair.get('txns', {}).get('h1', {}).get('buys', 0) + pair.get('txns', {}).get('h1', {}).get('sells', 0)
                
                is_new = False
                pair_created_at_val = pair.get('pairCreatedAt')
                if pair_created_at_val:
                    try:
                        if isinstance(pair_created_at_val, (int, float)):
                            if pair_created_at_val > 10**12: pair_created_at_val /= 1000
                            pair_created_at = datetime.fromtimestamp(pair_created_at_val, tz=timezone.utc)
                        elif isinstance(pair_created_at_val, str):
                            if not pair_created_at_val.endswith('Z'): pair_created_at_val += 'Z'
                            pair_created_at = datetime.fromisoformat(pair_created_at_val.replace('Z', '+00:00'))
                        
                        if pair_created_at and pair_created_at > fifteen_minutes_ago:
                            is_new = True
                    except (ValueError, TypeError, OSError):
                        pass # Abaikan jika timestamp tidak valid

                # Tambahkan token jika memenuhi SALAH SATU kriteria
                if is_new or volume_h24 > 75000 or price_change_h24 > 20 or txns_h1 > 200:
                    if 'baseToken' in pair and 'address' in pair['baseToken']:
                        potential_token_addresses.add(pair['baseToken']['address'])
            
            time.sleep(1) # Jeda antar kueri untuk menghormati API
        except requests.RequestException as e:
            add_log(f"Gagal mengambil data untuk kueri '{query}': {e}")
            continue
            
    if not potential_token_addresses:
        add_log("Pencarian selesai. Tidak ada token yang memenuhi kriteria aktivitas.")
        return []

    add_log(f"Pencarian selesai. Ditemukan {len(potential_token_addresses)} token unik untuk dianalisis.")
    return list(potential_token_addresses)


def get_token_prices(token_address):
    app_state['status'] = f"Mengambil data harga untuk token: {token_address[:10]}..."
    try:
        pairs_url = f"{DEXSCREENER_API_BASE_URL}/tokens/{token_address}"
        response = requests.get(pairs_url, timeout=10)
        response.raise_for_status()
        return response.json().get('pairs', [])
    except requests.RequestException as e:
        error_msg = f"Error saat mengambil harga: {e}"
        app_state['status'] = error_msg
        add_log(error_msg)
        return []

def analyze_opportunities(pairs):
    if not pairs:
        return

    base_token_symbol = pairs[0].get('baseToken', {}).get('symbol', 'N/A')
    add_log(f"Menganalisis {len(pairs)} pasangan untuk token {base_token_symbol}...")
    
    grouped_by_quote_token = {}
    for pair in pairs:
        if pair.get('priceUsd') and pair.get('liquidity', {}).get('usd', 0) > 500:
            quote_token_address = pair['quoteToken']['address']
            if quote_token_address not in grouped_by_quote_token:
                grouped_by_quote_token[quote_token_address] = []
            grouped_by_quote_token[quote_token_address].append(pair)

    if not grouped_by_quote_token:
        add_log(f"Tidak ada pasangan {base_token_symbol} yang memenuhi syarat likuiditas > $500.")
        return

    for quote_token_symbol, pair_group in grouped_by_quote_token.items():
        best_price_per_dex = {}
        for pair in pair_group:
            dex_id = pair.get('dexId')
            if not dex_id:
                continue
            price = float(pair['priceUsd'])
            if dex_id not in best_price_per_dex or price < float(best_price_per_dex[dex_id]['priceUsd']):
                best_price_per_dex[dex_id] = pair
        
        unique_dex_pairs = list(best_price_per_dex.values())
        
        if len(unique_dex_pairs) < 2:
            add_log(f"Hanya ditemukan 1 DEX unik untuk {base_token_symbol}/{unique_dex_pairs[0]['quoteToken']['symbol']}. Analisis dilewati.")
            continue 

        sorted_pairs = sorted(unique_dex_pairs, key=lambda p: float(p['priceUsd']))
        
        buy_dex = sorted_pairs[0]
        sell_dex = sorted_pairs[-1]

        if not buy_dex.get('dexId') or not sell_dex.get('dexId'):
            add_log(f"Peluang dilewati: Data DEX tidak lengkap untuk pasangan {base_token_symbol}.")
            continue

        buy_price = float(buy_dex['priceUsd'])
        sell_price = float(sell_dex['priceUsd'])

        log_msg = (f"Membandingkan {base_token_symbol}/{buy_dex['quoteToken']['symbol']}: "
                   f"Beli di {buy_dex['dexId']} @ ${buy_price:.6f}, "
                   f"Jual di {sell_dex['dexId']} @ ${sell_price:.6f}")
        add_log(log_msg)

        if buy_price > 0 and buy_dex['dexId'] != sell_dex['dexId']:
            profit_percentage = ((sell_price - buy_price) / buy_price) * 100

            max_profit = app_state.get('max_profit_threshold', 25.0)
            if profit_percentage > 0.25 and profit_percentage < max_profit:
                capital = app_state.get('simulation_capital', 100.0)
                buy_liquidity = buy_dex.get('liquidity', {}).get('usd', 0)
                sell_liquidity = sell_dex.get('liquidity', {}).get('usd', 0)
                
                slippage_threshold = 0.015 
                
                if capital < (buy_liquidity * slippage_threshold) and capital < (sell_liquidity * slippage_threshold):
                    opportunity = {
                        'baseToken': buy_dex['baseToken']['symbol'],
                        'quoteToken': buy_dex['quoteToken']['symbol'],
                        'chainId': buy_dex['chainId'],
                        'buy_dex_name': buy_dex['dexId'].capitalize(),
                        'buy_price': f"{buy_price:.6f}",
                        'sell_price': f"{sell_price:.6f}",
                        'profit_percentage': f"{profit_percentage:.2f}",
                        'buy_url': buy_dex['url'],
                        'sell_url': sell_dex['url'],
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    }
                    
                    is_duplicate = any(op['buy_url'] == opportunity['buy_url'] and op['sell_url'] == opportunity['sell_url'] for op in app_state['opportunities'])
                    if not is_duplicate:
                        add_log(f"✅ PELUANG DITEMUKAN: Profit {profit_percentage:.2f}% untuk {opportunity['baseToken']}/{opportunity['quoteToken']}")
                        app_state['opportunities'].append(opportunity)
                        app_state['opportunities'].sort(key=lambda x: float(x['profit_percentage']), reverse=True)
                else:
                    add_log(f"Peluang dilewati: Modal ${capital:,.0f} terlalu besar untuk likuiditas (${buy_liquidity:,.0f} & ${sell_liquidity:,.0f}).")
            elif profit_percentage >= max_profit:
                add_log(f"Peluang dilewati: Profit {profit_percentage:.2f}% melebihi ambang batas maksimal {max_profit}%.")


def scanner_worker():
    add_log("Memulai scanner_worker thread.")
    while app_state.get('is_running', False):
        app_state['scan_count'] += 1
        app_state['last_scan_time'] = datetime.now().strftime('%H:%M:%S')

        potential_tokens = find_potential_tokens()
        
        if not potential_tokens:
            app_state['status'] = "Tidak ada token berpotensi ditemukan. Menunggu..."
        else:
            app_state['status'] = f"Ditemukan {len(potential_tokens)} token. Menganalisis..."
            
            for token_addr in potential_tokens:
                if not app_state['is_running']: 
                    add_log("Pemindaian dihentikan di tengah analisis token.")
                    break
                pairs = get_token_prices(token_addr)
                if pairs:
                    analyze_opportunities(pairs)

        app_state['status'] = "Menunggu siklus pindai berikutnya..."
        
        for i in range(60):
             if not app_state['is_running']:
                 add_log("Pemindaian dihentikan saat jeda.")
                 break
             time.sleep(1)

    app_state['status'] = 'Idle'
    add_log("Pemindaian dihentikan oleh pengguna.")


# --- Rute Aplikasi Flask ---

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/toggle-scan', methods=['POST'])
def toggle_scan():
    print("SERVER: /toggle-scan endpoint diakses.") # Log di konsol server
    if not app_state['is_running']:
        app_state['is_running'] = True
        app_state['opportunities'] = []
        app_state['scan_count'] = 0
        app_state['logs'].clear()
        add_log("Bot arbitrase dimulai.")
        thread = threading.Thread(target=scanner_worker)
        thread.daemon = True
        thread.start()
        print("SERVER: Thread scanner_worker telah dimulai.")
        return jsonify({'status': 'scan_started'})
    else:
        app_state['is_running'] = False
        print("SERVER: Status is_running diubah menjadi False.")
        return jsonify({'status': 'scan_stopped'})

@app.route('/update-settings', methods=['POST'])
def update_settings():
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'error': 'Invalid data'}), 400

    updated = False
    if 'capital' in data:
        try:
            capital = float(data['capital'])
            if capital > 0:
                app_state['simulation_capital'] = capital
                add_log(f"Pengaturan diperbarui: Modal simulasi diatur ke ${capital:,.2f}")
                updated = True
        except (ValueError, TypeError): pass
    
    if 'max_profit' in data:
        try:
            max_profit = float(data['max_profit'])
            if max_profit > 0:
                app_state['max_profit_threshold'] = max_profit
                add_log(f"Pengaturan diperbarui: Maksimal profit diatur ke {max_profit:.2f}%")
                updated = True
        except (ValueError, TypeError): pass

    if updated: return jsonify({'success': True})
    else: return jsonify({'success': False, 'error': 'Invalid value'}), 400


@app.route('/get-status')
def get_status():
    status_data = app_state.copy()
    status_data['logs'] = list(app_state['logs'])
    return jsonify(status_data)

# --- Template HTML ---
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bot Arbitrase Dex Screener</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Fira+Code&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #111827; /* bg-gray-900 */
            color: #e5e7eb; /* text-gray-200 */
        }
        .glass-card {
            background: rgba(31, 41, 55, 0.6); /* bg-gray-800 with opacity */
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .glass-card:hover {
            border: 1px solid rgba(255, 255, 255, 0.2);
            transform: translateY(-4px);
        }
        .btn-glow { box-shadow: 0 0 8px #3b82f6, 0 0 16px #3b82f6; }
        .btn-glow-stop { box-shadow: 0 0 8px #ef4444, 0 0 16px #ef4444; }
        .log-panel { font-family: 'Fira Code', monospace; }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .fade-in { animation: fadeIn 0.5s ease-out forwards; }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        .animate-pulse-green { animation: pulse 1.5s infinite; }
        
        ::-webkit-scrollbar { width: 6px; }
        ::-webkit-scrollbar-track { background: #1f2937; }
        ::-webkit-scrollbar-thumb { background: #4b5563; border-radius: 3px; }
        ::-webkit-scrollbar-thumb:hover { background: #6b7280; }
    </style>
</head>
<body class="p-4 md:p-8">
    <div class="container mx-auto max-w-7xl">
        
        <header class="text-center mb-10 fade-in">
            <h1 class="text-5xl md:text-6xl font-extrabold text-white">Arbitrage Scanner</h1>
            <p class="text-lg text-gray-400 mt-2">Mendeteksi peluang pasar secara real-time dengan presisi.</p>
        </header>

        <div class="glass-card rounded-xl p-6 mb-8 fade-in" style="animation-delay: 100ms;">
            <div class="flex flex-col lg:flex-row justify-between items-center gap-6">
                <div class="flex items-center gap-4 w-full lg:w-auto">
                    <div id="status-indicator" class="w-4 h-4 rounded-full bg-gray-500 transition-colors duration-300"></div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Status Bot</h2>
                        <p id="status-text" class="text-gray-400">Idle</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:flex items-center gap-6 text-center w-full lg:w-auto">
                    <div>
                        <label for="simulation-capital" class="text-sm text-gray-400 block mb-1">Modal ($)</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">$</span>
                            <input type="number" id="simulation-capital" value="100" class="bg-gray-900 border border-gray-700 rounded-lg w-28 pl-7 pr-2 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:outline-none">
                        </div>
                    </div>
                    <div>
                        <label for="max-profit" class="text-sm text-gray-400 block mb-1">Maks Profit (%)</label>
                        <div class="relative">
                             <input type="number" id="max-profit" value="25" class="bg-gray-900 border border-gray-700 rounded-lg w-28 pl-4 pr-2 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:outline-none">
                             <span class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">%</span>
                        </div>
                    </div>
                    <div class="border-l border-gray-700 pl-6">
                        <p class="text-sm text-gray-400">Siklus Pindai</p>
                        <p id="scan-count" class="text-2xl font-bold text-white">0</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-400">Pindai Terakhir</p>
                        <p id="last-scan-time" class="text-2xl font-bold text-white">N/A</p>
                    </div>
                </div>
                <button id="toggle-scan-btn" class="w-full lg:w-auto px-8 py-4 font-semibold text-white bg-blue-600 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300 btn-glow">
                    Mulai Memindai
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-5 gap-8">
            <div class="lg:col-span-3">
                <h2 class="text-3xl font-bold text-white mb-4 fade-in flex items-center gap-3" style="animation-delay: 200ms;">
                    Peluang Ditemukan
                </h2>
                <div id="opportunities-container" class="space-y-4 max-h-[65vh] overflow-y-auto pr-3">
                    <div id="placeholder" class="text-center py-16 text-gray-500 fade-in" style="animation-delay: 300ms;">
                        <svg class="mx-auto h-12 w-12 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
                        <p class="mt-2">Mulai pemindaian untuk mencari peluang.</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="flex justify-between items-center mb-4 fade-in" style="animation-delay: 200ms;">
                    <h2 class="text-3xl font-bold text-white flex items-center gap-3">
                        Log Aktivitas
                    </h2>
                    <button id="copy-log-btn" class="text-sm bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-full transition-colors">Salin</button>
                </div>
                <div class="glass-card p-4 h-[65vh] overflow-y-auto">
                    <pre id="log-panel" class="log-panel text-xs text-gray-400 whitespace-pre-wrap"></pre>
                </div>
            </div>
        </div>

        <footer class="text-center text-gray-500 mt-12 fade-in" style="animation-delay: 400ms;">
            <p><strong>Penting:</strong> Perhitungan profit belum termasuk gas fee, slippage, atau pajak. Selalu lakukan riset Anda sendiri (DYOR) sebelum melakukan perdagangan.</p>
        </footer>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log("CLIENT: DOM fully loaded. Initializing script...");

            const toggleBtn = document.getElementById('toggle-scan-btn');
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            const scanCountEl = document.getElementById('scan-count');
            const lastScanTimeEl = document.getElementById('last-scan-time');
            const opportunitiesContainer = document.getElementById('opportunities-container');
            const placeholder = document.getElementById('placeholder');
            const logPanel = document.getElementById('log-panel');
            const copyLogBtn = document.getElementById('copy-log-btn');
            const capitalInput = document.getElementById('simulation-capital');
            const maxProfitInput = document.getElementById('max-profit');

            let isScanning = false;
            let updateInterval;
            let debounceTimer;
            
            function generateAvatar(symbol) {
                const colors = ['#f87171', '#fb923c', '#fbbf24', '#a3e635', '#4ade80', '#34d399', '#22d3ee', '#60a5fa', '#818cf8', '#c084fc', '#f472b6'];
                if (!symbol) return `<div class="w-10 h-10 rounded-full flex-shrink-0 bg-gray-700"></div>`;
                const charCodeSum = symbol.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
                const color = colors[charCodeSum % colors.length];
                const initial = symbol.charAt(0).toUpperCase();
                return `<div class="w-10 h-10 rounded-full flex-shrink-0 flex items-center justify-center text-white font-bold text-lg" style="background-color: ${color};">${initial}</div>`;
            }

            function handleToggleScan() {
                console.log("CLIENT: handleToggleScan function called.");
                fetch('/toggle-scan', { method: 'POST' })
                    .then(response => {
                        console.log("CLIENT: Received response from /toggle-scan. Status:", response.status);
                        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                        return response.json();
                    })
                    .then(data => {
                        console.log("CLIENT: Data from /toggle-scan:", data);
                        if (data.status === 'scan_started') {
                            isScanning = true;
                            updateUIForScanning();
                            updateInterval = setInterval(getStatus, 2000);
                        } else if (data.status === 'scan_stopped') {
                            isScanning = false;
                            updateUIForStopped();
                            if (updateInterval) clearInterval(updateInterval);
                        }
                    })
                    .catch(e => {
                        console.error("CLIENT: Error during fetch to /toggle-scan:", e);
                        statusText.textContent = "Error memulai scan.";
                    });
            }

            function setupSettingsListener(inputElement, settingsKey) {
                inputElement.addEventListener('input', (e) => {
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        const value = e.target.value;
                        if (value && value > 0) {
                            fetch('/update-settings', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ [settingsKey]: value })
                            });
                        }
                    }, 500);
                });
            }

            function getStatus() {
                fetch('/get-status')
                    .then(response => response.json())
                    .then(data => {
                        statusText.textContent = data.status;
                        scanCountEl.textContent = data.scan_count;
                        lastScanTimeEl.textContent = data.last_scan_time || 'N/A';
                        
                        if (data.is_running && !isScanning) {
                            isScanning = true;
                            updateUIForScanning();
                            if (!updateInterval) updateInterval = setInterval(getStatus, 2000);
                        } else if (!data.is_running && isScanning) {
                            isScanning = false;
                            updateUIForStopped();
                            if (updateInterval) clearInterval(updateInterval);
                            updateInterval = null;
                        }

                        renderOpportunities(data.opportunities);
                        renderLogs(data.logs, logPanel);
                    });
            }
            
            function renderOpportunities(opportunities) {
                if (opportunities.length === 0) {
                    placeholder.style.display = 'block';
                    opportunitiesContainer.innerHTML = '';
                    opportunitiesContainer.appendChild(placeholder);
                    return;
                }

                placeholder.style.display = 'none';
                opportunitiesContainer.innerHTML = '';

                opportunities.forEach((op, index) => {
                    const avatar = generateAvatar(op.baseToken);
                    const cardId = `op-card-${index}`;
                    const card = document.createElement('div');
                    card.id = cardId;
                    card.className = "glass-card p-4 fade-in";
                    card.style.animationDelay = `${index * 50}ms`;
                    card.innerHTML = `
                        <div class="flex items-center gap-4">
                            ${avatar}
                            <div class="flex-grow">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-bold text-white">${op.baseToken}/${op.quoteToken}</h3>
                                        <p class="text-xs text-gray-400 bg-gray-700 px-2 py-0.5 rounded-full inline-block">${op.chainId}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-bold text-green-400">+${op.profit_percentage}%</p>
                                        <p class="text-xs text-gray-500">${op.timestamp}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-3 text-sm mt-3">
                            <a href="${op.buy_url}" target="_blank" class="bg-gray-900 p-3 rounded-xl hover:bg-gray-700 transition-colors">
                                <p class="text-green-400 font-semibold mb-1">BELI di ${op.buy_dex_name}</p>
                                <p class="text-lg text-white font-mono">$${op.buy_price}</p>
                            </a>
                            <a href="${op.sell_url}" target="_blank" class="bg-gray-900 p-3 rounded-xl hover:bg-gray-700 transition-colors">
                                <p class="text-red-400 font-semibold mb-1">JUAL di ${op.sell_dex_name}</p>
                                <p class="text-lg text-white font-mono">$${op.sell_price}</p>
                            </a>
                        </div>
                        <div class="mt-3 pt-3 border-t border-gray-700">
                            <button class="copy-info-btn w-full text-center text-sm text-blue-400 font-semibold hover:bg-gray-700 py-2 rounded-lg transition-colors">
                                Salin Info
                            </button>
                        </div>
                    `;
                    opportunitiesContainer.appendChild(card);
                });
            }
            
            opportunitiesContainer.addEventListener('click', function(e) {
                const button = e.target.closest('.copy-info-btn');
                if (button) {
                    const card = button.closest('.glass-card');
                    const baseToken = card.querySelector('h3').textContent.split('/')[0];
                    const quoteToken = card.querySelector('h3').textContent.split('/')[1];
                    const chainId = card.querySelector('.text-xs.text-gray-400').textContent;
                    const profit = card.querySelector('.text-green-400').textContent;
                    const buyDex = card.querySelector('a:nth-child(1) p:nth-child(1)').textContent.replace('BELI di ', '');
                    const buyPrice = card.querySelector('a:nth-child(1) p:nth-child(2)').textContent;
                    const sellDex = card.querySelector('a:nth-child(2) p:nth-child(1)').textContent.replace('JUAL di ', '');
                    const sellPrice = card.querySelector('a:nth-child(2) p:nth-child(2)').textContent;
                    const buyUrl = card.querySelector('a:nth-child(1)').href;
                    const sellUrl = card.querySelector('a:nth-child(2)').href;
                    
                    const textToCopy = '--- PELUANG ARBITRASE ---\\n' +
                        'Pasangan: ' + baseToken + '/' + quoteToken + '\\n' +
                        'Jaringan: ' + chainId + '\\n' +
                        'Potensi Profit: ' + profit + '\\n\\n' +
                        '-- STRATEGI --\\n' +
                        'Beli di: ' + buyDex + '\\n' +
                        'Harga Beli: ' + buyPrice + '\\n' +
                        'Link Beli: ' + buyUrl + '\\n\\n' +
                        'Jual di: ' + sellDex + '\\n' +
                        'Harga Jual: ' + sellPrice + '\\n' +
                        'Link Jual: ' + sellUrl + '\\n' +
                        '---------------------------';

                    navigator.clipboard.writeText(textToCopy).then(() => {
                        button.textContent = 'Info Disalin!';
                        setTimeout(() => { 
                            button.textContent = 'Salin Info';
                        }, 2000);
                    });
                }
            });

            function renderLogs(logs, panel) {
                const logContent = logs.join('\\n');
                if (panel.textContent !== logContent) {
                    panel.textContent = logContent;
                    panel.scrollTop = 0;
                }
            }

            function updateUIForScanning() {
                toggleBtn.textContent = 'Hentikan Pemindaian';
                toggleBtn.classList.remove('bg-blue-600', 'btn-glow');
                toggleBtn.classList.add('bg-red-600', 'btn-glow-stop');
                statusIndicator.classList.remove('bg-gray-500');
                statusIndicator.classList.add('bg-green-500', 'animate-pulse-green');
                capitalInput.disabled = true;
                maxProfitInput.disabled = true;
            }

            function updateUIForStopped() {
                toggleBtn.textContent = 'Mulai Memindai';
                toggleBtn.classList.remove('bg-red-600', 'btn-glow-stop');
                toggleBtn.classList.add('bg-blue-600', 'btn-glow');
                statusIndicator.classList.remove('bg-green-500', 'animate-pulse-green');
                statusIndicator.classList.add('bg-gray-500');
                statusText.textContent = 'Idle';
                capitalInput.disabled = false;
                maxProfitInput.disabled = false;
            }

            // Inisialisasi
            if (toggleBtn) {
                console.log("CLIENT: Tombol 'Mulai Memindai' ditemukan. Menambahkan event listener.");
                toggleBtn.addEventListener('click', handleToggleScan);
            } else {
                console.error("CLIENT: KRITIS: Tombol 'Mulai Memindai' tidak ditemukan saat inisialisasi.");
            }
            
            setupSettingsListener(capitalInput, 'capital');
            setupSettingsListener(maxProfitInput, 'max_profit');
            getStatus();
        });
    </script>
</body>
</html>
"""

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)

