"""
Pattern recognition for identifying trading patterns and market trends
"""
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from scipy import stats
from scipy.signal import find_peaks, argrelextrema
import ta
from backend.core.logging import get_logger
from backend.core.cache import cache

logger = get_logger(__name__)


class TradingPatterns:
    """Identify common trading patterns in price data"""
    
    @staticmethod
    def detect_support_resistance(df: pd.DataFrame, window: int = 20) -> Dict[str, List[float]]:
        """Detect support and resistance levels"""
        if len(df) < window * 2:
            return {"support": [], "resistance": []}
        
        highs = df['high'].values
        lows = df['low'].values
        
        # Find local maxima (resistance) and minima (support)
        resistance_indices = argrelextrema(highs, np.greater, order=window)[0]
        support_indices = argrelextrema(lows, np.less, order=window)[0]
        
        resistance_levels = highs[resistance_indices]
        support_levels = lows[support_indices]
        
        # Cluster nearby levels
        resistance_clusters = TradingPatterns._cluster_levels(resistance_levels)
        support_clusters = TradingPatterns._cluster_levels(support_levels)
        
        return {
            "support": support_clusters,
            "resistance": resistance_clusters
        }
    
    @staticmethod
    def _cluster_levels(levels: np.ndarray, threshold: float = 0.02) -> List[float]:
        """Cluster nearby price levels"""
        if len(levels) == 0:
            return []
        
        levels = np.sort(levels)
        clusters = []
        current_cluster = [levels[0]]
        
        for level in levels[1:]:
            if abs(level - current_cluster[-1]) / current_cluster[-1] <= threshold:
                current_cluster.append(level)
            else:
                # Finalize current cluster and start new one
                clusters.append(np.mean(current_cluster))
                current_cluster = [level]
        
        # Add the last cluster
        clusters.append(np.mean(current_cluster))
        
        return clusters
    
    @staticmethod
    def detect_breakout(df: pd.DataFrame, support_resistance: Dict[str, List[float]]) -> Dict[str, Any]:
        """Detect breakout patterns"""
        if len(df) < 10:
            return {"type": "none", "confidence": 0.0}
        
        current_price = df['close'].iloc[-1]
        recent_volume = df['volume'].tail(5).mean() if 'volume' in df.columns else 0
        avg_volume = df['volume'].mean() if 'volume' in df.columns else 0
        
        support_levels = support_resistance.get("support", [])
        resistance_levels = support_resistance.get("resistance", [])
        
        # Check for resistance breakout
        for resistance in resistance_levels:
            if current_price > resistance * 1.01:  # 1% above resistance
                volume_confirmation = recent_volume > avg_volume * 1.5 if avg_volume > 0 else False
                confidence = 0.7 if volume_confirmation else 0.4
                
                return {
                    "type": "resistance_breakout",
                    "level": resistance,
                    "current_price": current_price,
                    "volume_confirmation": volume_confirmation,
                    "confidence": confidence
                }
        
        # Check for support breakdown
        for support in support_levels:
            if current_price < support * 0.99:  # 1% below support
                volume_confirmation = recent_volume > avg_volume * 1.5 if avg_volume > 0 else False
                confidence = 0.7 if volume_confirmation else 0.4
                
                return {
                    "type": "support_breakdown",
                    "level": support,
                    "current_price": current_price,
                    "volume_confirmation": volume_confirmation,
                    "confidence": confidence
                }
        
        return {"type": "none", "confidence": 0.0}
    
    @staticmethod
    def detect_trend_reversal(df: pd.DataFrame) -> Dict[str, Any]:
        """Detect potential trend reversal patterns"""
        if len(df) < 20:
            return {"type": "none", "confidence": 0.0}
        
        # Calculate moving averages
        df['sma_10'] = ta.trend.sma_indicator(df['close'], window=10)
        df['sma_20'] = ta.trend.sma_indicator(df['close'], window=20)
        
        # Calculate RSI
        df['rsi'] = ta.momentum.rsi(df['close'], window=14)
        
        current_price = df['close'].iloc[-1]
        current_rsi = df['rsi'].iloc[-1]
        
        # Check for bullish reversal
        if (current_rsi < 30 and  # Oversold
            df['sma_10'].iloc[-1] > df['sma_10'].iloc[-2] and  # MA turning up
            current_price > df['low'].tail(5).min() * 1.02):  # Price above recent low
            
            return {
                "type": "bullish_reversal",
                "rsi": current_rsi,
                "confidence": 0.6
            }
        
        # Check for bearish reversal
        if (current_rsi > 70 and  # Overbought
            df['sma_10'].iloc[-1] < df['sma_10'].iloc[-2] and  # MA turning down
            current_price < df['high'].tail(5).max() * 0.98):  # Price below recent high
            
            return {
                "type": "bearish_reversal",
                "rsi": current_rsi,
                "confidence": 0.6
            }
        
        return {"type": "none", "confidence": 0.0}
    
    @staticmethod
    def detect_consolidation(df: pd.DataFrame) -> Dict[str, Any]:
        """Detect consolidation/sideways movement"""
        if len(df) < 20:
            return {"is_consolidating": False, "confidence": 0.0}
        
        # Calculate price range over recent periods
        recent_high = df['high'].tail(20).max()
        recent_low = df['low'].tail(20).min()
        price_range = (recent_high - recent_low) / recent_low
        
        # Calculate volatility
        returns = df['close'].pct_change().tail(20)
        volatility = returns.std()
        
        # Consolidation criteria
        is_consolidating = (
            price_range < 0.1 and  # Price range less than 10%
            volatility < 0.05  # Low volatility
        )
        
        confidence = 0.8 if price_range < 0.05 else 0.5 if price_range < 0.1 else 0.0
        
        return {
            "is_consolidating": is_consolidating,
            "price_range": price_range,
            "volatility": volatility,
            "confidence": confidence
        }


class VolumeAnalysis:
    """Analyze volume patterns"""
    
    @staticmethod
    def analyze_volume_profile(df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze volume profile and distribution"""
        if len(df) < 10 or 'volume' not in df.columns:
            return {"volume_trend": "unknown", "confidence": 0.0}
        
        # Calculate volume moving average
        df['volume_ma'] = df['volume'].rolling(window=10).mean()
        
        recent_volume = df['volume'].tail(5).mean()
        avg_volume = df['volume_ma'].iloc[-1]
        
        # Volume trend analysis
        volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0
        
        if volume_ratio > 1.5:
            volume_trend = "increasing"
            confidence = min(0.9, volume_ratio / 2)
        elif volume_ratio < 0.7:
            volume_trend = "decreasing"
            confidence = min(0.9, (1 - volume_ratio) * 2)
        else:
            volume_trend = "stable"
            confidence = 0.5
        
        # Price-volume relationship
        price_change = df['close'].pct_change().tail(5).mean()
        volume_change = df['volume'].pct_change().tail(5).mean()
        
        # Healthy trends have volume confirming price movement
        volume_confirmation = (
            (price_change > 0 and volume_change > 0) or
            (price_change < 0 and volume_change > 0)
        )
        
        return {
            "volume_trend": volume_trend,
            "volume_ratio": volume_ratio,
            "volume_confirmation": volume_confirmation,
            "confidence": confidence
        }
    
    @staticmethod
    def detect_volume_spikes(df: pd.DataFrame, threshold: float = 2.0) -> List[Dict[str, Any]]:
        """Detect unusual volume spikes"""
        if len(df) < 20 or 'volume' not in df.columns:
            return []
        
        # Calculate volume moving average and standard deviation
        df['volume_ma'] = df['volume'].rolling(window=20).mean()
        df['volume_std'] = df['volume'].rolling(window=20).std()
        
        spikes = []
        
        for i in range(20, len(df)):
            volume = df['volume'].iloc[i]
            ma = df['volume_ma'].iloc[i]
            std = df['volume_std'].iloc[i]
            
            if volume > ma + threshold * std:
                price_change = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                
                spikes.append({
                    "timestamp": df.index[i] if hasattr(df.index, '__getitem__') else i,
                    "volume": volume,
                    "volume_ratio": volume / ma,
                    "price_change": price_change,
                    "significance": min(5.0, volume / ma)
                })
        
        return spikes[-10:]  # Return last 10 spikes


class PatternRecognitionEngine:
    """Main engine for pattern recognition"""
    
    def __init__(self):
        self.patterns = TradingPatterns()
        self.volume_analyzer = VolumeAnalysis()
    
    async def analyze_patterns(self, token_symbol: str, price_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Comprehensive pattern analysis"""
        cache_key = f"patterns:{token_symbol}:{len(price_data)}"
        cached_result = await cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # Convert to DataFrame
        df = pd.DataFrame(price_data)
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
        
        # Ensure required columns
        required_columns = ['open', 'high', 'low', 'close']
        for col in required_columns:
            if col not in df.columns:
                return {"error": f"Missing required column: {col}"}
        
        if 'volume' not in df.columns:
            df['volume'] = 0  # Default volume
        
        analysis = {
            "token_symbol": token_symbol,
            "data_points": len(df),
            "analysis_timestamp": datetime.utcnow().isoformat()
        }
        
        try:
            # Support and resistance analysis
            support_resistance = self.patterns.detect_support_resistance(df)
            analysis["support_resistance"] = support_resistance
            
            # Breakout detection
            breakout = self.patterns.detect_breakout(df, support_resistance)
            analysis["breakout"] = breakout
            
            # Trend reversal detection
            reversal = self.patterns.detect_trend_reversal(df)
            analysis["trend_reversal"] = reversal
            
            # Consolidation detection
            consolidation = self.patterns.detect_consolidation(df)
            analysis["consolidation"] = consolidation
            
            # Volume analysis
            volume_profile = self.volume_analyzer.analyze_volume_profile(df)
            analysis["volume_profile"] = volume_profile
            
            # Volume spikes
            volume_spikes = self.volume_analyzer.detect_volume_spikes(df)
            analysis["volume_spikes"] = volume_spikes
            
            # Overall pattern score
            analysis["pattern_score"] = self._calculate_pattern_score(analysis)
            
            # Trading signals
            analysis["signals"] = self._generate_trading_signals(analysis)
            
        except Exception as e:
            logger.error(f"Error in pattern analysis for {token_symbol}: {e}")
            analysis["error"] = str(e)
        
        # Cache for 10 minutes
        await cache.set(cache_key, analysis, ttl=600)
        
        return analysis
    
    def _calculate_pattern_score(self, analysis: Dict[str, Any]) -> Dict[str, float]:
        """Calculate overall pattern strength scores"""
        scores = {
            "bullish_score": 0.0,
            "bearish_score": 0.0,
            "neutral_score": 0.0
        }
        
        # Breakout patterns
        breakout = analysis.get("breakout", {})
        if breakout.get("type") == "resistance_breakout":
            scores["bullish_score"] += breakout.get("confidence", 0) * 0.3
        elif breakout.get("type") == "support_breakdown":
            scores["bearish_score"] += breakout.get("confidence", 0) * 0.3
        
        # Trend reversal patterns
        reversal = analysis.get("trend_reversal", {})
        if reversal.get("type") == "bullish_reversal":
            scores["bullish_score"] += reversal.get("confidence", 0) * 0.25
        elif reversal.get("type") == "bearish_reversal":
            scores["bearish_score"] += reversal.get("confidence", 0) * 0.25
        
        # Consolidation
        consolidation = analysis.get("consolidation", {})
        if consolidation.get("is_consolidating"):
            scores["neutral_score"] += consolidation.get("confidence", 0) * 0.2
        
        # Volume confirmation
        volume_profile = analysis.get("volume_profile", {})
        if volume_profile.get("volume_confirmation"):
            # Boost the dominant score
            max_score_key = max(scores.keys(), key=lambda k: scores[k])
            scores[max_score_key] += 0.15
        
        # Normalize scores
        total_score = sum(scores.values())
        if total_score > 0:
            scores = {k: v / total_score for k, v in scores.items()}
        
        return scores
    
    def _generate_trading_signals(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate trading signals based on pattern analysis"""
        signals = []
        
        pattern_scores = analysis.get("pattern_score", {})
        bullish_score = pattern_scores.get("bullish_score", 0)
        bearish_score = pattern_scores.get("bearish_score", 0)
        
        # Strong bullish signal
        if bullish_score > 0.6:
            signals.append({
                "type": "buy",
                "strength": "strong",
                "confidence": bullish_score,
                "reason": "Strong bullish pattern detected"
            })
        elif bullish_score > 0.4:
            signals.append({
                "type": "buy",
                "strength": "moderate",
                "confidence": bullish_score,
                "reason": "Moderate bullish pattern detected"
            })
        
        # Strong bearish signal
        if bearish_score > 0.6:
            signals.append({
                "type": "sell",
                "strength": "strong",
                "confidence": bearish_score,
                "reason": "Strong bearish pattern detected"
            })
        elif bearish_score > 0.4:
            signals.append({
                "type": "sell",
                "strength": "moderate",
                "confidence": bearish_score,
                "reason": "Moderate bearish pattern detected"
            })
        
        # Volume-based signals
        volume_spikes = analysis.get("volume_spikes", [])
        if volume_spikes:
            latest_spike = volume_spikes[-1]
            if latest_spike.get("volume_ratio", 0) > 3.0:
                signal_type = "buy" if latest_spike.get("price_change", 0) > 0 else "sell"
                signals.append({
                    "type": signal_type,
                    "strength": "moderate",
                    "confidence": min(0.8, latest_spike.get("volume_ratio", 0) / 5),
                    "reason": f"High volume spike with {signal_type} pressure"
                })
        
        return signals


# Global pattern recognition engine
pattern_engine = PatternRecognitionEngine()
