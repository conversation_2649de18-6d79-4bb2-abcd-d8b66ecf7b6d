"""
Base API client with rate limiting, retries, and error handling
"""
import asyncio
import time
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
import httpx
from backend.config.settings import settings
from backend.core.logging import APICallLogger
from backend.core.cache import cache
import json


class RateLimiter:
    """Token bucket rate limiter"""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.tokens = requests_per_minute
        self.last_update = time.time()
        self.lock = asyncio.Lock()
    
    async def acquire(self) -> bool:
        """Acquire a token for making a request"""
        async with self.lock:
            now = time.time()
            # Add tokens based on time passed
            time_passed = now - self.last_update
            self.tokens = min(
                self.requests_per_minute,
                self.tokens + time_passed * (self.requests_per_minute / 60.0)
            )
            self.last_update = now
            
            if self.tokens >= 1:
                self.tokens -= 1
                return True
            return False
    
    async def wait_for_token(self):
        """Wait until a token is available"""
        while not await self.acquire():
            await asyncio.sleep(0.1)


class BaseAPIClient(ABC):
    """Base class for all API clients"""
    
    def __init__(
        self, 
        base_url: str, 
        api_key: Optional[str] = None,
        requests_per_minute: int = 60,
        timeout: int = 30
    ):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.rate_limiter = RateLimiter(requests_per_minute)
        self.timeout = timeout
        self.logger = APICallLogger(self.__class__.__name__)
        
        # HTTP client configuration
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(timeout),
            limits=httpx.Limits(max_keepalive_connections=20, max_connections=100)
        )
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def get_headers(self) -> Dict[str, str]:
        """Get default headers for requests"""
        headers = {
            "User-Agent": f"{settings.APP_NAME}/{settings.APP_VERSION}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        if self.api_key:
            headers.update(self.get_auth_headers())
        
        return headers
    
    @abstractmethod
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers (implemented by subclasses)"""
        pass
    
    async def make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        use_cache: bool = True,
        cache_ttl: int = 300,
        max_retries: int = 3
    ) -> Optional[Dict[str, Any]]:
        """Make HTTP request with rate limiting, caching, and retries"""
        
        # Create cache key
        cache_key = None
        if use_cache and method.upper() == "GET":
            cache_key = f"{self.__class__.__name__}:{endpoint}:{hash(str(params))}"
            cached_result = await cache.get(cache_key)
            if cached_result:
                return cached_result
        
        # Wait for rate limit
        await self.rate_limiter.wait_for_token()
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = self.get_headers()
        
        # Log request
        await self.logger.log_request(endpoint, method, params)
        
        start_time = time.time()
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                response = await self.client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    params=params,
                    json=data
                )
                
                response_time = time.time() - start_time
                
                # Handle rate limiting
                if response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 60))
                    await self.logger.log_rate_limit(endpoint, retry_after)
                    
                    if attempt < max_retries:
                        await asyncio.sleep(retry_after)
                        continue
                
                # Log response
                await self.logger.log_response(
                    endpoint, 
                    response.status_code, 
                    response_time,
                    success=response.is_success
                )
                
                if response.is_success:
                    result = response.json()
                    
                    # Cache successful GET requests
                    if cache_key and use_cache:
                        await cache.set(cache_key, result, cache_ttl)
                    
                    return result
                else:
                    error_msg = f"HTTP {response.status_code}: {response.text}"
                    await self.logger.log_response(
                        endpoint, 
                        response.status_code, 
                        response_time,
                        success=False,
                        error=error_msg
                    )
                    
                    if attempt < max_retries and response.status_code >= 500:
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                        continue
                    
                    return None
                    
            except Exception as e:
                last_exception = e
                response_time = time.time() - start_time
                
                await self.logger.log_response(
                    endpoint, 
                    0, 
                    response_time,
                    success=False,
                    error=str(e)
                )
                
                if attempt < max_retries:
                    await asyncio.sleep(2 ** attempt)
                    continue
        
        # All retries failed
        if last_exception:
            raise last_exception
        
        return None
    
    async def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None, **kwargs) -> Optional[Dict[str, Any]]:
        """Make GET request"""
        return await self.make_request("GET", endpoint, params=params, **kwargs)
    
    async def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None, **kwargs) -> Optional[Dict[str, Any]]:
        """Make POST request"""
        return await self.make_request("POST", endpoint, data=data, use_cache=False, **kwargs)


class APIClientFactory:
    """Factory for creating API clients"""
    
    _clients: Dict[str, BaseAPIClient] = {}
    
    @classmethod
    async def get_client(cls, client_type: str) -> BaseAPIClient:
        """Get or create API client instance"""
        if client_type not in cls._clients:
            if client_type == "dexscreener":
                from backend.services.dexscreener_client import DexScreenerClient
                cls._clients[client_type] = DexScreenerClient()
            elif client_type == "coingecko":
                from backend.services.coingecko_client import CoinGeckoClient
                cls._clients[client_type] = CoinGeckoClient()
            elif client_type == "dextools":
                from backend.services.dextools_client import DexToolsClient
                cls._clients[client_type] = DexToolsClient()
            elif client_type == "moralis":
                from backend.services.moralis_client import MoralisClient
                cls._clients[client_type] = MoralisClient()
            elif client_type == "binance":
                from backend.services.binance_client import BinanceClient
                cls._clients[client_type] = BinanceClient()
            else:
                raise ValueError(f"Unknown client type: {client_type}")
        
        return cls._clients[client_type]
    
    @classmethod
    async def close_all(cls):
        """Close all API clients"""
        for client in cls._clients.values():
            await client.client.aclose()
        cls._clients.clear()
