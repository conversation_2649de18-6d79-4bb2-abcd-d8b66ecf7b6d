"""
Structured logging configuration
"""
import logging
import sys
from typing import Any, Dict
import structlog
from backend.config.settings import settings


def configure_logging():
    """Configure structured logging for the application"""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.LOG_FORMAT == "json" 
            else structlog.dev.ConsoleRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL.upper()),
    )
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("aioredis").setLevel(logging.WARNING)


class LoggerMixin:
    """Mixin to add structured logging to classes"""
    
    @property
    def logger(self):
        """Get a bound logger for this class"""
        return structlog.get_logger(self.__class__.__name__)


def get_logger(name: str = None) -> structlog.BoundLogger:
    """Get a structured logger instance"""
    return structlog.get_logger(name)


class APICallLogger:
    """Logger specifically for API calls with metrics"""
    
    def __init__(self, api_name: str):
        self.api_name = api_name
        self.logger = get_logger(f"api.{api_name}")
    
    async def log_request(
        self, 
        endpoint: str, 
        method: str = "GET", 
        params: Dict[str, Any] = None
    ):
        """Log API request"""
        self.logger.info(
            "API request started",
            api=self.api_name,
            endpoint=endpoint,
            method=method,
            params=params or {}
        )
    
    async def log_response(
        self, 
        endpoint: str, 
        status_code: int, 
        response_time: float,
        success: bool = True,
        error: str = None
    ):
        """Log API response"""
        log_data = {
            "api": self.api_name,
            "endpoint": endpoint,
            "status_code": status_code,
            "response_time_ms": round(response_time * 1000, 2),
            "success": success
        }
        
        if error:
            log_data["error"] = error
        
        if success:
            self.logger.info("API request completed", **log_data)
        else:
            self.logger.error("API request failed", **log_data)
    
    async def log_rate_limit(self, endpoint: str, retry_after: int):
        """Log rate limit hit"""
        self.logger.warning(
            "API rate limit hit",
            api=self.api_name,
            endpoint=endpoint,
            retry_after_seconds=retry_after
        )


class ArbitrageLogger:
    """Logger specifically for arbitrage operations"""
    
    def __init__(self):
        self.logger = get_logger("arbitrage")
    
    async def log_opportunity_found(
        self, 
        token_symbol: str, 
        profit_percentage: float,
        buy_dex: str,
        sell_dex: str,
        chain_id: str
    ):
        """Log when arbitrage opportunity is found"""
        self.logger.info(
            "Arbitrage opportunity discovered",
            token=token_symbol,
            profit_percentage=profit_percentage,
            buy_dex=buy_dex,
            sell_dex=sell_dex,
            chain=chain_id
        )
    
    async def log_scan_started(self, session_id: str, config: Dict[str, Any]):
        """Log scan session start"""
        self.logger.info(
            "Arbitrage scan started",
            session_id=session_id,
            config=config
        )
    
    async def log_scan_completed(
        self, 
        session_id: str, 
        duration: float,
        opportunities_found: int,
        tokens_analyzed: int
    ):
        """Log scan session completion"""
        self.logger.info(
            "Arbitrage scan completed",
            session_id=session_id,
            duration_seconds=duration,
            opportunities_found=opportunities_found,
            tokens_analyzed=tokens_analyzed
        )
    
    async def log_validation_result(
        self, 
        opportunity_id: int, 
        is_valid: bool,
        validation_errors: list = None
    ):
        """Log opportunity validation result"""
        log_data = {
            "opportunity_id": opportunity_id,
            "is_valid": is_valid
        }
        
        if validation_errors:
            log_data["validation_errors"] = validation_errors
        
        if is_valid:
            self.logger.info("Opportunity validation passed", **log_data)
        else:
            self.logger.warning("Opportunity validation failed", **log_data)
