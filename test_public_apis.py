#!/usr/bin/env python3
"""
Test script for public APIs
Verifies that all public endpoints are working correctly
"""
import asyncio
import sys
from backend.services.public_api_clients import (
    coingecko_client,
    dexscreener_client,
    binance_client,
    reddit_client,
    cleanup_clients
)

async def test_coingecko():
    """Test CoinGecko public API"""
    print("🦎 Testing CoinGecko API...")
    
    try:
        # Test price endpoint
        prices = await coingecko_client.get_price(["bitcoin", "ethereum"], ["usd"])
        print(f"   ✅ Prices: BTC=${prices.get('bitcoin', {}).get('usd', 'N/A')}, ETH=${prices.get('ethereum', {}).get('usd', 'N/A')}")
        
        # Test coins list
        coins = await coingecko_client.get_coins_list()
        print(f"   ✅ Found {len(coins)} supported coins")
        
        # Test coin data
        btc_data = await coingecko_client.get_coin_data("bitcoin")
        market_cap = btc_data.get("market_data", {}).get("market_cap", {}).get("usd", "N/A")
        print(f"   ✅ Bitcoin market cap: ${market_cap:,}" if isinstance(market_cap, (int, float)) else f"   ✅ Bitcoin data retrieved")
        
        return True
        
    except Exception as e:
        print(f"   ❌ CoinGecko error: {e}")
        return False

async def test_dexscreener():
    """Test DexScreener public API"""
    print("📊 Testing DexScreener API...")
    
    try:
        # Test search
        search_result = await dexscreener_client.search_pairs("USDC")
        pairs = search_result.get("pairs", [])
        print(f"   ✅ Found {len(pairs)} USDC pairs")
        
        if pairs:
            # Test specific token
            first_pair = pairs[0]
            token_address = first_pair.get("baseToken", {}).get("address")
            if token_address:
                token_data = await dexscreener_client.get_pairs_by_token(token_address)
                token_pairs = token_data.get("pairs", [])
                print(f"   ✅ Token {token_address[:8]}... has {len(token_pairs)} pairs")
        
        return True
        
    except Exception as e:
        print(f"   ❌ DexScreener error: {e}")
        return False

async def test_binance():
    """Test Binance public API"""
    print("🔶 Testing Binance API...")
    
    try:
        # Test ticker
        ticker = await binance_client.get_ticker_24hr("BTCUSDT")
        price = ticker.get("lastPrice", "N/A")
        volume = ticker.get("volume", "N/A")
        print(f"   ✅ BTCUSDT: ${price}, Volume: {volume}")
        
        # Test order book
        orderbook = await binance_client.get_orderbook("BTCUSDT", 5)
        bids = len(orderbook.get("bids", []))
        asks = len(orderbook.get("asks", []))
        print(f"   ✅ Order book: {bids} bids, {asks} asks")
        
        # Test exchange info
        exchange_info = await binance_client.get_exchange_info()
        symbols = len(exchange_info.get("symbols", []))
        print(f"   ✅ Exchange info: {symbols} trading pairs")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Binance error: {e}")
        return False

async def test_reddit():
    """Test Reddit public API"""
    print("💬 Testing Reddit API...")
    
    try:
        # Test subreddit posts
        posts = await reddit_client.get_subreddit_posts("cryptocurrency", "hot", 5)
        post_count = len(posts.get("data", {}).get("children", []))
        print(f"   ✅ Retrieved {post_count} posts from r/cryptocurrency")
        
        # Test search
        search_results = await reddit_client.search_posts("bitcoin", "cryptocurrency", 3)
        search_count = len(search_results.get("data", {}).get("children", []))
        print(f"   ✅ Found {search_count} Bitcoin posts")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Reddit error: {e}")
        return False

async def test_sentiment_analyzer():
    """Test sentiment analyzer"""
    print("🧠 Testing Sentiment Analyzer...")
    
    try:
        from backend.services.sentiment_analyzer import sentiment_analyzer
        
        # Test token sentiment
        sentiment = await sentiment_analyzer.analyze_token_sentiment("BTC")
        print(f"   ✅ BTC sentiment: {sentiment.get('sentiment_label', 'N/A')} (confidence: {sentiment.get('confidence', 0):.2f})")
        
        # Test market sentiment
        market_sentiment = await sentiment_analyzer.get_market_sentiment_overview()
        print(f"   ✅ Market sentiment: {market_sentiment.get('sentiment_label', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Sentiment analyzer error: {e}")
        return False

async def test_data_aggregator():
    """Test data aggregator"""
    print("📈 Testing Data Aggregator...")
    
    try:
        from backend.services.data_aggregator import DataAggregator
        
        aggregator = DataAggregator()
        await aggregator.initialize()
        
        # Test comprehensive data
        token_data = await aggregator.get_comprehensive_token_data("BTC")
        sources = len([k for k, v in token_data.items() if v])
        print(f"   ✅ BTC data from {sources} sources")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Data aggregator error: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Testing Public APIs for Crypto Arbitrage Bot v2.0")
    print("=" * 60)
    
    tests = [
        ("CoinGecko", test_coingecko),
        ("DexScreener", test_dexscreener),
        ("Binance", test_binance),
        ("Reddit", test_reddit),
        ("Sentiment Analyzer", test_sentiment_analyzer),
        ("Data Aggregator", test_data_aggregator),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"   ❌ {test_name} test failed: {e}")
            results[test_name] = False
        
        print()  # Add spacing
    
    # Summary
    print("=" * 60)
    print("📊 Test Results Summary:")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The bot is ready to use with public APIs.")
    else:
        print("⚠️  Some tests failed. Check your internet connection and try again.")
    
    # Cleanup
    await cleanup_clients()
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
