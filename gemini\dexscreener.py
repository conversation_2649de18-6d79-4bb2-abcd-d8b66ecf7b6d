import sys
import time
import requests
import datetime
import webbrowser
import asyncio
import aiohttp
import uvicorn
import random
from fastapi import FastAP<PERSON>, Body
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from typing import Dict, Any, List

# --- KONFIGURASI GLOBAL ---
DEXSCREENER_API_BASE = "https://api.dexscreener.com/latest/dex"
COINGECKO_API_BASE = "https://api.coingecko.com/api/v3"

# --- STATE APLIKASI ---
STATE: Dict[str, Any] = {
    "is_running": False,
    "status": "Siap.",
    "opportunities": [],
    "task": None,
    "config": {}
}

# --- KLIEN API & LOGIKA INTI ---
def get_coingecko_token_map():
    print("[LOG] Mengambil peta token terpercaya dari CoinGecko...")
    url = f"{COINGECKO_API_BASE}/coins/list"
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        return {item['symbol'].lower(): item['name'].lower() for item in response.json()}
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Gagal mengambil daftar token dari CoinGecko: {e}")
        return None

async def search_dexscreener_pairs_async(session, query):
    url = f"{DEXSCREENER_API_BASE}/search"
    max_retries = 3
    base_wait_time = 1.0
    for attempt in range(max_retries):
        try:
            async with session.get(url, params={'q': query}) as response:
                if response.status == 429:
                    retry_after_header = response.headers.get("Retry-After")
                    wait_time = float(retry_after_header) if retry_after_header else base_wait_time * (2 ** attempt)
                    wait_time += random.uniform(0, 1)
                    print(f"[RATE_LIMIT] Kena limit untuk '{query}'. Mencoba lagi dalam {wait_time:.2f} detik...")
                    await asyncio.sleep(wait_time)
                    continue
                response.raise_for_status()
                return (await response.json()).get('pairs', [])
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            print(f"[LOG] Error saat mencari '{query}': {e}")
            return []
    print(f"[LOG] Gagal mengambil data untuk '{query}' setelah {max_retries} percobaan.")
    return []

def find_opportunities(all_dex_pairs: Dict, config: Dict) -> List[Dict]:
    """Fungsi yang difokuskan untuk mencari peluang DEX vs DEX di jaringan yang sama."""
    dex_token_prices = {}
    for chain_id, pairs in all_dex_pairs.items():
        for pair in pairs:
            try:
                base_token_symbol = pair['baseToken']['symbol']
                price_usd = float(pair.get('priceUsd', 0))
                if price_usd > 0:
                    if base_token_symbol not in dex_token_prices:
                        dex_token_prices[base_token_symbol] = []
                    dex_token_prices[base_token_symbol].append({
                        'price': price_usd, 'dex': pair['dexId'], 'pair_address': pair['pairAddress'],
                        'chain_id': chain_id, 'url': pair.get('url')
                    })
            except (KeyError, TypeError, ValueError):
                continue

    opportunities = []
    found_paths = set()

    for token_symbol, entries in dex_token_prices.items():
        if len(entries) < 2:
            continue
        for i in range(len(entries)):
            for j in range(i + 1, len(entries)):
                dex1, dex2 = entries[i], entries[j]
                if dex1['chain_id'] != dex2['chain_id'] or dex1['dex'] == dex2['dex']:
                    continue
                buy_dex, sell_dex = (dex1, dex2) if dex1['price'] < dex2['price'] else (dex2, dex1)
                profit_margin = ((sell_dex['price'] - buy_dex['price']) / buy_dex['price']) * 100
                net_profit = profit_margin - (config['fees'] * 2)
                path_key = f"{token_symbol}-{buy_dex['chain_id']}-{min(buy_dex['dex'], sell_dex['dex'])}-{max(buy_dex['dex'], sell_dex['dex'])}"
                if config['min_profit'] < net_profit < config['max_profit']:
                    if path_key not in found_paths:
                        found_paths.add(path_key)
                        data = {
                            'token': token_symbol, 'network': buy_dex['chain_id'],
                            'buy_dex': buy_dex['dex'], 'buy_price': buy_dex['price'], 'buy_dex_url': buy_dex['url'],
                            'sell_dex': sell_dex['dex'], 'sell_price': sell_dex['price'], 'sell_dex_url': sell_dex['url'],
                            'profit': net_profit, 'time': datetime.datetime.now().strftime('%H:%M:%S')
                        }
                        print(f"[PELUANG] Ditemukan: {data['token']} di {data['network']} | Profit: {data['profit']:.2f}%")
                        opportunities.append(data)
    return opportunities

async def worker(queue: asyncio.Queue, session: aiohttp.ClientSession, results: List):
    while True:
        try:
            token = await queue.get()
            search_result = await search_dexscreener_pairs_async(session, token)
            if search_result:
                results.extend(search_result)
            queue.task_done()
        except asyncio.CancelledError:
            break
        except Exception as e:
            print(f"[WORKER ERROR] Terjadi kesalahan: {e}")
            queue.task_done()

async def run_arbitrage_monitoring(config: Dict):
    STATE["is_running"] = True
    STATE["opportunities"] = []
    trusted_token_map = get_coingecko_token_map()
    if not trusted_token_map:
        STATE["status"] = "Error: Gagal memuat peta token dari CoinGecko."
        STATE["is_running"] = False
        return
    
    binance_symbols = get_binance_symbols()
    if not binance_symbols:
        STATE["status"] = "Error: Gagal mengambil simbol dari Binance."
        STATE["is_running"] = False
        return
    
    binance_base_assets = sorted(list({s['baseAsset'] for s in binance_symbols if s['status'] == 'TRADING'}))
    if not STATE["is_running"]: return
    print(f"[CONFIG] Memulai dengan filter: {config}")

    timeout = aiohttp.ClientTimeout(total=20)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        while STATE["is_running"]:
            try:
                start_time = time.time()
                STATE["status"] = f"Memulai siklus baru pada {datetime.datetime.now().strftime('%H:%M:%S')}"
                
                queue = asyncio.Queue()
                tokens_to_search = [s for s in binance_base_assets[:200] if len(s) > 1]
                for token in tokens_to_search:
                    queue.put_nowait(token)
                
                search_results_list = []
                num_workers = 10
                STATE["status"] = f"Menjalankan {len(tokens_to_search)} pencarian dengan {num_workers} pekerja..."
                
                worker_tasks = [asyncio.create_task(worker(queue, session, search_results_list)) for _ in range(num_workers)]
                await queue.join()
                for task in worker_tasks:
                    task.cancel()
                await asyncio.gather(*worker_tasks, return_exceptions=True)

                STATE["status"] = "Memfilter & memvalidasi hasil pencarian..."
                all_dex_pairs = {chain_id: [] for chain_id in config['networks']}
                total_pairs_found, filtered_pairs_count = 0, 0
                min_age_timestamp_ms = (time.time() - config['min_age_hours'] * 3600) * 1000
                for pair in search_results_list:
                    total_pairs_found += 1
                    chain_id = pair.get('chainId')
                    if chain_id not in config['networks']: continue
                    base_token = pair.get('baseToken', {})
                    token_symbol_lower = base_token.get('symbol', '').lower()
                    token_name_lower = base_token.get('name', '').lower()
                    official_name = trusted_token_map.get(token_symbol_lower)
                    if not official_name or official_name not in token_name_lower: continue
                    liquidity_usd = pair.get('liquidity', {}).get('usd', 0)
                    if liquidity_usd < config['min_liquidity']: continue
                    if pair.get('volume', {}).get('h24', 0) < config['min_volume']: continue
                    if pair.get('pairCreatedAt', 0) > min_age_timestamp_ms: continue
                    if pair.get('txns', {}).get('h1', {}).get('sells', 0) < config['min_h1_sells']: continue
                    if config['max_trade_size'] > (liquidity_usd * 0.02): continue
                    all_dex_pairs[chain_id].append(pair)
                    filtered_pairs_count += 1
                
                print(f"[LOG] Siklus Pencarian: Total {total_pairs_found} pasangan ditemukan, {filtered_pairs_count} lolos semua filter.")
                if any(all_dex_pairs.values()):
                    STATE["status"] = "Menganalisis data untuk peluang arbitrase..."
                    new_opportunities = find_opportunities(all_dex_pairs, config)
                    STATE["opportunities"] = (new_opportunities + STATE["opportunities"])[:50]
                else:
                    STATE["status"] = "Tidak ada data DEX yang relevan ditemukan."
                cycle_duration = time.time() - start_time
                print(f"[LOG] Siklus selesai dalam {cycle_duration:.2f} detik.\n" + "="*50)
                STATE["status"] = f"Siklus selesai. Menunggu 60 detik..."
                await asyncio.sleep(max(0, 60 - cycle_duration))
            except asyncio.CancelledError:
                print("[LOG] Tugas pemantauan dibatalkan.")
                break
            except Exception as e:
                print(f"[ERROR] Terjadi kesalahan di loop utama: {e}")
                STATE["status"] = f"Error: {e}"
                await asyncio.sleep(30)
    STATE["is_running"] = False
    STATE["status"] = "Pemantauan dihentikan."
    print("[LOG] Proses latar belakang telah berhenti.")

# --- SETUP APLIKASI WEB (FastAPI) ---
app = FastAPI()
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"])

@app.post("/start")
async def start_monitoring(config: Dict[str, Any] = Body(...)):
    if STATE["is_running"]: return {"status": "error", "message": "Pemantauan sudah berjalan."}
    STATE["config"] = config
    STATE["task"] = asyncio.create_task(run_arbitrage_monitoring(config))
    return {"status": "success", "message": "Pemantauan dimulai."}

@app.post("/stop")
async def stop_monitoring():
    if not STATE["is_running"] or STATE["task"] is None: return {"status": "error", "message": "Pemantauan tidak sedang berjalan."}
    STATE["is_running"] = False
    STATE["task"].cancel()
    STATE["task"] = None
    return {"status": "success", "message": "Sinyal berhenti dikirim."}

@app.get("/data")
async def get_data():
    return {"is_running": STATE["is_running"], "status": STATE["status"], "opportunities": STATE["opportunities"]}

# --- KONTEN FRONTEND (HTML, CSS, JS) ---
HTML_CONTENT = """
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arbitrage Monitor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #121212; color: #e0e0e0; }
        .glass-pane { background: rgba(22, 22, 24, 0.6); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1); }
        .form-label { font-weight: 500; color: #a0a0a0; }
        .form-input { background-color: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 8px; padding: 8px 12px; font-size: 15px; width: 100%; transition: all 0.2s; }
        .form-input:focus { border-color: #8b5cf6; outline: none; background-color: rgba(255, 255, 255, 0.1); }
        .btn { color: white; font-weight: 600; border: none; padding: 10px 20px; border-radius: 8px; display: flex; align-items: center; justify-content: center; gap: 8px; transition: all 0.2s; }
        .btn-primary { background-color: #6d28d9; }
        .btn-primary:hover { background-color: #7c3aed; }
        .btn:disabled { background-color: #374151; color: #9ca3af; cursor: not-allowed; }
        .table-container { border: 1px solid rgba(255, 255, 255, 0.1); }
        th { color: #a0a0a0; font-weight: 600; padding: 12px; border-bottom: 1px solid rgba(255, 255, 255, 0.1); }
        td { padding: 12px; border-bottom: 1px solid rgba(255, 255, 255, 0.05); }
        tr:last-child td { border-bottom: none; }
        .link { color: #8b5cf6; text-decoration: none; font-weight: 500; }
        .link:hover { text-decoration: underline; }
        #loader {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(18, 18, 18, 0.8); backdrop-filter: blur(5px);
            z-index: 9999; display: flex; align-items: center; justify-content: center;
            flex-direction: column; gap: 1rem; transition: opacity 0.3s;
            opacity: 0; pointer-events: none;
        }
        #loader.visible { opacity: 1; pointer-events: auto; }
        .scanner { width: 150px; height: 150px; position: relative; }
        .scanner .ring {
            position: absolute; inset: 0; border: 2px solid transparent;
            border-radius: 50%; animation: spin 2s linear infinite;
        }
        .scanner .ring:nth-child(1) { border-top-color: #8b5cf6; animation-delay: 0s; }
        .scanner .ring:nth-child(2) { border-right-color: #6366f1; animation-delay: -0.5s; }
        .scanner .ring:nth-child(3) { border-bottom-color: #3b82f6; animation-delay: -1s; }
        .scanner .ring:nth-child(4) { border-left-color: #14b8a6; animation-delay: -1.5s; }
        @keyframes spin { 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div id="loader"><div class="scanner"><div class="ring"></div><div class="ring"></div><div class="ring"></div><div class="ring"></div></div><p id="loader-status" class="text-lg font-medium text-gray-300"></p></div>
    <div class="min-h-screen p-4 md:p-8">
        <div class="max-w-7xl mx-auto">
            <header class="mb-6"><h1 class="text-4xl font-bold tracking-tighter">DEX Arbitrage Monitor</h1><p id="status-bar" class="text-gray-400 mt-1">Siap.</p></header>
            <div id="controls-container" class="glass-pane p-4 rounded-xl mb-6"></div>
            <div id="table-container" class="table-container rounded-xl overflow-hidden"></div>
        </div>
    </div>

    <script>
        const API_URL = window.location.origin;
        const controlsContainer = document.getElementById('controls-container');
        const tableContainer = document.getElementById('table-container');
        const statusBar = document.getElementById('status-bar');
        const loader = document.getElementById('loader');
        const loaderStatus = document.getElementById('loader-status');
        
        let state = {
            isRunning: false,
            opportunities: [],
            config: {
                networks: "ethereum,bsc,solana", fees: "0.5", min_profit: "1.5", max_profit: "150",
                min_liquidity: "10000", min_volume: "10000", min_age_hours: "24",
                min_h1_sells: "5", max_trade_size: "500",
            }
        };

        function renderControls() {
            const { isRunning, config } = state;
            const configEntries = Object.entries(config);
            controlsContainer.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    ${configEntries.map(([key, value]) => `
                        <div>
                            <label for="${key}" class="form-label capitalize">${key.replace(/_/g, ' ')}</label>
                            <input type="text" id="${key}" name="${key}" class="form-input mt-1" value="${value}" ${isRunning ? 'disabled' : ''}>
                        </div>
                    `).join('')}
                </div>
                <div class="mt-6 flex justify-end space-x-4">
                    <button id="start-btn" class="btn btn-primary" ${isRunning ? 'disabled' : ''}>Mulai Analisa</button>
                    <button id="stop-btn" class="btn bg-red-600 hover:bg-red-700" ${!isRunning ? 'disabled' : ''}>Hentikan</button>
                </div>
            `;
            document.getElementById('start-btn').addEventListener('click', startMonitoring);
            document.getElementById('stop-btn').addEventListener('click', stopMonitoring);
            configEntries.forEach(([key]) => {
                document.getElementById(key).addEventListener('change', handleConfigChange);
            });
        }

        function handleConfigChange(e) {
            state.config[e.target.name] = e.target.value;
        }

        function renderTable() {
            const { opportunities } = state;
            if (opportunities.length === 0) {
                tableContainer.innerHTML = '<div class="text-center py-12 text-gray-500">Menunggu peluang atau tidak ada yang ditemukan...</div>';
                return;
            }
            const headers = ['Token', 'Jaringan', 'Beli di (DEX)', 'Harga Beli ($)', 'Jual di (DEX)', 'Harga Jual ($)', 'Profit Bersih', 'Waktu'];
            const rows = opportunities.map(o => `
                <tr class="fade-in">
                    <td class="font-bold">${o.token}</td><td>${o.network}</td>
                    <td><a href="${o.buy_dex_url}" target="_blank" class="link">${o.buy_dex}</a></td><td>${o.buy_price.toFixed(6)}</td>
                    <td><a href="${o.sell_dex_url}" target="_blank" class="link">${o.sell_dex}</a></td><td>${o.sell_price.toFixed(6)}</td>
                    <td class="font-bold text-green-400">${o.profit.toFixed(2)}%</td><td>${o.time}</td>
                </tr>
            `).join('');
            tableContainer.innerHTML = `
                <table class="w-full text-sm text-left">
                    <thead class="bg-white/5"><tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr></thead>
                    <tbody>${rows}</tbody>
                </table>
            `;
        }
        
        async function startMonitoring() {
            const numericConfig = Object.fromEntries(
                Object.entries(state.config).map(([key, value]) => [key, key === 'networks' ? value.split(',').map(n=>n.trim()) : parseFloat(value)])
            );
            loader.classList.add('visible');
            loaderStatus.textContent = 'Memulai pemantauan...';
            try {
                await fetch(`${API_URL}/start`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(numericConfig) });
            } catch (error) {
                statusBar.textContent = 'Error: Gagal menghubungi server.';
                loader.classList.remove('visible');
            }
        }

        async function stopMonitoring() {
            loader.classList.remove('visible');
            try {
                await fetch(`${API_URL}/stop`, { method: 'POST' });
            } catch (error) {
                statusBar.textContent = 'Error: Gagal menghubungi server.';
            }
        }

        async function fetchData() {
            try {
                const response = await fetch(`${API_URL}/data`);
                if (!response.ok) return;
                const data = await response.json();
                
                statusBar.textContent = data.status;
                state.isRunning = data.is_running;
                state.opportunities = data.opportunities;

                const isActivelyWorking = data.is_running && !data.status.includes("Menunggu") && !data.status.includes("Siap");
                if (isActivelyWorking) {
                    loader.classList.add('visible');
                    loaderStatus.textContent = data.status;
                } else {
                    loader.classList.remove('visible');
                }
                
                renderControls();
                renderTable();
            } catch (error) {
                statusBar.textContent = "Koneksi ke server terputus. Mencoba lagi...";
                loader.classList.remove('visible');
            }
        }
        
        // Initial render
        renderControls();
        renderTable();
        setInterval(fetchData, 2000);
    </script>
</body>
</html>
"""

@app.get("/", response_class=HTMLResponse)
async def read_root():
    return HTML_CONTENT

# --- MENJALANKAN APLIKASI ---
if __name__ == "__main__":
    print("="*50)
    print("Aplikasi Web Monitor Arbitrase")
    print("Buka browser Anda dan navigasikan ke: http://127.0.0.1:8000")
    print("="*50)
    uvicorn.run(app, host="127.0.0.1", port=8000)
