<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arbitrage Monitor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- React & Framer Motion Dependencies -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/framer-motion@10/dist/framer-motion.umd.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #121212;
            color: #e0e0e0;
        }
        .glass-pane {
            background: rgba(22, 22, 24, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .form-label { font-weight: 500; color: #a0a0a0; }
        .form-input { 
            background-color: rgba(255, 255, 255, 0.05); 
            border: 1px solid rgba(255, 255, 255, 0.1); 
            border-radius: 8px; padding: 8px 12px; 
            font-size: 15px; width: 100%;
            transition: all 0.2s;
        }
        .form-input:focus { border-color: #8b5cf6; outline: none; background-color: rgba(255, 255, 255, 0.1); }
        .btn { 
            color: white; font-weight: 600; border: none; 
            padding: 10px 20px; border-radius: 8px; 
            display: flex; align-items: center; justify-content: center; 
            gap: 8px; transition: all 0.2s;
        }
        .btn-primary { background-color: #6d28d9; }
        .btn-primary:hover { background-color: #7c3aed; }
        .btn:disabled { background-color: #374151; color: #9ca3af; cursor: not-allowed; }
        .table-container { border: 1px solid rgba(255, 255, 255, 0.1); }
        th { color: #a0a0a0; font-weight: 600; padding: 12px; border-bottom: 1px solid rgba(255, 255, 255, 0.1); }
        td { padding: 12px; border-bottom: 1px solid rgba(255, 255, 255, 0.05); }
        tr:last-child td { border-bottom: none; }
        .link { color: #8b5cf6; text-decoration: none; font-weight: 500; }
        .link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // --- PERBAIKAN: Menjalankan kode setelah semua konten DOM dimuat ---
        window.addEventListener('DOMContentLoaded', () => {
            const { useState, useEffect } = React;
            
            // Memastikan FramerMotion ada sebelum digunakan
            if (!window.FramerMotion) {
                console.error("Framer Motion library not loaded.");
                document.getElementById('root').innerText = "Error: Framer Motion library could not be loaded.";
                return;
            }
            const { motion, AnimatePresence } = window.FramerMotion;

            const API_URL = 'http://127.0.0.1:8000';

            // --- Komponen-komponen React ---

            const Loader = ({ status }) => (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex flex-col items-center justify-center"
                >
                    <motion.div
                        className="w-24 h-24"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    >
                        <div className="w-full h-full border-4 border-t-violet-500 border-l-violet-500 border-r-transparent border-b-transparent rounded-full"></div>
                    </motion.div>
                    <p className="mt-4 text-lg text-gray-300">{status}</p>
                </motion.div>
            );

            const Controls = ({ onStart, onStop, isRunning, config, setConfig }) => {
                const handleChange = (e) => {
                    setConfig({ ...config, [e.target.name]: e.target.value });
                };

                return (
                    <div className="glass-pane p-4 rounded-xl mb-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {Object.entries(config).map(([key, value]) => (
                                <div key={key}>
                                    <label htmlFor={key} className="form-label capitalize">{key.replace(/_/g, ' ')}</label>
                                    <input type="text" id={key} name={key} className="form-input mt-1" value={value} onChange={handleChange} disabled={isRunning} />
                                </div>
                            ))}
                        </div>
                        <div className="mt-6 flex justify-end space-x-4">
                            <motion.button whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} onClick={onStart} disabled={isRunning} className="btn btn-primary">Mulai Analisa</motion.button>
                            <motion.button whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} onClick={onStop} disabled={!isRunning} className="btn bg-red-600 hover:bg-red-700">Hentikan</motion.button>
                        </div>
                    </div>
                );
            };

            const OpportunitiesTable = ({ opportunities }) => {
                if (opportunities.length === 0) {
                    return <div className="text-center py-12 text-gray-500 glass-pane rounded-xl">Menunggu peluang atau tidak ada yang ditemukan...</div>;
                }

                return (
                    <div className="table-container rounded-xl overflow-hidden">
                        <table className="w-full text-sm text-left">
                            <thead className="bg-white/5">
                                <tr>
                                    <th scope="col">Token</th><th scope="col">Jaringan</th>
                                    <th scope="col">Beli di (DEX)</th><th scope="col">Harga Beli ($)</th>
                                    <th scope="col">Jual di (DEX)</th><th scope="col">Harga Jual ($)</th>
                                    <th scope="col">Profit Bersih</th><th scope="col">Waktu</th>
                                </tr>
                            </thead>
                            <tbody>
                                <AnimatePresence>
                                    {opportunities.map(o => (
                                        <motion.tr
                                            key={o.buy_dex_url + o.sell_dex_url}
                                            layout
                                            initial={{ opacity: 0, y: -20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, x: -50 }}
                                            transition={{ duration: 0.5 }}
                                            className="border-b border-white/5"
                                        >
                                            <td className="font-bold">{o.token}</td><td>{o.network}</td>
                                            <td><a href={o.buy_dex_url} target="_blank" className="link">{o.buy_dex}</a></td><td>{o.buy_price.toFixed(6)}</td>
                                            <td><a href={o.sell_dex_url} target="_blank" className="link">{o.sell_dex}</a></td><td>{o.sell_price.toFixed(6)}</td>
                                            <td className="font-bold text-green-400">{o.profit.toFixed(2)}%</td><td>{o.time}</td>
                                        </motion.tr>
                                    ))}
                                </AnimatePresence>
                            </tbody>
                        </table>
                    </div>
                );
            };

            function App() {
                const [state, setState] = useState({
                    isRunning: false,
                    status: "Siap.",
                    opportunities: [],
                });
                const [config, setConfig] = useState({
                    networks: "ethereum,bsc,solana",
                    fees: "0.5",
                    min_profit: "1.5",
                    max_profit: "150",
                    min_liquidity: "10000",
                    min_volume: "10000",
                    min_age_hours: "24",
                    min_h1_sells: "5",
                    max_trade_size: "500",
                });

                const isActivelyWorking = state.isRunning && !state.status.includes("Menunggu") && !state.status.includes("Siap");

                useEffect(() => {
                    const fetchData = async () => {
                        try {
                            const response = await fetch(`${API_URL}/data`);
                            if (!response.ok) throw new Error("Network response was not ok");
                            const data = await response.json();
                            setState(data);
                        } catch (error) {
                            setState(s => ({ ...s, status: "Koneksi ke server terputus." }));
                        }
                    };
                    const intervalId = setInterval(fetchData, 2000);
                    return () => clearInterval(intervalId);
                }, []);

                const handleStart = async () => {
                    const numericConfig = Object.fromEntries(
                        Object.entries(config).map(([key, value]) => [key, key === 'networks' ? value.split(',').map(n=>n.trim()) : parseFloat(value)])
                    );
                    try {
                        await fetch(`${API_URL}/start`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(numericConfig) });
                    } catch (error) {
                        setState(s => ({ ...s, status: "Gagal memulai: tidak bisa menghubungi server." }));
                    }
                };

                const handleStop = async () => {
                    try {
                        await fetch(`${API_URL}/stop`, { method: 'POST' });
                    } catch (error) {
                        setState(s => ({ ...s, status: "Gagal menghentikan: tidak bisa menghubungi server." }));
                    }
                };

                return (
                    <div className="min-h-screen p-4 md:p-8">
                        <AnimatePresence>
                            {isActivelyWorking && <Loader status={state.status} />}
                        </AnimatePresence>
                        <div className="max-w-7xl mx-auto">
                            <header className="mb-6">
                                <h1 className="text-4xl font-bold tracking-tighter">Arbitrage Monitor</h1>
                                <p className="text-gray-400 mt-1">{state.status}</p>
                            </header>
                            <Controls onStart={handleStart} onStop={handleStop} isRunning={state.isRunning} config={config} setConfig={setConfig} />
                            <OpportunitiesTable opportunities={state.opportunities} />
                        </div>
                    </div>
                );
            }

            ReactDOM.render(<App />, document.getElementById('root'));
        });
    </script>
</body>
</html>
