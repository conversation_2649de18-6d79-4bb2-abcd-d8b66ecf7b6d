import React, { useMemo } from 'react'
import { <PERSON>hnut } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  ArcElement,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'chart.js'
import { motion } from 'framer-motion'
import Card, { Card<PERSON>eader, CardTitle, CardContent } from '@components/ui/Card'
import Badge from '@components/ui/Badge'
import { ArbitrageOpportunity } from '@types/index'
import { formatPercentage, formatChainName } from '@utils/formatters'

ChartJS.register(ArcElement, Tooltip, Legend)

interface NetworkDistributionProps {
  opportunities: ArbitrageOpportunity[]
}

const NetworkDistribution: React.FC<NetworkDistributionProps> = ({
  opportunities
}) => {
  const { chartData, networkStats } = useMemo(() => {
    // Count opportunities by network
    const networkCounts: { [key: string]: number } = {}
    const networkProfits: { [key: string]: number[] } = {}
    
    opportunities.forEach(opp => {
      const network = opp.chain_id
      networkCounts[network] = (networkCounts[network] || 0) + 1
      
      if (!networkProfits[network]) {
        networkProfits[network] = []
      }
      networkProfits[network].push(opp.profit_percentage)
    })

    // Define network colors
    const networkColors: { [key: string]: string } = {
      ethereum: '#627EEA',
      bsc: '#F3BA2F',
      polygon: '#8247E5',
      arbitrum: '#28A0F0',
      avalanche: '#E84142',
      solana: '#9945FF',
      fantom: '#1969FF',
      optimism: '#FF0420',
    }

    const networks = Object.keys(networkCounts)
    const counts = networks.map(network => networkCounts[network])
    const colors = networks.map(network => networkColors[network] || '#6B7280')
    const borderColors = colors.map(color => color + '80')

    // Calculate stats for each network
    const stats = networks.map(network => {
      const profits = networkProfits[network]
      const avgProfit = profits.reduce((sum, p) => sum + p, 0) / profits.length
      const maxProfit = Math.max(...profits)
      const count = networkCounts[network]
      const percentage = (count / opportunities.length) * 100

      return {
        network,
        count,
        percentage,
        avgProfit,
        maxProfit,
        color: networkColors[network] || '#6B7280'
      }
    }).sort((a, b) => b.count - a.count)

    const data = {
      labels: networks.map(formatChainName),
      datasets: [
        {
          data: counts,
          backgroundColor: colors.map(color => color + '40'),
          borderColor: borderColors,
          borderWidth: 2,
          hoverBackgroundColor: colors.map(color => color + '60'),
          hoverBorderColor: colors,
          hoverBorderWidth: 3,
        },
      ],
    }

    return { chartData: data, networkStats: stats }
  }, [opportunities])

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Inter',
            size: 12,
          },
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(15, 15, 35, 0.95)',
        titleColor: 'rgba(255, 255, 255, 0.9)',
        bodyColor: 'rgba(255, 255, 255, 0.8)',
        borderColor: 'rgba(139, 92, 246, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            const label = context.label || ''
            const value = context.parsed
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0)
            const percentage = ((value / total) * 100).toFixed(1)
            return `${label}: ${value} (${percentage}%)`
          }
        }
      },
    },
    cutout: '60%',
    elements: {
      arc: {
        borderWidth: 2,
      },
    },
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Chart */}
      <Card variant="glass" className="h-96">
        <CardHeader>
          <CardTitle>Network Distribution</CardTitle>
        </CardHeader>
        <CardContent className="h-80">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="h-full relative"
          >
            <Doughnut data={chartData} options={options} />
            
            {/* Center text */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {opportunities.length}
                </div>
                <div className="text-sm text-gray-400">
                  Total Opportunities
                </div>
              </div>
            </div>
          </motion.div>
        </CardContent>
      </Card>

      {/* Network Stats */}
      <Card variant="glass" className="h-96">
        <CardHeader>
          <CardTitle>Network Performance</CardTitle>
        </CardHeader>
        <CardContent className="h-80 overflow-y-auto">
          <div className="space-y-4">
            {networkStats.map((stat, index) => (
              <motion.div
                key={stat.network}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-3 bg-dark-800/50 rounded-lg border border-dark-600/30"
              >
                <div className="flex items-center space-x-3">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: stat.color }}
                  />
                  <div>
                    <div className="font-medium text-white">
                      {formatChainName(stat.network)}
                    </div>
                    <div className="text-sm text-gray-400">
                      {stat.count} opportunities ({formatPercentage(stat.percentage)})
                    </div>
                  </div>
                </div>
                
                <div className="text-right space-y-1">
                  <Badge variant="success" size="xs">
                    Avg: {formatPercentage(stat.avgProfit)}
                  </Badge>
                  <div className="text-xs text-gray-400">
                    Max: {formatPercentage(stat.maxProfit)}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default NetworkDistribution
