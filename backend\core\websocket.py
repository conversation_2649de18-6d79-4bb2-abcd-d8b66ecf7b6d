"""
WebSocket manager for real-time updates
"""
import json
import asyncio
from typing import Dict, List, Any, Optional, Set
from fastapi import WebSocket, WebSocketDisconnect
from backend.config.settings import settings
from backend.core.logging import get_logger
import uuid
from datetime import datetime

logger = get_logger(__name__)


class ConnectionManager:
    """Manages WebSocket connections and broadcasting"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        self.subscriptions: Dict[str, Set[str]] = {}  # topic -> connection_ids
        
    async def connect(self, websocket: WebSocket, client_id: str = None) -> str:
        """Accept a new WebSocket connection"""
        await websocket.accept()
        
        if not client_id:
            client_id = str(uuid.uuid4())
            
        self.active_connections[client_id] = websocket
        self.connection_metadata[client_id] = {
            "connected_at": datetime.utcnow(),
            "last_ping": datetime.utcnow(),
            "subscriptions": set()
        }
        
        logger.info(f"WebSocket client connected: {client_id}")
        
        # Send welcome message
        await self.send_personal_message({
            "type": "connection_established",
            "client_id": client_id,
            "timestamp": datetime.utcnow().isoformat()
        }, client_id)
        
        return client_id
    
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection"""
        if client_id in self.active_connections:
            # Remove from all subscriptions
            for topic, subscribers in self.subscriptions.items():
                subscribers.discard(client_id)
            
            # Clean up empty subscriptions
            self.subscriptions = {
                topic: subscribers 
                for topic, subscribers in self.subscriptions.items() 
                if subscribers
            }
            
            del self.active_connections[client_id]
            del self.connection_metadata[client_id]
            
            logger.info(f"WebSocket client disconnected: {client_id}")
    
    async def send_personal_message(self, message: Dict[str, Any], client_id: str):
        """Send message to a specific client"""
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                await websocket.send_text(json.dumps(message, default=str))
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                self.disconnect(client_id)
    
    async def broadcast_to_topic(self, message: Dict[str, Any], topic: str):
        """Broadcast message to all subscribers of a topic"""
        if topic not in self.subscriptions:
            return
            
        disconnected_clients = []
        
        for client_id in self.subscriptions[topic].copy():
            try:
                websocket = self.active_connections[client_id]
                await websocket.send_text(json.dumps(message, default=str))
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """Broadcast message to all connected clients"""
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message, default=str))
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    def subscribe_to_topic(self, client_id: str, topic: str):
        """Subscribe a client to a topic"""
        if client_id not in self.active_connections:
            return False
            
        if topic not in self.subscriptions:
            self.subscriptions[topic] = set()
            
        self.subscriptions[topic].add(client_id)
        self.connection_metadata[client_id]["subscriptions"].add(topic)
        
        logger.info(f"Client {client_id} subscribed to topic: {topic}")
        return True
    
    def unsubscribe_from_topic(self, client_id: str, topic: str):
        """Unsubscribe a client from a topic"""
        if topic in self.subscriptions:
            self.subscriptions[topic].discard(client_id)
            
        if client_id in self.connection_metadata:
            self.connection_metadata[client_id]["subscriptions"].discard(topic)
            
        logger.info(f"Client {client_id} unsubscribed from topic: {topic}")
    
    async def handle_ping(self, client_id: str):
        """Handle ping from client"""
        if client_id in self.connection_metadata:
            self.connection_metadata[client_id]["last_ping"] = datetime.utcnow()
            
        await self.send_personal_message({
            "type": "pong",
            "timestamp": datetime.utcnow().isoformat()
        }, client_id)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "total_connections": len(self.active_connections),
            "total_subscriptions": sum(len(subs) for subs in self.subscriptions.values()),
            "topics": list(self.subscriptions.keys()),
            "connections": [
                {
                    "client_id": client_id,
                    "connected_at": metadata["connected_at"].isoformat(),
                    "last_ping": metadata["last_ping"].isoformat(),
                    "subscriptions": list(metadata["subscriptions"])
                }
                for client_id, metadata in self.connection_metadata.items()
            ]
        }


# Global connection manager instance
manager = ConnectionManager()


class WebSocketHandler:
    """Handles WebSocket message processing"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.manager = connection_manager
        self.logger = get_logger("websocket")
    
    async def handle_message(self, websocket: WebSocket, client_id: str, message: str):
        """Process incoming WebSocket message"""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            if message_type == "ping":
                await self.manager.handle_ping(client_id)
                
            elif message_type == "subscribe":
                topic = data.get("topic")
                if topic:
                    success = self.manager.subscribe_to_topic(client_id, topic)
                    await self.manager.send_personal_message({
                        "type": "subscription_result",
                        "topic": topic,
                        "success": success
                    }, client_id)
                    
            elif message_type == "unsubscribe":
                topic = data.get("topic")
                if topic:
                    self.manager.unsubscribe_from_topic(client_id, topic)
                    await self.manager.send_personal_message({
                        "type": "unsubscription_result",
                        "topic": topic,
                        "success": True
                    }, client_id)
                    
            elif message_type == "get_stats":
                stats = self.manager.get_connection_stats()
                await self.manager.send_personal_message({
                    "type": "stats",
                    "data": stats
                }, client_id)
                
            else:
                await self.manager.send_personal_message({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                }, client_id)
                
        except json.JSONDecodeError:
            await self.manager.send_personal_message({
                "type": "error",
                "message": "Invalid JSON format"
            }, client_id)
        except Exception as e:
            self.logger.error(f"Error handling message from {client_id}: {e}")
            await self.manager.send_personal_message({
                "type": "error",
                "message": "Internal server error"
            }, client_id)


# Global WebSocket handler
ws_handler = WebSocketHandler(manager)


async def websocket_endpoint(websocket: WebSocket, client_id: str = None):
    """Main WebSocket endpoint"""
    client_id = await manager.connect(websocket, client_id)
    
    try:
        while True:
            message = await websocket.receive_text()
            await ws_handler.handle_message(websocket, client_id, message)
            
    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        manager.disconnect(client_id)
