import React from 'react'
import { motion } from 'framer-motion'
import classNames from 'classnames'

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'glass' | 'gradient' | 'neon' | 'minimal'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  hover?: boolean
  glow?: boolean
  border?: boolean
  loading?: boolean
  header?: React.ReactNode
  footer?: React.ReactNode
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  size = 'md',
  hover = true,
  glow = false,
  border = true,
  loading = false,
  header,
  footer,
  className,
  ...props
}) => {
  const baseClasses = [
    'relative overflow-hidden transition-all duration-300',
    'rounded-2xl',
  ]

  const variantClasses = {
    default: [
      'bg-dark-800/80 backdrop-blur-sm',
      border ? 'border border-dark-600/50' : '',
      hover ? 'hover:bg-dark-700/80 hover:border-dark-500/50' : '',
    ],
    glass: [
      'glass',
      hover ? 'hover:bg-white/20' : '',
    ],
    gradient: [
      'bg-gradient-to-br from-dark-800/90 via-dark-700/80 to-dark-800/90',
      'border border-gradient',
      hover ? 'hover:from-dark-700/90 hover:via-dark-600/80 hover:to-dark-700/90' : '',
    ],
    neon: [
      'bg-dark-900/90 backdrop-blur-sm',
      'border border-neon-blue/30',
      'shadow-neon-blue',
      hover ? 'hover:border-neon-blue/50 hover:shadow-neon-blue' : '',
    ],
    minimal: [
      'bg-transparent',
      border ? 'border border-dark-700/30' : '',
      hover ? 'hover:bg-dark-800/20' : '',
    ],
  }

  const sizeClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  }

  const glowClasses = glow ? 'shadow-glow hover:shadow-glow-lg' : ''

  const cardClasses = classNames(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    glowClasses,
    {
      'cursor-pointer': hover,
    },
    className
  )

  const LoadingSkeleton = () => (
    <div className="animate-pulse">
      <div className="h-4 bg-dark-600 rounded w-3/4 mb-4"></div>
      <div className="h-4 bg-dark-600 rounded w-1/2 mb-2"></div>
      <div className="h-4 bg-dark-600 rounded w-5/6"></div>
    </div>
  )

  const cardVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    hover: hover ? { y: -2, scale: 1.01 } : {},
  }

  return (
    <motion.div
      className={cardClasses}
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      {...props}
    >
      {/* Background pattern */}
      <div className="absolute inset-0 bg-noise opacity-5 pointer-events-none" />
      
      {/* Gradient overlay for neon variant */}
      {variant === 'neon' && (
        <div className="absolute inset-0 bg-gradient-to-br from-neon-blue/5 via-transparent to-neon-purple/5 pointer-events-none" />
      )}

      {/* Header */}
      {header && (
        <div className="relative mb-4 pb-4 border-b border-dark-600/50">
          {header}
        </div>
      )}

      {/* Content */}
      <div className="relative">
        {loading ? <LoadingSkeleton /> : children}
      </div>

      {/* Footer */}
      {footer && (
        <div className="relative mt-4 pt-4 border-t border-dark-600/50">
          {footer}
        </div>
      )}

      {/* Hover effect overlay */}
      {hover && (
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
      )}
    </motion.div>
  )
}

// Card sub-components
export const CardHeader: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => (
  <div className={classNames('mb-4', className)}>
    {children}
  </div>
)

export const CardTitle: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => (
  <h3 className={classNames('text-lg font-semibold text-white', className)}>
    {children}
  </h3>
)

export const CardDescription: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => (
  <p className={classNames('text-sm text-gray-400', className)}>
    {children}
  </p>
)

export const CardContent: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => (
  <div className={className}>
    {children}
  </div>
)

export const CardFooter: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => (
  <div className={classNames('mt-4 pt-4 border-t border-dark-600/50', className)}>
    {children}
  </div>
)

export default Card
