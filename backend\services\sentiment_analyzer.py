"""
Sentiment analysis service using public APIs
Analyzes crypto sentiment from Reddit and other public sources
"""
import asyncio
import re
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from backend.services.public_api_clients import reddit_client
from backend.core.logging import get_logger
from backend.core.cache import cache

logger = get_logger(__name__)


class SentimentAnalyzer:
    """Analyzes cryptocurrency sentiment from public sources"""
    
    def __init__(self):
        self.crypto_subreddits = [
            "cryptocurrency", "bitcoin", "ethereum", "defi", "altcoin",
            "cryptomarkets", "cryptomoonshots", "satoshistreetbets"
        ]
        
        # Simple sentiment keywords
        self.positive_keywords = [
            "bullish", "moon", "pump", "buy", "hodl", "diamond hands",
            "to the moon", "rocket", "green", "profit", "gains", "up",
            "surge", "rally", "breakout", "bullrun", "ath", "all time high"
        ]
        
        self.negative_keywords = [
            "bearish", "dump", "sell", "crash", "red", "loss", "down",
            "dip", "correction", "bear market", "panic", "fud", "fear",
            "uncertainty", "doubt", "decline", "drop", "fall"
        ]
    
    async def analyze_token_sentiment(self, token_symbol: str) -> Dict[str, Any]:
        """Analyze sentiment for a specific token"""
        cache_key = f"sentiment:{token_symbol.lower()}"
        cached_result = await cache.get(cache_key)
        if cached_result:
            return cached_result
        
        try:
            # Search for token mentions across crypto subreddits
            all_posts = []
            search_queries = [token_symbol, f"${token_symbol}"]
            
            for query in search_queries:
                for subreddit in self.crypto_subreddits:
                    try:
                        posts = await reddit_client.search_posts(
                            query=query,
                            subreddit=subreddit,
                            limit=10
                        )
                        
                        if posts and "data" in posts and "children" in posts["data"]:
                            all_posts.extend(posts["data"]["children"])
                        
                        # Rate limiting
                        await asyncio.sleep(1)
                        
                    except Exception as e:
                        logger.warning(f"Error searching {subreddit} for {query}: {e}")
                        continue
            
            if not all_posts:
                return self._create_empty_sentiment_result(token_symbol)
            
            # Analyze sentiment from posts
            sentiment_data = await self._analyze_posts_sentiment(all_posts, token_symbol)
            
            # Cache for 30 minutes
            await cache.set(cache_key, sentiment_data, ttl=1800)
            
            return sentiment_data
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment for {token_symbol}: {e}")
            return self._create_empty_sentiment_result(token_symbol)
    
    async def _analyze_posts_sentiment(self, posts: List[Dict], token_symbol: str) -> Dict[str, Any]:
        """Analyze sentiment from Reddit posts"""
        total_posts = len(posts)
        positive_count = 0
        negative_count = 0
        neutral_count = 0
        
        post_scores = []
        comment_counts = []
        upvote_ratios = []
        
        for post_data in posts:
            post = post_data.get("data", {})
            
            # Extract text content
            title = post.get("title", "").lower()
            selftext = post.get("selftext", "").lower()
            content = f"{title} {selftext}"
            
            # Basic sentiment analysis
            sentiment_score = self._calculate_sentiment_score(content)
            
            if sentiment_score > 0.1:
                positive_count += 1
            elif sentiment_score < -0.1:
                negative_count += 1
            else:
                neutral_count += 1
            
            # Collect engagement metrics
            post_scores.append(post.get("score", 0))
            comment_counts.append(post.get("num_comments", 0))
            upvote_ratios.append(post.get("upvote_ratio", 0.5))
        
        # Calculate overall sentiment
        if total_posts > 0:
            positive_ratio = positive_count / total_posts
            negative_ratio = negative_count / total_posts
            neutral_ratio = neutral_count / total_posts
        else:
            positive_ratio = negative_ratio = neutral_ratio = 0
        
        # Calculate engagement metrics
        avg_score = sum(post_scores) / len(post_scores) if post_scores else 0
        avg_comments = sum(comment_counts) / len(comment_counts) if comment_counts else 0
        avg_upvote_ratio = sum(upvote_ratios) / len(upvote_ratios) if upvote_ratios else 0.5
        
        # Overall sentiment score (-1 to 1)
        overall_sentiment = positive_ratio - negative_ratio
        
        # Confidence based on sample size and engagement
        confidence = min(1.0, total_posts / 50) * (avg_upvote_ratio + 0.5) / 1.5
        
        return {
            "token_symbol": token_symbol,
            "overall_sentiment": overall_sentiment,
            "sentiment_label": self._get_sentiment_label(overall_sentiment),
            "confidence": confidence,
            "total_posts": total_posts,
            "positive_posts": positive_count,
            "negative_posts": negative_count,
            "neutral_posts": neutral_count,
            "positive_ratio": positive_ratio,
            "negative_ratio": negative_ratio,
            "neutral_ratio": neutral_ratio,
            "engagement_metrics": {
                "avg_score": avg_score,
                "avg_comments": avg_comments,
                "avg_upvote_ratio": avg_upvote_ratio
            },
            "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
            "data_source": "reddit_public"
        }
    
    def _calculate_sentiment_score(self, text: str) -> float:
        """Calculate sentiment score for text content"""
        positive_matches = 0
        negative_matches = 0
        
        # Count positive keywords
        for keyword in self.positive_keywords:
            positive_matches += len(re.findall(r'\b' + re.escape(keyword) + r'\b', text, re.IGNORECASE))
        
        # Count negative keywords
        for keyword in self.negative_keywords:
            negative_matches += len(re.findall(r'\b' + re.escape(keyword) + r'\b', text, re.IGNORECASE))
        
        # Calculate score
        total_matches = positive_matches + negative_matches
        if total_matches == 0:
            return 0.0
        
        return (positive_matches - negative_matches) / total_matches
    
    def _get_sentiment_label(self, sentiment_score: float) -> str:
        """Convert sentiment score to label"""
        if sentiment_score > 0.2:
            return "bullish"
        elif sentiment_score < -0.2:
            return "bearish"
        else:
            return "neutral"
    
    def _create_empty_sentiment_result(self, token_symbol: str) -> Dict[str, Any]:
        """Create empty sentiment result when no data is available"""
        return {
            "token_symbol": token_symbol,
            "overall_sentiment": 0.0,
            "sentiment_label": "neutral",
            "confidence": 0.0,
            "total_posts": 0,
            "positive_posts": 0,
            "negative_posts": 0,
            "neutral_posts": 0,
            "positive_ratio": 0.0,
            "negative_ratio": 0.0,
            "neutral_ratio": 0.0,
            "engagement_metrics": {
                "avg_score": 0,
                "avg_comments": 0,
                "avg_upvote_ratio": 0.5
            },
            "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
            "data_source": "reddit_public",
            "note": "No data available"
        }
    
    async def get_market_sentiment_overview(self) -> Dict[str, Any]:
        """Get overall crypto market sentiment"""
        cache_key = "market_sentiment_overview"
        cached_result = await cache.get(cache_key)
        if cached_result:
            return cached_result
        
        try:
            # Get posts from main crypto subreddits
            all_posts = []
            
            for subreddit in self.crypto_subreddits[:3]:  # Limit to top 3 subreddits
                try:
                    posts = await reddit_client.get_subreddit_posts(
                        subreddit=subreddit,
                        sort="hot",
                        limit=25
                    )
                    
                    if posts and "data" in posts and "children" in posts["data"]:
                        all_posts.extend(posts["data"]["children"])
                    
                    await asyncio.sleep(1)  # Rate limiting
                    
                except Exception as e:
                    logger.warning(f"Error getting posts from {subreddit}: {e}")
                    continue
            
            if not all_posts:
                return {
                    "overall_sentiment": 0.0,
                    "sentiment_label": "neutral",
                    "confidence": 0.0,
                    "total_posts": 0,
                    "analysis_timestamp": datetime.now(timezone.utc).isoformat()
                }
            
            # Analyze overall sentiment
            sentiment_data = await self._analyze_posts_sentiment(all_posts, "MARKET")
            
            # Cache for 1 hour
            await cache.set(cache_key, sentiment_data, ttl=3600)
            
            return sentiment_data
            
        except Exception as e:
            logger.error(f"Error analyzing market sentiment: {e}")
            return {
                "overall_sentiment": 0.0,
                "sentiment_label": "neutral",
                "confidence": 0.0,
                "total_posts": 0,
                "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
                "error": str(e)
            }


# Global instance
sentiment_analyzer = SentimentAnalyzer()
