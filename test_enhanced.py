"""
Test Enhanced Crypto Arbitrage Bot
"""
from fastapi import FastAPI
from fastapi.responses import HTMLResponse
import uvicorn

app = FastAPI(title="Test Enhanced Bot")

@app.get("/")
async def root():
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Enhanced Crypto Arbitrage Bot v2.0</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white; 
                margin: 0; 
                padding: 40px;
                min-height: 100vh;
            }
            .container { 
                max-width: 1200px; 
                margin: 0 auto; 
                text-align: center;
            }
            .card {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 30px;
                margin: 20px 0;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .btn {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 15px;
                cursor: pointer;
                margin: 10px;
                font-size: 16px;
            }
            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            }
            .opportunity {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 20px;
                margin: 15px 0;
                text-align: left;
            }
            .risk-low { color: #10b981; }
            .risk-medium { color: #f59e0b; }
            .risk-high { color: #ef4444; }
            .profit-positive { color: #a7f3d0; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Enhanced Crypto Arbitrage Bot v2.0</h1>
            <p>Token Categories • Trading Simulation • Enhanced Filtering</p>
            
            <div class="card">
                <h2>🎯 Enhanced Control Center</h2>
                <button class="btn" onclick="scanOpportunities()">🔍 Scan with Simulation</button>
                <button class="btn" onclick="updateSimulation()">💼 Update Simulation</button>
                
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-top: 20px;">
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 12px;">
                        <div style="font-size: 1.5rem; font-weight: bold;" id="opportunities-count">0</div>
                        <div>Peluang Layak</div>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 12px;">
                        <div style="font-size: 1.5rem; font-weight: bold;" id="total-profit">$0</div>
                        <div>Profit Potensial</div>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 12px;">
                        <div style="font-size: 1.5rem; font-weight: bold;" id="avg-slippage">0%</div>
                        <div>Avg Slippage</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h2>💼 Trading Simulation</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                    <div>
                        <label>Modal ($)</label><br>
                        <input type="number" id="simulation-capital" value="100" min="10" max="10000" 
                               style="width: 100%; padding: 10px; border-radius: 8px; border: none; margin-top: 5px;">
                    </div>
                    <div>
                        <label>Max Slippage (%)</label><br>
                        <input type="number" id="max-slippage" value="0.5" min="0.1" max="5" step="0.1"
                               style="width: 100%; padding: 10px; border-radius: 8px; border: none; margin-top: 5px;">
                    </div>
                </div>
                <button class="btn" onclick="updateSimulationConfig()">💾 Update Simulation</button>
            </div>
            
            <div class="card">
                <h2>💰 Enhanced Arbitrage Opportunities</h2>
                <div id="opportunities">
                    <div style="text-align: center; padding: 40px;">
                        <div style="font-size: 3rem; margin-bottom: 20px;">🔍</div>
                        <p>Ready for Enhanced Arbitrage Scanning</p>
                        <p style="opacity: 0.8;">Click "Scan with Simulation" to start analysis</p>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            async function scanOpportunities() {
                document.getElementById('opportunities').innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <div style="font-size: 3rem; margin-bottom: 20px;">🔍</div>
                        <p>Enhanced scanning with simulation filtering...</p>
                    </div>
                `;
                
                try {
                    const response = await fetch('/api/scan');
                    const data = await response.json();
                    
                    document.getElementById('opportunities-count').textContent = data.count || 0;
                    
                    if (data.opportunities && data.opportunities.length > 0) {
                        let html = '';
                        data.opportunities.forEach((opp, index) => {
                            const riskClass = opp.risk_level ? `risk-${opp.risk_level.toLowerCase()}` : 'risk-medium';
                            
                            html += `
                                <div class="opportunity">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                                        <h3>💎 ${opp.token_symbol} Enhanced Opportunity</h3>
                                        <span class="${riskClass}">${opp.risk_level || 'Medium'} Risk</span>
                                    </div>
                                    
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0;">
                                        <div style="background: rgba(248, 113, 113, 0.2); padding: 15px; border-radius: 12px;">
                                            <p><strong>🛒 Buy:</strong> ${opp.buy_exchange}</p>
                                            <p style="font-size: 1.2rem; color: #fca5a5; font-weight: 600;">$${opp.buy_price}</p>
                                        </div>
                                        <div style="background: rgba(52, 211, 153, 0.2); padding: 15px; border-radius: 12px;">
                                            <p><strong>💰 Sell:</strong> ${opp.sell_exchange}</p>
                                            <p style="font-size: 1.2rem; color: #a7f3d0; font-weight: 600;">$${opp.sell_price}</p>
                                        </div>
                                    </div>
                                    
                                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin: 15px 0; padding: 15px; background: rgba(255,255,255,0.05); border-radius: 12px;">
                                        <div style="text-align: center;">
                                            <div style="font-weight: bold; color: #fecfef;">$${opp.estimated_profit_usd || 0}</div>
                                            <div style="font-size: 0.8rem;">Estimated Profit</div>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-weight: bold; color: #fecfef;">${opp.total_slippage || 0}%</div>
                                            <div style="font-size: 0.8rem;">Total Slippage</div>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-weight: bold; color: #fecfef;">${opp.liquidity_ratio || 0}x</div>
                                            <div style="font-size: 0.8rem;">Liquidity Ratio</div>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-weight: bold; color: #fecfef;">$${opp.recommended_order_size || 0}</div>
                                            <div style="font-size: 0.8rem;">Recommended Size</div>
                                        </div>
                                    </div>
                                    
                                    <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 12px;">
                                        <p><strong>📊 Profit:</strong> <span class="profit-positive">${opp.profit_percentage}%</span></p>
                                        <p><strong>💧 Min Liquidity:</strong> $${opp.min_liquidity?.toLocaleString()}</p>
                                        <p><strong>🔗 Blockchain:</strong> ${opp.buy_chain}</p>
                                        <p><strong>⏰ Detected:</strong> ${new Date(opp.timestamp).toLocaleString()}</p>
                                    </div>
                                </div>
                            `;
                        });
                        document.getElementById('opportunities').innerHTML = html;
                        
                        // Update summary
                        const totalProfit = data.opportunities.reduce((sum, opp) => sum + (opp.estimated_profit_usd || 0), 0);
                        const avgSlippage = data.opportunities.length > 0 ? 
                            data.opportunities.reduce((sum, opp) => sum + (opp.total_slippage || 0), 0) / data.opportunities.length : 0;
                        
                        document.getElementById('total-profit').textContent = `$${totalProfit.toFixed(2)}`;
                        document.getElementById('avg-slippage').textContent = `${avgSlippage.toFixed(2)}%`;
                        
                    } else {
                        document.getElementById('opportunities').innerHTML = `
                            <div style="text-align: center; padding: 40px;">
                                <div style="font-size: 3rem; margin-bottom: 20px;">😔</div>
                                <p>No feasible opportunities found</p>
                                <p style="opacity: 0.8;">Try adjusting simulation parameters</p>
                                <button class="btn" onclick="scanOpportunities()" style="margin-top: 20px;">🔄 Scan Again</button>
                            </div>
                        `;
                    }
                    
                } catch (error) {
                    document.getElementById('opportunities').innerHTML = `
                        <div style="text-align: center; padding: 40px;">
                            <div style="font-size: 3rem; margin-bottom: 20px;">❌</div>
                            <p>Scan Error: ${error.message}</p>
                            <button class="btn" onclick="scanOpportunities()" style="margin-top: 20px;">🔄 Try Again</button>
                        </div>
                    `;
                }
            }
            
            async function updateSimulationConfig() {
                const capital = parseFloat(document.getElementById('simulation-capital').value);
                const maxSlippage = parseFloat(document.getElementById('max-slippage').value);
                
                try {
                    const response = await fetch('/api/config', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            simulation_capital: capital,
                            max_slippage: maxSlippage
                        })
                    });
                    
                    if (response.ok) {
                        alert('✅ Simulation configuration updated!');
                    }
                } catch (error) {
                    alert('❌ Error updating configuration');
                }
            }
            
            async function updateSimulation() {
                try {
                    const response = await fetch('/api/simulation-summary');
                    const data = await response.json();
                    
                    if (data.summary) {
                        const summary = data.summary;
                        alert(`💼 Simulation Summary:\\n` +
                              `💰 Total Capital: $${summary.total_capital}\\n` +
                              `🎯 Feasible Opportunities: ${summary.feasible_opportunities}\\n` +
                              `📈 Total Potential Profit: $${summary.total_potential_profit}\\n` +
                              `📉 Average Slippage: ${summary.average_slippage}%`);
                    }
                } catch (error) {
                    alert('❌ Error fetching simulation summary');
                }
            }
        </script>
    </body>
    </html>
    """)

@app.get("/api/scan")
async def scan():
    # Mock enhanced opportunities with simulation data
    opportunities = [
        {
            "token_symbol": "USDC",
            "buy_exchange": "ethereum_uniswap",
            "sell_exchange": "ethereum_sushiswap",
            "buy_price": 1.0000,
            "sell_price": 1.0025,
            "profit_percentage": 0.25,
            "min_liquidity": 150000,
            "estimated_profit_usd": 0.25,
            "total_slippage": 0.15,
            "liquidity_ratio": 15.0,
            "risk_level": "Low",
            "recommended_order_size": 95,
            "buy_chain": "ethereum",
            "timestamp": "2024-01-01T12:00:00Z"
        },
        {
            "token_symbol": "WETH",
            "buy_exchange": "polygon_quickswap",
            "sell_exchange": "polygon_sushiswap",
            "buy_price": 2450.50,
            "sell_price": 2455.75,
            "profit_percentage": 0.21,
            "min_liquidity": 200000,
            "estimated_profit_usd": 0.21,
            "total_slippage": 0.25,
            "liquidity_ratio": 20.0,
            "risk_level": "Low",
            "recommended_order_size": 98,
            "buy_chain": "polygon",
            "timestamp": "2024-01-01T12:01:00Z"
        },
        {
            "token_symbol": "UNI",
            "buy_exchange": "arbitrum_uniswap",
            "sell_exchange": "arbitrum_sushiswap",
            "buy_price": 8.45,
            "sell_price": 8.52,
            "profit_percentage": 0.83,
            "min_liquidity": 80000,
            "estimated_profit_usd": 0.83,
            "total_slippage": 0.45,
            "liquidity_ratio": 8.0,
            "risk_level": "Medium",
            "recommended_order_size": 75,
            "buy_chain": "arbitrum",
            "timestamp": "2024-01-01T12:02:00Z"
        }
    ]
    
    return {
        "status": "success",
        "opportunities": opportunities,
        "count": len(opportunities)
    }

@app.post("/api/config")
async def update_config(config: dict):
    return {"status": "success", "message": "Configuration updated"}

@app.get("/api/simulation-summary")
async def simulation_summary():
    return {
        "status": "success",
        "summary": {
            "total_capital": 100,
            "feasible_opportunities": 3,
            "total_potential_profit": 1.29,
            "average_slippage": 0.28,
            "risk_distribution": {"Low": 2, "Medium": 1, "High": 0}
        }
    }

if __name__ == "__main__":
    print("🚀 Starting Test Enhanced Crypto Arbitrage Bot")
    print("🌐 Web interface: http://localhost:8002")
    uvicorn.run(app, host="0.0.0.0", port=8002)
