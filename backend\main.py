"""
Main FastAPI application for Crypto Arbitrage Bot
"""
from fastapi import FastAPI, WebSocket, Depends, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import HTMLResponse
from contextlib import asynccontextmanager
import uvicorn

# Core imports
from backend.config.settings import settings
from backend.core.database import init_db, close_db
from backend.core.cache import cache
from backend.core.logging import configure_logging, get_logger
from backend.core.websocket import websocket_endpoint, manager

# API routes (will be created next)
# from backend.api.routes import arbitrage, tokens, admin

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Crypto Arbitrage Bot...")
    
    # Configure logging
    configure_logging()
    
    # Initialize database
    await init_db()
    
    # Connect to Redis
    await cache.connect()
    
    logger.info("Application startup complete")
    
    yield
    
    # Shutdown
    logger.info("Shutting down application...")
    
    # Close database connections
    await close_db()
    
    # Disconnect from Redis
    await cache.disconnect()
    
    logger.info("Application shutdown complete")


# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="Advanced Crypto Arbitrage Detection Bot with ML and Multi-Chain Support",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)


# WebSocket endpoint
@app.websocket("/ws/{client_id}")
async def websocket_route(websocket: WebSocket, client_id: str = None):
    """WebSocket endpoint for real-time updates"""
    await websocket_endpoint(websocket, client_id)


@app.websocket("/ws")
async def websocket_route_no_id(websocket: WebSocket):
    """WebSocket endpoint without client ID"""
    await websocket_endpoint(websocket)


# Health check endpoints
@app.get("/health")
async def health_check():
    """Basic health check"""
    return {
        "status": "healthy",
        "version": settings.APP_VERSION,
        "timestamp": "2024-01-01T00:00:00Z"  # Will be replaced with actual timestamp
    }


@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with dependencies"""
    health_status = {
        "status": "healthy",
        "version": settings.APP_VERSION,
        "components": {}
    }
    
    # Check database
    try:
        # Add database health check here
        health_status["components"]["database"] = "healthy"
    except Exception as e:
        health_status["components"]["database"] = f"unhealthy: {str(e)}"
        health_status["status"] = "unhealthy"
    
    # Check Redis
    try:
        if cache.redis:
            await cache.redis.ping()
            health_status["components"]["cache"] = "healthy"
        else:
            health_status["components"]["cache"] = "not connected"
    except Exception as e:
        health_status["components"]["cache"] = f"unhealthy: {str(e)}"
        health_status["status"] = "unhealthy"
    
    # Check WebSocket connections
    ws_stats = manager.get_connection_stats()
    health_status["components"]["websocket"] = {
        "status": "healthy",
        "active_connections": ws_stats["total_connections"]
    }
    
    return health_status


# API status endpoint
@app.get("/api/status")
async def api_status():
    """Get current API status and statistics"""
    return {
        "status": "operational",
        "version": settings.APP_VERSION,
        "features": {
            "multi_api_integration": True,
            "machine_learning": True,
            "cross_chain_support": True,
            "real_time_updates": True,
            "advanced_validation": True
        },
        "supported_networks": settings.SUPPORTED_NETWORKS,
        "websocket_connections": manager.get_connection_stats()["total_connections"]
    }


# Root endpoint - serve frontend
@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main frontend application"""
    # This will be replaced with actual frontend serving
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Crypto Arbitrage Bot v2.0</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                font-family: 'Inter', sans-serif;
                background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
                color: #e0e0e0;
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                text-align: center;
                max-width: 600px;
            }
            h1 {
                font-size: 3rem;
                margin-bottom: 1rem;
                background: linear-gradient(45deg, #8b5cf6, #06b6d4);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .status {
                background: rgba(139, 92, 246, 0.1);
                border: 1px solid rgba(139, 92, 246, 0.3);
                border-radius: 12px;
                padding: 20px;
                margin: 20px 0;
            }
            .feature {
                display: inline-block;
                background: rgba(6, 182, 212, 0.1);
                border: 1px solid rgba(6, 182, 212, 0.3);
                border-radius: 8px;
                padding: 8px 16px;
                margin: 4px;
                font-size: 0.9rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Crypto Arbitrage Bot v2.0</h1>
            <div class="status">
                <h3>✅ Backend Infrastructure Ready</h3>
                <p>Advanced arbitrage detection system with ML capabilities</p>
            </div>
            <div>
                <div class="feature">Multi-API Integration</div>
                <div class="feature">Machine Learning</div>
                <div class="feature">Cross-Chain Support</div>
                <div class="feature">Real-time WebSocket</div>
                <div class="feature">Advanced Validation</div>
                <div class="feature">Risk Assessment</div>
            </div>
            <p style="margin-top: 30px; opacity: 0.7;">
                Frontend interface coming next...<br>
                API Documentation: <a href="/docs" style="color: #8b5cf6;">/docs</a>
            </p>
        </div>
    </body>
    </html>
    """


# Include API routers (will be added next)
# app.include_router(arbitrage.router, prefix="/api/v1/arbitrage", tags=["arbitrage"])
# app.include_router(tokens.router, prefix="/api/v1/tokens", tags=["tokens"])
# app.include_router(admin.router, prefix="/api/v1/admin", tags=["admin"])


if __name__ == "__main__":
    uvicorn.run(
        "backend.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD,
        log_level=settings.LOG_LEVEL.lower()
    )
