@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0f0f23;
  color: #ffffff;
  overflow-x: hidden;
}

code {
  font-family: 'JetBrains Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 15, 35, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.5);
}

::-webkit-scrollbar-corner {
  background: rgba(15, 15, 35, 0.5);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.3) rgba(15, 15, 35, 0.5);
}

/* Selection styles */
::selection {
  background: rgba(139, 92, 246, 0.3);
  color: #ffffff;
}

::-moz-selection {
  background: rgba(139, 92, 246, 0.3);
  color: #ffffff;
}

/* Focus styles */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid rgba(139, 92, 246, 0.5);
  outline-offset: 2px;
}

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.8), 0 0 30px rgba(139, 92, 246, 0.4);
  }
}

/* Utility classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-gradient-shift {
  animation: gradientShift 3s ease infinite;
  background-size: 200% 200%;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-effect-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient text */
.text-gradient-cyber {
  background: linear-gradient(135deg, #8b5cf6, #06b6d4, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* Neon effects */
.neon-glow-blue {
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5), 0 0 20px rgba(0, 212, 255, 0.3);
}

.neon-glow-purple {
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3);
}

.neon-glow-green {
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5), 0 0 20px rgba(16, 185, 129, 0.3);
}

/* Loading animations */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* Chart container styles */
.chart-container {
  position: relative;
  height: 100%;
  width: 100%;
}

.chart-container canvas {
  max-height: 100% !important;
}

/* React Hot Toast custom styles */
.react-hot-toast {
  background: rgba(15, 15, 35, 0.95) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(10px) !important;
}

/* Custom form styles */
.form-input {
  @apply w-full px-4 py-3 bg-dark-800/50 border border-dark-600/50 rounded-xl text-white placeholder-gray-400 focus:border-neon-blue focus:ring-2 focus:ring-neon-blue/20 focus:outline-none transition-all duration-200;
}

.form-select {
  @apply w-full px-4 py-3 bg-dark-800/50 border border-dark-600/50 rounded-xl text-white focus:border-neon-blue focus:ring-2 focus:ring-neon-blue/20 focus:outline-none transition-all duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-300 mb-2;
}

/* Table styles */
.table-container {
  @apply overflow-x-auto;
}

.table {
  @apply min-w-full divide-y divide-dark-600/50;
}

.table-header {
  @apply bg-dark-800/50;
}

.table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider;
}

.table-body {
  @apply bg-dark-900/50 divide-y divide-dark-600/30;
}

.table-row {
  @apply hover:bg-dark-800/30 transition-colors duration-150;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-300;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .mobile-hide {
    display: none;
  }
  
  .mobile-full {
    width: 100%;
  }
  
  .mobile-text-sm {
    font-size: 0.875rem;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .glass-effect,
  .glass-effect-dark {
    background: white !important;
    backdrop-filter: none !important;
    border: 1px solid #ccc !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .glass-effect,
  .glass-effect-dark {
    border-width: 2px;
  }
  
  .text-gradient,
  .text-gradient-cyber {
    -webkit-text-fill-color: unset;
    background: none;
    color: #ffffff;
  }
}
