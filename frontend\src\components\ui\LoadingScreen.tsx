import React from 'react'
import { motion } from 'framer-motion'

const LoadingScreen: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  const pulseVariants = {
    pulse: {
      scale: [1, 1.2, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  const orbitVariants = {
    orbit: {
      rotate: 360,
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "linear"
      }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-tertiary flex items-center justify-center">
      {/* Background Effects */}
      <div className="fixed inset-0 bg-noise opacity-5 pointer-events-none" />
      <div className="fixed inset-0 bg-gradient-to-br from-neon-purple/5 via-transparent to-neon-blue/5 pointer-events-none" />
      
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="text-center"
      >
        {/* Logo and Spinner */}
        <motion.div
          variants={itemVariants}
          className="relative mb-8"
        >
          {/* Outer orbit ring */}
          <motion.div
            variants={orbitVariants}
            animate="orbit"
            className="w-32 h-32 mx-auto relative"
          >
            <div className="absolute inset-0 border-2 border-neon-blue/30 rounded-full" />
            <div className="absolute top-0 left-1/2 w-3 h-3 bg-neon-blue rounded-full transform -translate-x-1/2 -translate-y-1/2 shadow-neon-blue" />
          </motion.div>

          {/* Inner pulsing core */}
          <motion.div
            variants={pulseVariants}
            animate="pulse"
            className="absolute inset-0 flex items-center justify-center"
          >
            <div className="w-16 h-16 bg-gradient-cyber rounded-full shadow-glow-lg flex items-center justify-center">
              <div className="w-8 h-8 bg-white rounded-full opacity-90" />
            </div>
          </motion.div>
        </motion.div>

        {/* Title */}
        <motion.div variants={itemVariants} className="mb-6">
          <h1 className="text-4xl font-bold text-gradient mb-2">
            ArbiBot Pro
          </h1>
          <p className="text-lg text-gray-300">
            Advanced Crypto Arbitrage Platform
          </p>
        </motion.div>

        {/* Loading text */}
        <motion.div variants={itemVariants} className="mb-8">
          <div className="flex items-center justify-center space-x-2">
            <div className="flex space-x-1">
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-2 h-2 bg-neon-blue rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2,
                  }}
                />
              ))}
            </div>
            <span className="text-gray-400 ml-3">Initializing systems...</span>
          </div>
        </motion.div>

        {/* Features loading */}
        <motion.div variants={itemVariants} className="space-y-3">
          {[
            'Loading ML models...',
            'Connecting to exchanges...',
            'Initializing real-time data...',
            'Setting up arbitrage engine...'
          ].map((text, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.5 + 1 }}
              className="flex items-center justify-center space-x-3"
            >
              <motion.div
                className="w-2 h-2 bg-success rounded-full"
                animate={{
                  scale: [0, 1, 1],
                  opacity: [0, 1, 1],
                }}
                transition={{ delay: index * 0.5 + 1.2 }}
              />
              <span className="text-sm text-gray-500">{text}</span>
            </motion.div>
          ))}
        </motion.div>

        {/* Progress bar */}
        <motion.div
          variants={itemVariants}
          className="mt-8 w-64 mx-auto"
        >
          <div className="h-1 bg-dark-700 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-neon-purple to-neon-blue"
              initial={{ width: 0 }}
              animate={{ width: '100%' }}
              transition={{ duration: 4, ease: "easeInOut" }}
            />
          </div>
        </motion.div>

        {/* Version info */}
        <motion.div
          variants={itemVariants}
          className="mt-8 text-xs text-gray-600"
        >
          <p>Version 2.0.0 - Advanced ML Edition</p>
          <p className="mt-1">© 2024 ArbiBot Technologies</p>
        </motion.div>
      </motion.div>
    </div>
  )
}

export default LoadingScreen
