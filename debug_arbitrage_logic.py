#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import main_fixed

async def debug_arbitrage_logic():
    """Debug the arbitrage detection logic step by step"""
    
    print("🔍 DEBUGGING ARBITRAGE LOGIC STEP BY STEP")
    print("=" * 70)
    
    # Create detector instance
    detector = main_fixed.AdvancedArbitrageDetector()
    
    # Mock data with clear arbitrage opportunity
    mock_pairs = [
        {
            'baseToken': {'symbol': 'USDC', 'address': '0x123'},
            'quoteToken': {'symbol': 'WETH', 'address': '0x456'},
            'chainId': 'ethereum',
            'dexId': 'uniswap_v3',
            'priceUsd': '0.999',  # $0.999
            'liquidity': {'usd': 100000},  # $100K liquidity
            'volume': {'h24': 150000},     # $150K volume (above $100K requirement)
            'pairAddress': '0xabc123'
        },
        {
            'baseToken': {'symbol': 'USDC', 'address': '0x123'},
            'quoteToken': {'symbol': 'WETH', 'address': '0x456'},
            'chainId': 'ethereum',
            'dexId': 'sushiswap',
            'priceUsd': '1.002',  # $1.002 (0.30% higher)
            'liquidity': {'usd': 80000},   # $80K liquidity (above $50K requirement)
            'volume': {'h24': 120000},     # $120K volume (above $100K requirement)
            'pairAddress': '0xdef456'
        }
    ]
    
    print("📊 Mock Data:")
    for pair in mock_pairs:
        symbol = pair['baseToken']['symbol']
        dex = pair['dexId']
        price = pair['priceUsd']
        liquidity = pair['liquidity']['usd']
        volume = pair['volume']['h24']
        print(f"   {symbol} on {dex}: ${price} (${liquidity:,} liq, ${volume:,} vol)")
    
    print(f"\n🎯 Expected Arbitrage:")
    price1 = float(mock_pairs[0]['priceUsd'])
    price2 = float(mock_pairs[1]['priceUsd'])
    expected_profit = ((price2 - price1) / price1) * 100
    print(f"   Buy at ${price1} → Sell at ${price2} = {expected_profit:.2f}% profit")
    
    # Step 1: Test tier requirements
    print(f"\n📋 Step 1: Testing Tier Requirements")
    tier = 1
    min_liquidity = detector._get_tier_min_liquidity(tier)
    min_volume = detector._get_tier_min_volume(tier)
    
    print(f"   Tier {tier} Requirements:")
    print(f"      Min Liquidity: ${min_liquidity:,}")
    print(f"      Min Volume: ${min_volume:,}")
    
    for i, pair in enumerate(mock_pairs):
        liquidity = pair['liquidity']['usd']
        volume = pair['volume']['h24']
        liq_pass = liquidity > min_liquidity
        vol_pass = volume > min_volume
        
        print(f"   Pair {i+1} ({pair['dexId']}):")
        print(f"      Liquidity: ${liquidity:,} {'✅' if liq_pass else '❌'}")
        print(f"      Volume: ${volume:,} {'✅' if vol_pass else '❌'}")
    
    # Step 2: Test profit threshold
    print(f"\n📈 Step 2: Testing Profit Thresholds")
    usdc_threshold = detector.profit_threshold_manager.get_profit_threshold('USDC', 'stablecoins')
    print(f"   USDC Threshold: {usdc_threshold['min']:.1f}% - {usdc_threshold['max']:.1f}%")
    print(f"   Expected Profit: {expected_profit:.2f}%")
    
    threshold_pass = usdc_threshold['min'] <= expected_profit <= usdc_threshold['max']
    print(f"   Threshold Check: {'✅' if threshold_pass else '❌'}")
    
    # Step 3: Manual arbitrage detection
    print(f"\n🔍 Step 3: Manual Arbitrage Detection")
    
    try:
        # Simulate the arbitrage detection logic manually
        token_symbol = 'USDC'
        chain = 'ethereum'
        
        # Group pairs by token
        token_pairs = {}
        key = f"{token_symbol}_{chain}"
        token_pairs[key] = mock_pairs
        
        print(f"   Token pairs grouped: {len(token_pairs)} groups")
        
        # Process pairs for arbitrage
        opportunities = []
        
        for key, pairs in token_pairs.items():
            if len(pairs) >= 2:
                token_symbol, chain = key.split('_', 1)
                
                print(f"   Processing {token_symbol} on {chain} ({len(pairs)} pairs)")
                
                # Get profit threshold
                token_category = detector._get_token_category(token_symbol)
                profit_threshold = detector.profit_threshold_manager.get_profit_threshold(token_symbol, token_category)
                
                print(f"   Token category: {token_category}")
                print(f"   Profit threshold: {profit_threshold}")
                
                # Find arbitrage
                chain_opportunities = await detector._find_arbitrage_in_pairs_v3(
                    pairs, token_symbol, chain, profit_threshold, tier
                )
                
                print(f"   Opportunities found: {len(chain_opportunities)}")
                opportunities.extend(chain_opportunities)
        
        print(f"\n✅ Manual Detection Complete")
        print(f"🎯 Total Opportunities: {len(opportunities)}")
        
        if opportunities:
            for i, opp in enumerate(opportunities):
                print(f"   {i+1}. {opp['token_symbol']}: {opp['profit_percentage']:.2f}% profit")
                print(f"      {opp['buy_exchange']} → {opp['sell_exchange']}")
        else:
            print("   ❌ No opportunities found - investigating further...")
            
            # Debug the _find_arbitrage_in_pairs_v3 method
            print(f"\n🔍 Step 4: Deep Debug of _find_arbitrage_in_pairs_v3")
            
            # Manually simulate the method
            dex_prices = {}
            
            for pair in mock_pairs:
                dex_name = pair.get('dexId', 'unknown')
                price_usd = float(pair.get('priceUsd', 0))
                liquidity_usd = pair.get('liquidity', {}).get('usd', 0)
                volume_24h = pair.get('volume', {}).get('h24', 0)
                
                print(f"   Processing {dex_name}:")
                print(f"      Price: ${price_usd}")
                print(f"      Liquidity: ${liquidity_usd:,}")
                print(f"      Volume: ${volume_24h:,}")
                
                # Check tier requirements
                min_liq = detector._get_tier_min_liquidity(tier)
                min_vol = detector._get_tier_min_volume(tier)
                
                liq_check = liquidity_usd > min_liq
                vol_check = volume_24h > min_vol
                price_check = price_usd > 0
                
                print(f"      Price > 0: {'✅' if price_check else '❌'}")
                print(f"      Liquidity > ${min_liq:,}: {'✅' if liq_check else '❌'}")
                print(f"      Volume > ${min_vol:,}: {'✅' if vol_check else '❌'}")
                
                if price_check and liq_check and vol_check:
                    dex_prices[dex_name] = {
                        'price': price_usd,
                        'liquidity': liquidity_usd,
                        'volume_24h': volume_24h,
                        'pair_address': pair.get('pairAddress', ''),
                        'pair_data': pair
                    }
                    print(f"      ✅ Added to dex_prices")
                else:
                    print(f"      ❌ Filtered out")
            
            print(f"\n   Valid DEX prices: {len(dex_prices)}")
            
            if len(dex_prices) >= 2:
                print(f"   Checking arbitrage between DEXs...")
                
                dex_list = list(dex_prices.items())
                for i, (dex1_id, dex1_data) in enumerate(dex_list):
                    for j, (dex2_id, dex2_data) in enumerate(dex_list[i+1:], i+1):
                        
                        price1 = dex1_data["price"]
                        price2 = dex2_data["price"]
                        
                        print(f"   Comparing {dex1_id} (${price1}) vs {dex2_id} (${price2})")
                        
                        if price1 <= 0 or price2 <= 0:
                            print(f"      ❌ Invalid prices")
                            continue
                        
                        # Calculate profit
                        if price2 > price1:
                            profit_pct = ((price2 - price1) / price1) * 100
                            direction = f"{dex1_id} → {dex2_id}"
                        else:
                            profit_pct = ((price1 - price2) / price2) * 100
                            direction = f"{dex2_id} → {dex1_id}"
                        
                        print(f"      Profit: {profit_pct:.2f}% ({direction})")
                        
                        # Check thresholds
                        min_profit = profit_threshold.get('min', 5.0)
                        max_profit = profit_threshold.get('max', 50.0)
                        
                        print(f"      Threshold: {min_profit:.1f}% - {max_profit:.1f}%")
                        
                        if min_profit < profit_pct <= max_profit:
                            print(f"      ✅ Meets profit threshold!")
                        else:
                            print(f"      ❌ Does not meet profit threshold")
            else:
                print(f"   ❌ Not enough valid DEX prices for arbitrage")
        
    except Exception as e:
        print(f"❌ Error in manual detection: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "=" * 70)
    print("🎯 ARBITRAGE LOGIC DEBUG COMPLETE")

if __name__ == "__main__":
    asyncio.run(debug_arbitrage_logic())
