from fastapi import FastAPI
from fastapi.responses import HTMLResponse
import uvicorn

app = FastAPI()

@app.get("/")
async def root():
    return HTMLResponse("""
    <html>
    <head><title>Enhanced Arbitrage Bot</title></head>
    <body style="font-family: Arial; background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 40px;">
        <h1>🚀 Enhanced Crypto Arbitrage Bot v2.0</h1>
        <p>Token Categories • Trading Simulation • Enhanced Filtering</p>
        
        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 20px; margin: 20px 0;">
            <h2>🎯 Enhanced Control Center</h2>
            <button onclick="scan()" style="background: linear-gradient(135deg, #ff9a9e, #fecfef); color: white; border: none; padding: 12px 24px; border-radius: 15px; cursor: pointer; margin: 10px;">
                🔍 Scan with Simulation
            </button>
            
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-top: 20px;">
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 12px; text-align: center;">
                    <div style="font-size: 1.5rem; font-weight: bold;" id="count">0</div>
                    <div>Peluang Layak</div>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 12px; text-align: center;">
                    <div style="font-size: 1.5rem; font-weight: bold;" id="profit">$0</div>
                    <div>Profit Potensial</div>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 12px; text-align: center;">
                    <div style="font-size: 1.5rem; font-weight: bold;" id="slippage">0%</div>
                    <div>Avg Slippage</div>
                </div>
            </div>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 20px; margin: 20px 0;">
            <h2>💼 Trading Simulation</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <label>Modal ($)</label><br>
                    <input type="number" id="capital" value="100" style="width: 100%; padding: 10px; border-radius: 8px; border: none; margin-top: 5px;">
                </div>
                <div>
                    <label>Max Slippage (%)</label><br>
                    <input type="number" id="max-slip" value="0.5" step="0.1" style="width: 100%; padding: 10px; border-radius: 8px; border: none; margin-top: 5px;">
                </div>
            </div>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 20px; margin: 20px 0;">
            <h2>💰 Enhanced Opportunities</h2>
            <div id="results">
                <div style="text-align: center; padding: 40px;">
                    <div style="font-size: 3rem;">🔍</div>
                    <p>Ready for Enhanced Arbitrage Scanning</p>
                </div>
            </div>
        </div>
        
        <script>
            async function scan() {
                document.getElementById('results').innerHTML = '<div style="text-align: center; padding: 40px;"><div style="font-size: 3rem;">🔍</div><p>Scanning...</p></div>';
                
                try {
                    const response = await fetch('/api/scan');
                    const data = await response.json();
                    
                    document.getElementById('count').textContent = data.count;
                    
                    let html = '';
                    data.opportunities.forEach(opp => {
                        html += `
                            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; margin: 15px 0;">
                                <h3>💎 ${opp.token_symbol} Enhanced Opportunity</h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0;">
                                    <div style="background: rgba(248,113,113,0.2); padding: 15px; border-radius: 12px;">
                                        <p><strong>🛒 Buy:</strong> ${opp.buy_exchange}</p>
                                        <p style="font-size: 1.2rem; color: #fca5a5;">$${opp.buy_price}</p>
                                    </div>
                                    <div style="background: rgba(52,211,153,0.2); padding: 15px; border-radius: 12px;">
                                        <p><strong>💰 Sell:</strong> ${opp.sell_exchange}</p>
                                        <p style="font-size: 1.2rem; color: #a7f3d0;">$${opp.sell_price}</p>
                                    </div>
                                </div>
                                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; background: rgba(255,255,255,0.05); padding: 15px; border-radius: 12px;">
                                    <div style="text-align: center;">
                                        <div style="font-weight: bold;">$${opp.estimated_profit_usd}</div>
                                        <div style="font-size: 0.8rem;">Estimated Profit</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="font-weight: bold;">${opp.total_slippage}%</div>
                                        <div style="font-size: 0.8rem;">Total Slippage</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="font-weight: bold;">${opp.liquidity_ratio}x</div>
                                        <div style="font-size: 0.8rem;">Liquidity Ratio</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="font-weight: bold; color: ${opp.risk_level === 'Low' ? '#10b981' : opp.risk_level === 'Medium' ? '#f59e0b' : '#ef4444'}">${opp.risk_level}</div>
                                        <div style="font-size: 0.8rem;">Risk Level</div>
                                    </div>
                                </div>
                                <p><strong>📊 Profit:</strong> <span style="color: #a7f3d0; font-weight: bold;">${opp.profit_percentage}%</span></p>
                                <p><strong>💧 Liquidity:</strong> $${opp.min_liquidity.toLocaleString()}</p>
                            </div>
                        `;
                    });
                    
                    document.getElementById('results').innerHTML = html;
                    
                    // Update summary
                    const totalProfit = data.opportunities.reduce((sum, opp) => sum + opp.estimated_profit_usd, 0);
                    const avgSlippage = data.opportunities.reduce((sum, opp) => sum + opp.total_slippage, 0) / data.opportunities.length;
                    
                    document.getElementById('profit').textContent = `$${totalProfit.toFixed(2)}`;
                    document.getElementById('slippage').textContent = `${avgSlippage.toFixed(2)}%`;
                    
                } catch (error) {
                    document.getElementById('results').innerHTML = '<div style="text-align: center; padding: 40px;"><div style="font-size: 3rem;">❌</div><p>Error: ' + error.message + '</p></div>';
                }
            }
        </script>
    </body>
    </html>
    """)

@app.get("/api/scan")
async def scan():
    return {
        "status": "success",
        "count": 3,
        "opportunities": [
            {
                "token_symbol": "USDC",
                "buy_exchange": "ethereum_uniswap",
                "sell_exchange": "ethereum_sushiswap",
                "buy_price": 1.0000,
                "sell_price": 1.0025,
                "profit_percentage": 0.25,
                "min_liquidity": 150000,
                "estimated_profit_usd": 0.25,
                "total_slippage": 0.15,
                "liquidity_ratio": 15.0,
                "risk_level": "Low"
            },
            {
                "token_symbol": "WETH",
                "buy_exchange": "polygon_quickswap",
                "sell_exchange": "polygon_sushiswap",
                "buy_price": 2450.50,
                "sell_price": 2455.75,
                "profit_percentage": 0.21,
                "min_liquidity": 200000,
                "estimated_profit_usd": 0.21,
                "total_slippage": 0.25,
                "liquidity_ratio": 20.0,
                "risk_level": "Low"
            },
            {
                "token_symbol": "UNI",
                "buy_exchange": "arbitrum_uniswap",
                "sell_exchange": "arbitrum_sushiswap",
                "buy_price": 8.45,
                "sell_price": 8.52,
                "profit_percentage": 0.83,
                "min_liquidity": 80000,
                "estimated_profit_usd": 0.83,
                "total_slippage": 0.45,
                "liquidity_ratio": 8.0,
                "risk_level": "Medium"
            }
        ]
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8003)
