import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { ArbitrageOpportunity, ArbitrageEngineStatus, ScanConfig, DashboardStats } from '@types/index'
import { arbitrageApi } from '@api/arbitrage'
import toast from 'react-hot-toast'

interface ArbitrageState {
  // State
  opportunities: ArbitrageOpportunity[]
  engineStatus: ArbitrageEngineStatus
  scanConfig: ScanConfig
  isScanning: boolean
  lastScanTime: string | null
  stats: DashboardStats
  selectedOpportunity: ArbitrageOpportunity | null
  
  // Loading states
  isLoading: boolean
  isStartingScan: boolean
  isStoppingScan: boolean
  
  // Error states
  error: string | null
  
  // Actions
  setOpportunities: (opportunities: ArbitrageOpportunity[]) => void
  addOpportunity: (opportunity: ArbitrageOpportunity) => void
  updateOpportunity: (id: string, updates: Partial<ArbitrageOpportunity>) => void
  removeOpportunity: (id: string) => void
  setEngineStatus: (status: ArbitrageEngineStatus) => void
  setScanConfig: (config: ScanConfig) => void
  setIsScanning: (isScanning: boolean) => void
  setStats: (stats: DashboardStats) => void
  setSelectedOpportunity: (opportunity: ArbitrageOpportunity | null) => void
  setError: (error: string | null) => void
  
  // Async actions
  startScan: () => Promise<void>
  stopScan: () => Promise<void>
  refreshOpportunities: () => Promise<void>
  validateOpportunity: (opportunityId: string) => Promise<void>
  fetchEngineStatus: () => Promise<void>
  fetchSupportedChains: () => Promise<string[]>
}

const defaultScanConfig: ScanConfig = {
  networks: ['ethereum', 'bsc', 'polygon'],
  min_profit_percentage: 0.5,
  max_profit_percentage: 50.0,
  min_liquidity_usd: 10000,
  min_volume_24h: 50000,
  max_slippage_percentage: 2.0,
  include_cex_arbitrage: true,
  include_cross_chain: false,
  max_execution_time_minutes: 60,
  amount_usd: 1000,
}

const defaultStats: DashboardStats = {
  total_opportunities: 0,
  avg_profit_percentage: 0,
  total_volume_analyzed: 0,
  active_chains: 0,
  scan_sessions_today: 0,
  best_opportunity_profit: 0,
}

const defaultEngineStatus: ArbitrageEngineStatus = {
  is_running: false,
  session_id: null,
  opportunities_count: 0,
  supported_networks: [],
  websocket_connections: 0,
}

export const useArbitrageStore = create<ArbitrageState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    opportunities: [],
    engineStatus: defaultEngineStatus,
    scanConfig: defaultScanConfig,
    isScanning: false,
    lastScanTime: null,
    stats: defaultStats,
    selectedOpportunity: null,
    isLoading: false,
    isStartingScan: false,
    isStoppingScan: false,
    error: null,

    // Setters
    setOpportunities: (opportunities) => {
      set({ opportunities })
      
      // Update stats based on opportunities
      const stats: DashboardStats = {
        total_opportunities: opportunities.length,
        avg_profit_percentage: opportunities.length > 0 
          ? opportunities.reduce((sum, opp) => sum + opp.profit_percentage, 0) / opportunities.length
          : 0,
        total_volume_analyzed: opportunities.reduce((sum, opp) => {
          const minLiquidity = Math.min(opp.buy_liquidity_usd || 0, opp.sell_liquidity_usd || 0)
          return sum + minLiquidity
        }, 0),
        active_chains: new Set(opportunities.map(opp => opp.chain_id)).size,
        scan_sessions_today: get().stats.scan_sessions_today, // Keep existing value
        best_opportunity_profit: opportunities.length > 0 
          ? Math.max(...opportunities.map(opp => opp.profit_percentage))
          : 0,
      }
      
      set({ stats })
    },

    addOpportunity: (opportunity) => {
      set((state) => ({
        opportunities: [opportunity, ...state.opportunities]
      }))
      
      // Show notification for high-profit opportunities
      if (opportunity.profit_percentage > 5) {
        toast.success(
          `High profit opportunity found: ${opportunity.token_symbol} (${opportunity.profit_percentage.toFixed(2)}%)`,
          {
            duration: 5000,
            icon: '🚀',
          }
        )
      }
    },

    updateOpportunity: (id, updates) => {
      set((state) => ({
        opportunities: state.opportunities.map(opp =>
          opp.id === id ? { ...opp, ...updates } : opp
        )
      }))
    },

    removeOpportunity: (id) => {
      set((state) => ({
        opportunities: state.opportunities.filter(opp => opp.id !== id)
      }))
    },

    setEngineStatus: (status) => {
      set({ engineStatus: status, isScanning: status.is_running })
    },

    setScanConfig: (config) => {
      set({ scanConfig: config })
    },

    setIsScanning: (isScanning) => {
      set({ isScanning })
    },

    setStats: (stats) => {
      set({ stats })
    },

    setSelectedOpportunity: (opportunity) => {
      set({ selectedOpportunity: opportunity })
    },

    setError: (error) => {
      set({ error })
      
      if (error) {
        toast.error(error, {
          duration: 5000,
        })
      }
    },

    // Async actions
    startScan: async () => {
      const { scanConfig } = get()
      
      set({ isStartingScan: true, error: null })
      
      try {
        const response = await arbitrageApi.startScan(scanConfig)
        
        if (response.status === 'success') {
          set({ 
            isScanning: true, 
            lastScanTime: new Date().toISOString(),
            isStartingScan: false 
          })
          
          // Update scan sessions count
          set((state) => ({
            stats: {
              ...state.stats,
              scan_sessions_today: state.stats.scan_sessions_today + 1
            }
          }))
          
          toast.success('Arbitrage scanning started', {
            icon: '🔍',
          })
        } else {
          throw new Error(response.error || 'Failed to start scan')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to start scan'
        set({ error: errorMessage, isStartingScan: false })
      }
    },

    stopScan: async () => {
      set({ isStoppingScan: true, error: null })
      
      try {
        const response = await arbitrageApi.stopScan()
        
        if (response.status === 'success') {
          set({ 
            isScanning: false, 
            isStoppingScan: false 
          })
          
          toast.success('Arbitrage scanning stopped', {
            icon: '⏹️',
          })
        } else {
          throw new Error(response.error || 'Failed to stop scan')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to stop scan'
        set({ error: errorMessage, isStoppingScan: false })
      }
    },

    refreshOpportunities: async () => {
      set({ isLoading: true, error: null })
      
      try {
        const response = await arbitrageApi.getOpportunities({
          limit: 100,
          min_profit: get().scanConfig.min_profit_percentage,
          sort_by: 'profit_percentage'
        })
        
        if (response.status === 'success') {
          set({ 
            opportunities: response.opportunities || [],
            isLoading: false 
          })
        } else {
          throw new Error(response.error || 'Failed to fetch opportunities')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch opportunities'
        set({ error: errorMessage, isLoading: false })
      }
    },

    validateOpportunity: async (opportunityId: string) => {
      try {
        const response = await arbitrageApi.validateOpportunity(opportunityId)
        
        if (response.status === 'success') {
          // Update the opportunity with validation results
          get().updateOpportunity(opportunityId, {
            is_validated: response.validation_result?.is_valid || false,
            validation_errors: response.validation_result?.validation_errors || []
          })
          
          const isValid = response.validation_result?.is_valid
          toast.success(
            isValid ? 'Opportunity validated successfully' : 'Opportunity validation failed',
            {
              icon: isValid ? '✅' : '❌',
            }
          )
        } else {
          throw new Error(response.error || 'Failed to validate opportunity')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to validate opportunity'
        set({ error: errorMessage })
      }
    },

    fetchEngineStatus: async () => {
      try {
        const response = await arbitrageApi.getStatus()
        
        if (response.status === 'success') {
          set({ engineStatus: response.engine_status })
        }
      } catch (error) {
        console.error('Failed to fetch engine status:', error)
      }
    },

    fetchSupportedChains: async () => {
      try {
        const response = await arbitrageApi.getSupportedChains()
        
        if (response.status === 'success') {
          return response.supported_chains?.map((chain: any) => chain.chain_id) || []
        }
        
        return []
      } catch (error) {
        console.error('Failed to fetch supported chains:', error)
        return []
      }
    },
  }))
)

// Selectors for computed values
export const useOpportunitiesWithFilters = (filters: {
  minProfit?: number
  maxProfit?: number
  chains?: string[]
  types?: string[]
}) => {
  return useArbitrageStore((state) => {
    return state.opportunities.filter(opp => {
      if (filters.minProfit && opp.profit_percentage < filters.minProfit) return false
      if (filters.maxProfit && opp.profit_percentage > filters.maxProfit) return false
      if (filters.chains?.length && !filters.chains.includes(opp.chain_id)) return false
      if (filters.types?.length && !filters.types.includes(opp.type)) return false
      return true
    })
  })
}

export const useTopOpportunities = (limit: number = 10) => {
  return useArbitrageStore((state) => {
    return state.opportunities
      .sort((a, b) => b.profit_percentage - a.profit_percentage)
      .slice(0, limit)
  })
}

export const useOpportunitiesByChain = () => {
  return useArbitrageStore((state) => {
    const byChain: { [key: string]: ArbitrageOpportunity[] } = {}
    
    state.opportunities.forEach(opp => {
      if (!byChain[opp.chain_id]) {
        byChain[opp.chain_id] = []
      }
      byChain[opp.chain_id].push(opp)
    })
    
    return byChain
  })
}
