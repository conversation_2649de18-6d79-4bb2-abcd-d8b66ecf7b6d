"""
Advanced Crypto Arbitrage Bot - Main Application (Fixed)
FastAPI backend with public APIs only - no backend dependencies
"""
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, JSONResponse
import async<PERSON>
import uvicorn
from contextlib import asynccontextmanager
from typing import Dict, Any, List
import time
import httpx
import json
from datetime import datetime

# Simple logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Simple WebSocket manager
class SimpleWebSocketManager:
    def __init__(self):
        self.connections = []
    
    async def connect(self, websocket):
        await websocket.accept()
        self.connections.append(websocket)
    
    def disconnect(self, websocket):
        if websocket in self.connections:
            self.connections.remove(websocket)
    
    async def broadcast(self, message):
        for connection in self.connections:
            try:
                await connection.send_text(json.dumps(message))
            except:
                pass

# Simple API client for public APIs
class SimpleAPIClient:
    def __init__(self, base_url: str, rate_limit: int = 30):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def get(self, endpoint: str, params: Dict = None) -> Dict:
        try:
            url = f"{self.base_url}/{endpoint}"
            response = await self.client.get(url, params=params or {})
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            logger.error(f"API request failed: {e}")
            return {}

# Simple arbitrage detector
class SimpleArbitrageDetector:
    def __init__(self):
        self.opportunities = []
        self.last_scan = None
    
    async def scan_opportunities(self) -> List[Dict[str, Any]]:
        """Scan for arbitrage opportunities"""
        opportunities = []
        
        try:
            # Get Bitcoin prices from different sources
            btc_prices = await self.get_btc_prices()
            
            if len(btc_prices) >= 2:
                # Find arbitrage opportunities
                for i, (source1, price1) in enumerate(btc_prices.items()):
                    for source2, price2 in list(btc_prices.items())[i+1:]:
                        if abs(price1 - price2) > 0:
                            if price2 > price1:
                                profit_pct = ((price2 - price1) / price1) * 100
                                buy_source, sell_source = source1, source2
                                buy_price, sell_price = price1, price2
                            else:
                                profit_pct = ((price1 - price2) / price2) * 100
                                buy_source, sell_source = source2, source1
                                buy_price, sell_price = price2, price1
                            
                            if profit_pct > 0.1:  # Minimum 0.1% profit
                                opportunities.append({
                                    "id": f"btc_{int(time.time())}_{i}",
                                    "token_symbol": "BTC",
                                    "buy_exchange": buy_source,
                                    "sell_exchange": sell_source,
                                    "buy_price": buy_price,
                                    "sell_price": sell_price,
                                    "profit_percentage": round(profit_pct, 3),
                                    "profit_usd": round(profit_pct * 10, 2),  # Assuming $1000 trade
                                    "timestamp": datetime.now().isoformat()
                                })
            
            self.opportunities = opportunities
            self.last_scan = datetime.now().isoformat()
            
        except Exception as e:
            logger.error(f"Scan error: {e}")
        
        return opportunities
    
    async def get_btc_prices(self) -> Dict[str, float]:
        """Get BTC prices from different sources"""
        prices = {}
        
        # CoinGecko
        try:
            cg_data = await coingecko.get("simple/price", {
                "ids": "bitcoin",
                "vs_currencies": "usd"
            })
            if "bitcoin" in cg_data and "usd" in cg_data["bitcoin"]:
                prices["coingecko"] = float(cg_data["bitcoin"]["usd"])
        except:
            pass
        
        # Binance
        try:
            binance_data = await binance.get("ticker/24hr", {"symbol": "BTCUSDT"})
            if "lastPrice" in binance_data:
                prices["binance"] = float(binance_data["lastPrice"])
        except:
            pass
        
        # DexScreener (search for WBTC)
        try:
            dex_data = await dexscreener.get("dex/search", {"q": "WBTC"})
            if "pairs" in dex_data and dex_data["pairs"]:
                # Get first valid pair
                for pair in dex_data["pairs"][:3]:
                    if pair.get("priceUsd"):
                        prices[f"dex_{pair.get('dexId', 'unknown')}"] = float(pair["priceUsd"])
                        break
        except:
            pass
        
        return prices

# Global instances
websocket_manager = SimpleWebSocketManager()
coingecko = SimpleAPIClient("https://api.coingecko.com/api/v3", 10)
dexscreener = SimpleAPIClient("https://api.dexscreener.com/latest", 60)
binance = SimpleAPIClient("https://api.binance.com/api/v3", 300)
detector = SimpleArbitrageDetector()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Crypto Arbitrage Bot v2.0...")
    
    try:
        logger.info("✅ Application startup complete - Using public APIs only!")
        yield
        
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down application...")
        
        # Cleanup HTTP clients
        await coingecko.client.aclose()
        await dexscreener.client.aclose()
        await binance.client.aclose()
        logger.info("HTTP clients cleaned up")
        
        logger.info("✅ Application shutdown complete")

# Create FastAPI app
app = FastAPI(
    title="Crypto Arbitrage Bot v2.0",
    description="Advanced cryptocurrency arbitrage detection with public APIs only",
    version="2.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the frontend application"""
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Crypto Arbitrage Bot v2.0</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #1a1a2e; color: white; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { text-align: center; margin-bottom: 40px; }
            .card { background: #16213e; padding: 20px; margin: 20px 0; border-radius: 10px; border: 1px solid #0f3460; }
            .opportunity { background: #0f3460; padding: 15px; margin: 10px 0; border-radius: 8px; }
            .profit-positive { color: #4ade80; font-weight: bold; }
            button { background: #3b82f6; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
            button:hover { background: #2563eb; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 Crypto Arbitrage Bot v2.0</h1>
                <p>Advanced arbitrage detection using public APIs only</p>
            </div>
            
            <div class="card">
                <h2>📊 Quick Actions</h2>
                <button onclick="scanOpportunities()">🔍 Scan for Opportunities</button>
                <button onclick="checkStatus()">❤️ Check Status</button>
                <button onclick="viewDocs()">📚 API Documentation</button>
            </div>
            
            <div class="card">
                <h2>💰 Current Opportunities</h2>
                <div id="opportunities">
                    <p>Click "Scan for Opportunities" to start detection...</p>
                </div>
            </div>
            
            <div class="card">
                <h2>📈 Data Sources (Public APIs)</h2>
                <ul>
                    <li>🦎 <strong>CoinGecko</strong>: Market data and token information</li>
                    <li>📊 <strong>DexScreener</strong>: DEX trading pairs and liquidity</li>
                    <li>🔶 <strong>Binance</strong>: CEX market data and order books</li>
                </ul>
            </div>
        </div>
        
        <script>
            async function scanOpportunities() {
                document.getElementById('opportunities').innerHTML = '<p>🔍 Scanning for opportunities...</p>';
                
                try {
                    const response = await fetch('/api/scan');
                    const data = await response.json();
                    
                    if (data.opportunities && data.opportunities.length > 0) {
                        let html = '';
                        data.opportunities.forEach(opp => {
                            html += `
                                <div class="opportunity">
                                    <h3>${opp.token_symbol} Arbitrage</h3>
                                    <p><strong>Strategy:</strong> Buy on ${opp.buy_exchange} ($${opp.buy_price}) → Sell on ${opp.sell_exchange} ($${opp.sell_price})</p>
                                    <p><strong>Profit:</strong> <span class="profit-positive">${opp.profit_percentage}% ($${opp.profit_usd})</span></p>
                                    <p><strong>Time:</strong> ${new Date(opp.timestamp).toLocaleString()}</p>
                                </div>
                            `;
                        });
                        document.getElementById('opportunities').innerHTML = html;
                    } else {
                        document.getElementById('opportunities').innerHTML = '<p>❌ No profitable opportunities found at this time.</p>';
                    }
                } catch (error) {
                    document.getElementById('opportunities').innerHTML = '<p>❌ Error scanning opportunities: ' + error.message + '</p>';
                }
            }
            
            async function checkStatus() {
                try {
                    const response = await fetch('/api/status');
                    const data = await response.json();
                    alert('✅ Status: ' + data.status + '\\n📊 Version: ' + data.version + '\\n🕒 Time: ' + new Date().toLocaleString());
                } catch (error) {
                    alert('❌ Error: ' + error.message);
                }
            }
            
            function viewDocs() {
                window.open('/docs', '_blank');
            }
        </script>
    </body>
    </html>
    """, status_code=200)

@app.get("/api/status")
async def get_status():
    """Get application status"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "mode": "public_apis_only",
        "timestamp": datetime.now().isoformat(),
        "last_scan": detector.last_scan,
        "opportunities_count": len(detector.opportunities)
    }

@app.get("/api/scan")
async def scan_opportunities():
    """Scan for arbitrage opportunities"""
    try:
        opportunities = await detector.scan_opportunities()
        
        return {
            "status": "success",
            "opportunities": opportunities,
            "count": len(opportunities),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/opportunities")
async def get_opportunities():
    """Get current opportunities"""
    return {
        "status": "success",
        "opportunities": detector.opportunities,
        "count": len(detector.opportunities),
        "last_scan": detector.last_scan
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            
            if data == "ping":
                await websocket.send_text("pong")
            elif data.startswith("scan"):
                opportunities = await detector.scan_opportunities()
                await websocket.send_text(json.dumps({
                    "type": "opportunities",
                    "data": opportunities
                }))
                
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)

if __name__ == "__main__":
    print("🚀 Starting Crypto Arbitrage Bot v2.0")
    print("📊 Using public APIs: CoinGecko, DexScreener, Binance")
    print("🌐 Web interface: http://localhost:8000")
    print("📚 API docs: http://localhost:8000/docs")
    print("=" * 60)
    
    uvicorn.run(
        "main_fixed:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
