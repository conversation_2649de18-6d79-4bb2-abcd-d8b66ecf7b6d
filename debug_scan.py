#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import json
import time

async def debug_scan_process():
    """Debug the scanning process to understand why no opportunities are found"""
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("🔍 DEBUGGING SCAN PROCESS")
        print("=" * 60)
        
        # 1. Check if v3.0 features are actually enabled
        print("1. Checking v3.0 Feature Status...")
        try:
            async with session.get(f"{base_url}/api/v3/performance-metrics") as response:
                if response.status == 200:
                    data = await response.json()
                    metrics = data.get('performance_metrics', {})
                    v3_features = metrics.get('v3_features', {})
                    
                    print(f"   Dynamic Discovery: {v3_features.get('dynamic_discovery_enabled', 'NOT FOUND')}")
                    print(f"   Tier Management: {v3_features.get('tier_management_enabled', 'NOT FOUND')}")
                    print(f"   Adaptive Thresholds: {v3_features.get('adaptive_thresholds_enabled', 'NOT FOUND')}")
                    print(f"   Max Tokens: {v3_features.get('max_total_tokens', 'NOT FOUND')}")
                    
                    if not any([
                        v3_features.get('dynamic_discovery_enabled', False),
                        v3_features.get('tier_management_enabled', False),
                        v3_features.get('adaptive_thresholds_enabled', False)
                    ]):
                        print("   ❌ WARNING: v3.0 features appear to be disabled!")
                    else:
                        print("   ✅ v3.0 features are enabled")
        except Exception as e:
            print(f"   ❌ Error checking v3.0 features: {e}")
        
        print("\n" + "=" * 60)
        
        # 2. Check current configuration
        print("2. Checking Current Configuration...")
        try:
            async with session.get(f"{base_url}/api/config") as response:
                if response.status == 200:
                    data = await response.json()
                    config = data.get('config', {})
                    
                    print(f"   Profit Min: {config.get('profit_min', 'NOT FOUND')}%")
                    print(f"   Profit Max: {config.get('profit_max', 'NOT FOUND')}%")
                    print(f"   Min Liquidity: ${config.get('min_liquidity', 'NOT FOUND'):,}")
                    print(f"   Min Volume 24h: ${config.get('min_volume_24h', 'NOT FOUND'):,}")
                    print(f"   Enabled Chains: {config.get('enabled_chains', 'NOT FOUND')}")
                    print(f"   Enabled Categories: {config.get('enabled_token_categories', 'NOT FOUND')}")
                    
                    # Check if thresholds are too restrictive
                    profit_min = config.get('profit_min', 0)
                    if profit_min > 5:
                        print(f"   ⚠️  WARNING: Profit minimum ({profit_min}%) might be too high!")
                    
                    min_liquidity = config.get('min_liquidity', 0)
                    if min_liquidity > 50000:
                        print(f"   ⚠️  WARNING: Min liquidity (${min_liquidity:,}) might be too high!")
        except Exception as e:
            print(f"   ❌ Error checking configuration: {e}")
        
        print("\n" + "=" * 60)
        
        # 3. Check token distribution and tiers
        print("3. Checking Token Distribution and Tiers...")
        try:
            async with session.get(f"{base_url}/api/v3/token-distribution") as response:
                if response.status == 200:
                    data = await response.json()
                    dist = data.get('token_distribution', {})
                    
                    total_tokens = dist.get('total_tokens', 0)
                    tier_dist = dist.get('tier_distribution', {})
                    
                    print(f"   Total Tokens: {total_tokens}")
                    print(f"   Tier Distribution:")
                    for tier, count in tier_dist.items():
                        print(f"      {tier}: {count} tokens")
                    
                    if total_tokens < 100:
                        print(f"   ⚠️  WARNING: Only {total_tokens} tokens - might be too few!")
        except Exception as e:
            print(f"   ❌ Error checking token distribution: {e}")
        
        print("\n" + "=" * 60)
        
        # 4. Check profit thresholds
        print("4. Checking Profit Thresholds...")
        try:
            async with session.get(f"{base_url}/api/v3/profit-thresholds") as response:
                if response.status == 200:
                    data = await response.json()
                    thresholds = data.get('profit_thresholds', {})
                    
                    current_volatility = thresholds.get('current_volatility', 'unknown')
                    threshold_data = thresholds.get('thresholds', {})
                    
                    print(f"   Market Volatility: {current_volatility}")
                    print(f"   Category Thresholds:")
                    
                    for category, thresh in threshold_data.items():
                        if isinstance(thresh, dict):
                            min_thresh = thresh.get('min', 0)
                            max_thresh = thresh.get('max', 0)
                            print(f"      {category}: {min_thresh:.1f}% - {max_thresh:.1f}%")
                            
                            if min_thresh > 10:
                                print(f"         ⚠️  WARNING: {category} min threshold ({min_thresh}%) very high!")
        except Exception as e:
            print(f"   ❌ Error checking profit thresholds: {e}")
        
        print("\n" + "=" * 60)
        
        # 5. Test a simple scan with debug info
        print("5. Testing Scan with Debug Info...")
        print("   Starting scan...")
        
        scan_start = time.time()
        try:
            async with session.get(f"{base_url}/api/scan") as response:
                scan_duration = time.time() - scan_start
                
                if response.status == 200:
                    data = await response.json()
                    
                    print(f"   ✅ Scan completed in {scan_duration:.2f} seconds")
                    print(f"   Status: {data.get('status', 'unknown')}")
                    
                    opportunities = data.get('opportunities', [])
                    print(f"   Opportunities found: {len(opportunities)}")
                    
                    if 'summary' in data:
                        summary = data['summary']
                        print(f"   Tokens scanned: {summary.get('tokens_scanned', 0)}")
                        print(f"   Tokens per second: {summary.get('tokens_per_second', 0):.1f}")
                        print(f"   Cache hit rate: {summary.get('cache_hit_rate', 0):.1f}%")
                    
                    # Check for any error messages in logs
                    if 'logs' in data:
                        logs = data['logs']
                        error_logs = [log for log in logs if 'error' in log.get('message', '').lower()]
                        if error_logs:
                            print(f"   ⚠️  Found {len(error_logs)} error logs:")
                            for log in error_logs[-3:]:  # Show last 3 errors
                                print(f"      {log.get('message', '')}")
                
                else:
                    print(f"   ❌ Scan failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"   ❌ Scan error: {e}")
        
        print("\n" + "=" * 60)
        print("🎯 DEBUG ANALYSIS COMPLETE")
        print("\nRecommendations:")
        print("1. Check if DexScreener API is responding correctly")
        print("2. Verify profit thresholds are not too restrictive")
        print("3. Ensure sufficient tokens are being scanned")
        print("4. Check if v3.0 tier-based logic is actually being used")

if __name__ == "__main__":
    asyncio.run(debug_scan_process())
