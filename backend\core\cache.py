"""
Redis cache configuration and utilities
"""
import json
import pickle
from typing import Any, Optional, Union
import aioredis
from backend.config.settings import settings
import logging

logger = logging.getLogger(__name__)


class CacheManager:
    """Redis cache manager with async support"""
    
    def __init__(self):
        self.redis: Optional[aioredis.Redis] = None
        
    async def connect(self):
        """Connect to Red<PERSON>"""
        try:
            self.redis = aioredis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=False,
                max_connections=20,
                retry_on_timeout=True,
            )
            # Test connection
            await self.redis.ping()
            logger.info("Connected to Red<PERSON> successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from Redis"""
        if self.redis:
            await self.redis.close()
            logger.info("Disconnected from Redis")
    
    async def get(self, key: str, use_json: bool = True) -> Optional[Any]:
        """Get value from cache"""
        try:
            if not self.redis:
                return None
                
            value = await self.redis.get(key)
            if value is None:
                return None
                
            if use_json:
                return json.loads(value)
            else:
                return pickle.loads(value)
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None, 
        use_json: bool = True
    ) -> bool:
        """Set value in cache"""
        try:
            if not self.redis:
                return False
                
            if use_json:
                serialized_value = json.dumps(value, default=str)
            else:
                serialized_value = pickle.dumps(value)
                
            ttl = ttl or settings.CACHE_TTL
            await self.redis.setex(key, ttl, serialized_value)
            return True
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            if not self.redis:
                return False
            await self.redis.delete(key)
            return True
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            if not self.redis:
                return False
            return bool(await self.redis.exists(key))
        except Exception as e:
            logger.error(f"Error checking cache key {key}: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment counter in cache"""
        try:
            if not self.redis:
                return None
            return await self.redis.incrby(key, amount)
        except Exception as e:
            logger.error(f"Error incrementing cache key {key}: {e}")
            return None
    
    async def expire(self, key: str, ttl: int) -> bool:
        """Set expiration for key"""
        try:
            if not self.redis:
                return False
            return await self.redis.expire(key, ttl)
        except Exception as e:
            logger.error(f"Error setting expiration for cache key {key}: {e}")
            return False


# Global cache instance
cache = CacheManager()
