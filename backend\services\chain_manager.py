"""
Multi-chain support and cross-chain arbitrage detection
"""
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass
from enum import Enum
import asyncio
from backend.core.logging import get_logger
from backend.core.cache import cache
from backend.config.settings import settings

logger = get_logger(__name__)


class ChainType(Enum):
    """Supported blockchain types"""
    EVM = "evm"
    SOLANA = "solana"
    COSMOS = "cosmos"
    SUBSTRATE = "substrate"


@dataclass
class ChainConfig:
    """Configuration for a blockchain network"""
    chain_id: str
    name: str
    chain_type: ChainType
    native_token: str
    rpc_urls: List[str]
    explorer_url: str
    dex_screener_id: str
    coingecko_id: str
    gas_token: str
    avg_gas_price_gwei: float
    avg_block_time_seconds: float
    bridge_tokens: List[str]  # Common bridged tokens
    major_dexes: List[str]
    is_active: bool = True


class ChainManager:
    """Manages multiple blockchain networks and cross-chain operations"""
    
    def __init__(self):
        self.chains = self._initialize_chains()
        self.bridge_mappings = self._initialize_bridge_mappings()
    
    def _initialize_chains(self) -> Dict[str, ChainConfig]:
        """Initialize supported blockchain configurations"""
        chains = {
            "ethereum": ChainConfig(
                chain_id="ethereum",
                name="Ethereum",
                chain_type=ChainType.EVM,
                native_token="ETH",
                rpc_urls=[
                    "https://eth-mainnet.alchemyapi.io/v2/",
                    "https://mainnet.infura.io/v3/",
                    "https://rpc.ankr.com/eth"
                ],
                explorer_url="https://etherscan.io",
                dex_screener_id="ethereum",
                coingecko_id="ethereum",
                gas_token="ETH",
                avg_gas_price_gwei=30.0,
                avg_block_time_seconds=12.0,
                bridge_tokens=["USDC", "USDT", "WBTC", "DAI"],
                major_dexes=["uniswap_v3", "uniswap_v2", "sushiswap", "1inch"]
            ),
            
            "bsc": ChainConfig(
                chain_id="bsc",
                name="BNB Smart Chain",
                chain_type=ChainType.EVM,
                native_token="BNB",
                rpc_urls=[
                    "https://bsc-dataseed1.binance.org/",
                    "https://bsc-dataseed2.binance.org/",
                    "https://rpc.ankr.com/bsc"
                ],
                explorer_url="https://bscscan.com",
                dex_screener_id="bsc",
                coingecko_id="binance-smart-chain",
                gas_token="BNB",
                avg_gas_price_gwei=5.0,
                avg_block_time_seconds=3.0,
                bridge_tokens=["USDC", "USDT", "BTCB", "ETH"],
                major_dexes=["pancakeswap", "biswap", "apeswap", "1inch"]
            ),
            
            "polygon": ChainConfig(
                chain_id="polygon",
                name="Polygon",
                chain_type=ChainType.EVM,
                native_token="MATIC",
                rpc_urls=[
                    "https://polygon-rpc.com/",
                    "https://rpc.ankr.com/polygon",
                    "https://matic-mainnet.chainstacklabs.com"
                ],
                explorer_url="https://polygonscan.com",
                dex_screener_id="polygon",
                coingecko_id="polygon-pos",
                gas_token="MATIC",
                avg_gas_price_gwei=30.0,
                avg_block_time_seconds=2.0,
                bridge_tokens=["USDC", "USDT", "WBTC", "DAI", "WETH"],
                major_dexes=["quickswap", "sushiswap", "uniswap_v3", "1inch"]
            ),
            
            "arbitrum": ChainConfig(
                chain_id="arbitrum",
                name="Arbitrum One",
                chain_type=ChainType.EVM,
                native_token="ETH",
                rpc_urls=[
                    "https://arb1.arbitrum.io/rpc",
                    "https://rpc.ankr.com/arbitrum",
                    "https://arbitrum-mainnet.infura.io/v3/"
                ],
                explorer_url="https://arbiscan.io",
                dex_screener_id="arbitrum",
                coingecko_id="arbitrum-one",
                gas_token="ETH",
                avg_gas_price_gwei=0.1,
                avg_block_time_seconds=1.0,
                bridge_tokens=["USDC", "USDT", "WBTC", "DAI"],
                major_dexes=["uniswap_v3", "sushiswap", "balancer", "1inch"]
            ),
            
            "avalanche": ChainConfig(
                chain_id="avalanche",
                name="Avalanche C-Chain",
                chain_type=ChainType.EVM,
                native_token="AVAX",
                rpc_urls=[
                    "https://api.avax.network/ext/bc/C/rpc",
                    "https://rpc.ankr.com/avalanche",
                    "https://ava-mainnet.public.blastapi.io/ext/bc/C/rpc"
                ],
                explorer_url="https://snowtrace.io",
                dex_screener_id="avalanche",
                coingecko_id="avalanche",
                gas_token="AVAX",
                avg_gas_price_gwei=25.0,
                avg_block_time_seconds=2.0,
                bridge_tokens=["USDC", "USDT", "WBTC.e", "DAI.e", "WETH.e"],
                major_dexes=["traderjoe", "pangolin", "sushiswap", "1inch"]
            ),
            
            "solana": ChainConfig(
                chain_id="solana",
                name="Solana",
                chain_type=ChainType.SOLANA,
                native_token="SOL",
                rpc_urls=[
                    "https://api.mainnet-beta.solana.com",
                    "https://rpc.ankr.com/solana",
                    "https://solana-api.projectserum.com"
                ],
                explorer_url="https://solscan.io",
                dex_screener_id="solana",
                coingecko_id="solana",
                gas_token="SOL",
                avg_gas_price_gwei=0.000005,  # Very low fees
                avg_block_time_seconds=0.4,
                bridge_tokens=["USDC", "USDT", "WBTC", "ETH"],
                major_dexes=["raydium", "orca", "serum", "jupiter"]
            )
        }
        
        return chains
    
    def _initialize_bridge_mappings(self) -> Dict[str, Dict[str, str]]:
        """Initialize token bridge mappings between chains"""
        return {
            "USDC": {
                "ethereum": "******************************************",
                "bsc": "******************************************",
                "polygon": "******************************************",
                "arbitrum": "******************************************",
                "avalanche": "******************************************",
                "solana": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
            },
            "USDT": {
                "ethereum": "******************************************",
                "bsc": "******************************************",
                "polygon": "******************************************",
                "arbitrum": "******************************************",
                "avalanche": "******************************************",
                "solana": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"
            },
            "WBTC": {
                "ethereum": "******************************************",
                "bsc": "******************************************",
                "polygon": "******************************************",
                "arbitrum": "******************************************",
                "avalanche": "******************************************"
            }
        }
    
    def get_chain_config(self, chain_id: str) -> Optional[ChainConfig]:
        """Get configuration for a specific chain"""
        return self.chains.get(chain_id)
    
    def get_active_chains(self) -> List[ChainConfig]:
        """Get all active blockchain configurations"""
        return [chain for chain in self.chains.values() if chain.is_active]
    
    def get_supported_chains(self) -> List[str]:
        """Get list of supported chain IDs"""
        return list(self.chains.keys())
    
    def get_evm_chains(self) -> List[ChainConfig]:
        """Get all EVM-compatible chains"""
        return [
            chain for chain in self.chains.values() 
            if chain.chain_type == ChainType.EVM and chain.is_active
        ]
    
    def get_bridge_token_address(self, token_symbol: str, chain_id: str) -> Optional[str]:
        """Get token contract address on specific chain"""
        return self.bridge_mappings.get(token_symbol, {}).get(chain_id)
    
    def get_common_bridge_tokens(self, chain1: str, chain2: str) -> List[str]:
        """Get tokens that can be bridged between two chains"""
        if chain1 not in self.chains or chain2 not in self.chains:
            return []
        
        chain1_tokens = set(self.chains[chain1].bridge_tokens)
        chain2_tokens = set(self.chains[chain2].bridge_tokens)
        
        return list(chain1_tokens.intersection(chain2_tokens))
    
    async def estimate_gas_cost(
        self, 
        chain_id: str, 
        transaction_type: str = "swap"
    ) -> Dict[str, Any]:
        """Estimate gas cost for transaction on specific chain"""
        chain = self.get_chain_config(chain_id)
        if not chain:
            return {"error": "Unsupported chain"}
        
        # Base gas estimates for different transaction types
        gas_estimates = {
            "swap": 150000,  # DEX swap
            "bridge": 200000,  # Cross-chain bridge
            "transfer": 21000,  # Simple transfer
            "approve": 50000   # Token approval
        }
        
        base_gas = gas_estimates.get(transaction_type, 150000)
        
        # Calculate cost in native token
        gas_price_wei = chain.avg_gas_price_gwei * 1e9
        gas_cost_native = (base_gas * gas_price_wei) / 1e18
        
        # Get native token price in USD (cached)
        cache_key = f"token_price:{chain.native_token}"
        native_price_usd = await cache.get(cache_key)
        
        if not native_price_usd:
            # Default prices (should be updated from real API)
            default_prices = {
                "ETH": 2000,
                "BNB": 300,
                "MATIC": 0.8,
                "AVAX": 25,
                "SOL": 60
            }
            native_price_usd = default_prices.get(chain.native_token, 100)
            await cache.set(cache_key, native_price_usd, ttl=300)
        
        gas_cost_usd = gas_cost_native * native_price_usd
        
        return {
            "chain_id": chain_id,
            "transaction_type": transaction_type,
            "estimated_gas": base_gas,
            "gas_price_gwei": chain.avg_gas_price_gwei,
            "gas_cost_native": gas_cost_native,
            "gas_cost_usd": gas_cost_usd,
            "native_token": chain.native_token,
            "native_token_price_usd": native_price_usd
        }
    
    async def calculate_cross_chain_arbitrage(
        self, 
        token_symbol: str, 
        chain_prices: Dict[str, float],
        amount_usd: float = 1000
    ) -> List[Dict[str, Any]]:
        """Calculate cross-chain arbitrage opportunities"""
        
        opportunities = []
        
        # Only consider tokens that can be bridged
        bridgeable_tokens = set(self.bridge_mappings.keys())
        if token_symbol not in bridgeable_tokens:
            return opportunities
        
        # Get chains where this token exists
        available_chains = []
        for chain_id, price in chain_prices.items():
            if (chain_id in self.chains and 
                price > 0 and 
                self.get_bridge_token_address(token_symbol, chain_id)):
                available_chains.append((chain_id, price))
        
        if len(available_chains) < 2:
            return opportunities
        
        # Sort by price
        available_chains.sort(key=lambda x: x[1])
        
        # Calculate arbitrage opportunities
        for i in range(len(available_chains)):
            for j in range(i + 1, len(available_chains)):
                buy_chain, buy_price = available_chains[i]
                sell_chain, sell_price = available_chains[j]
                
                if buy_price >= sell_price:
                    continue
                
                # Calculate profit before costs
                gross_profit_percentage = ((sell_price - buy_price) / buy_price) * 100
                
                # Estimate costs
                buy_gas = await self.estimate_gas_cost(buy_chain, "swap")
                sell_gas = await self.estimate_gas_cost(sell_chain, "swap")
                bridge_gas = await self.estimate_gas_cost(buy_chain, "bridge")
                
                total_gas_cost = (
                    buy_gas.get("gas_cost_usd", 0) +
                    sell_gas.get("gas_cost_usd", 0) +
                    bridge_gas.get("gas_cost_usd", 0)
                )
                
                # Bridge fees (typically 0.1-0.3%)
                bridge_fee_percentage = 0.2
                bridge_fee_usd = amount_usd * (bridge_fee_percentage / 100)
                
                # DEX fees (typically 0.3%)
                dex_fee_percentage = 0.6  # 0.3% each for buy and sell
                dex_fee_usd = amount_usd * (dex_fee_percentage / 100)
                
                total_cost_usd = total_gas_cost + bridge_fee_usd + dex_fee_usd
                total_cost_percentage = (total_cost_usd / amount_usd) * 100
                
                net_profit_percentage = gross_profit_percentage - total_cost_percentage
                
                # Only include profitable opportunities
                if net_profit_percentage > 0.5:  # Minimum 0.5% profit
                    opportunity = {
                        "type": "cross_chain_arbitrage",
                        "token_symbol": token_symbol,
                        "buy_chain": buy_chain,
                        "sell_chain": sell_chain,
                        "buy_price": buy_price,
                        "sell_price": sell_price,
                        "gross_profit_percentage": gross_profit_percentage,
                        "net_profit_percentage": net_profit_percentage,
                        "estimated_costs": {
                            "total_gas_cost_usd": total_gas_cost,
                            "bridge_fee_usd": bridge_fee_usd,
                            "dex_fee_usd": dex_fee_usd,
                            "total_cost_usd": total_cost_usd,
                            "total_cost_percentage": total_cost_percentage
                        },
                        "execution_steps": [
                            f"Buy {token_symbol} on {buy_chain}",
                            f"Bridge {token_symbol} from {buy_chain} to {sell_chain}",
                            f"Sell {token_symbol} on {sell_chain}"
                        ],
                        "estimated_time_minutes": self._estimate_execution_time(buy_chain, sell_chain),
                        "risk_level": self._assess_cross_chain_risk(buy_chain, sell_chain),
                        "amount_usd": amount_usd
                    }
                    
                    opportunities.append(opportunity)
        
        # Sort by net profit
        opportunities.sort(key=lambda x: x["net_profit_percentage"], reverse=True)
        
        return opportunities
    
    def _estimate_execution_time(self, buy_chain: str, sell_chain: str) -> int:
        """Estimate total execution time for cross-chain arbitrage"""
        buy_config = self.get_chain_config(buy_chain)
        sell_config = self.get_chain_config(sell_chain)
        
        if not buy_config or not sell_config:
            return 60  # Default 1 hour
        
        # Base times
        buy_time = buy_config.avg_block_time_seconds * 3  # 3 confirmations
        sell_time = sell_config.avg_block_time_seconds * 3
        
        # Bridge time (varies by route)
        bridge_times = {
            ("ethereum", "polygon"): 300,  # 5 minutes
            ("ethereum", "arbitrum"): 600,  # 10 minutes
            ("ethereum", "avalanche"): 900,  # 15 minutes
            ("bsc", "polygon"): 1200,  # 20 minutes
            ("solana", "ethereum"): 1800,  # 30 minutes
        }
        
        bridge_time = bridge_times.get((buy_chain, sell_chain), 1800)  # Default 30 minutes
        
        total_seconds = buy_time + bridge_time + sell_time
        return int(total_seconds / 60)  # Convert to minutes
    
    def _assess_cross_chain_risk(self, buy_chain: str, sell_chain: str) -> str:
        """Assess risk level for cross-chain arbitrage"""
        buy_config = self.get_chain_config(buy_chain)
        sell_config = self.get_chain_config(sell_chain)
        
        if not buy_config or not sell_config:
            return "high"
        
        # Risk factors
        risk_score = 0
        
        # Chain maturity
        mature_chains = {"ethereum", "bsc", "polygon"}
        if buy_chain not in mature_chains:
            risk_score += 1
        if sell_chain not in mature_chains:
            risk_score += 1
        
        # Bridge complexity
        if buy_config.chain_type != sell_config.chain_type:
            risk_score += 2  # Cross-ecosystem bridges are riskier
        
        # Time exposure
        execution_time = self._estimate_execution_time(buy_chain, sell_chain)
        if execution_time > 30:
            risk_score += 1
        if execution_time > 60:
            risk_score += 1
        
        # Risk levels
        if risk_score <= 1:
            return "low"
        elif risk_score <= 3:
            return "medium"
        else:
            return "high"


# Global chain manager instance
chain_manager = ChainManager()
