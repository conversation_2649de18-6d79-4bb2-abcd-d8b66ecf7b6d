import React from 'react'
import { motion } from 'framer-motion'
import Card from '@components/ui/Card'
import Badge, { ProfitBadge, RiskBadge, ChainBadge, MLScoreBadge } from '@components/ui/Badge'
import Button from '@components/ui/Button'
import { ArbitrageOpportunity } from '@types/index'
import { formatCurrency, formatPercentage, formatTimeAgo } from '@utils/formatters'
import { 
  TrendingUpIcon, 
  TrendingDownIcon,
  ClockIcon,
  ExternalLinkIcon,
  ShieldCheckIcon,
  CpuChipIcon,
  BanknotesIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'

interface OpportunityCardProps {
  opportunity: ArbitrageOpportunity
  onSelect?: (opportunity: ArbitrageOpportunity) => void
  onValidate?: (opportunity: ArbitrageOpportunity) => void
  compact?: boolean
}

const OpportunityCard: React.FC<OpportunityCardProps> = ({
  opportunity,
  onSelect,
  onValidate,
  compact = false
}) => {
  const {
    token_symbol,
    profit_percentage,
    buy_price_usd,
    sell_price_usd,
    buy_liquidity_usd,
    sell_liquidity_usd,
    chain_id,
    type,
    ml_score,
    discovered_at,
    is_validated,
    risk_score
  } = opportunity

  const minLiquidity = Math.min(buy_liquidity_usd || 0, sell_liquidity_usd || 0)
  const isHighProfit = profit_percentage > 5
  const isValidated = is_validated === true

  const cardVariant = isHighProfit ? 'neon' : 'default'
  const shouldGlow = isHighProfit && (ml_score?.confidence || 0) > 0.7

  const handleCardClick = () => {
    onSelect?.(opportunity)
  }

  const handleValidateClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onValidate?.(opportunity)
  }

  const handleExternalClick = (url: string) => (e: React.MouseEvent) => {
    e.stopPropagation()
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  if (compact) {
    return (
      <motion.div
        className="p-4 bg-dark-800/50 rounded-xl border border-dark-600/50 hover:border-dark-500/50 transition-all cursor-pointer"
        onClick={handleCardClick}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="text-lg font-bold text-white">{token_symbol}</div>
            <ChainBadge chain={chain_id} />
            <ProfitBadge profit={profit_percentage} />
          </div>
          <div className="text-sm text-gray-400">
            {formatTimeAgo(discovered_at)}
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <Card
      variant={cardVariant}
      glow={shouldGlow}
      hover
      className="cursor-pointer"
      onClick={handleCardClick}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="text-xl font-bold text-white">{token_symbol}</div>
          <ChainBadge chain={chain_id} />
          <Badge variant="info" size="xs">
            {type.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          {isValidated && (
            <Badge variant="success" icon={<ShieldCheckIcon className="w-3 h-3" />}>
              Validated
            </Badge>
          )}
          <div className="text-xs text-gray-400">
            {formatTimeAgo(discovered_at)}
          </div>
        </div>
      </div>

      {/* Profit Display */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-400">Profit Potential</span>
          <ProfitBadge profit={profit_percentage} />
        </div>
        
        <div className="relative">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <TrendingUpIcon className="w-4 h-4 text-success" />
              <span className="text-gray-300">Buy: {formatCurrency(buy_price_usd)}</span>
            </div>
            <ArrowRightIcon className="w-4 h-4 text-gray-500" />
            <div className="flex items-center space-x-2">
              <TrendingDownIcon className="w-4 h-4 text-error" />
              <span className="text-gray-300">Sell: {formatCurrency(sell_price_usd)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Liquidity & Risk Info */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <BanknotesIcon className="w-4 h-4 text-neon-blue" />
            <span className="text-xs text-gray-400">Min Liquidity</span>
          </div>
          <div className="text-sm font-medium text-white">
            {formatCurrency(minLiquidity)}
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <ClockIcon className="w-4 h-4 text-neon-purple" />
            <span className="text-xs text-gray-400">Risk Level</span>
          </div>
          <RiskBadge risk={risk_score ? (risk_score > 0.7 ? 'high' : risk_score > 0.4 ? 'medium' : 'low') : 'medium'} />
        </div>
      </div>

      {/* ML Score */}
      {ml_score && (
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <CpuChipIcon className="w-4 h-4 text-neon-cyan" />
              <span className="text-xs text-gray-400">ML Analysis</span>
            </div>
            <MLScoreBadge score={ml_score.total_score} confidence={ml_score.confidence} />
          </div>
          
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center">
              <div className="text-gray-400">Prediction</div>
              <div className="text-white font-medium">
                {formatPercentage(ml_score.prediction_factor - 1)}
              </div>
            </div>
            <div className="text-center">
              <div className="text-gray-400">Sentiment</div>
              <div className="text-white font-medium">
                {formatPercentage(ml_score.sentiment_factor - 1)}
              </div>
            </div>
            <div className="text-center">
              <div className="text-gray-400">Pattern</div>
              <div className="text-white font-medium">
                {formatPercentage(ml_score.pattern_factor - 1)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-2">
        <Button
          variant="primary"
          size="sm"
          fullWidth
          onClick={handleCardClick}
        >
          View Details
        </Button>
        
        {!isValidated && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleValidateClick}
            icon={<ShieldCheckIcon className="w-4 h-4" />}
          >
            Validate
          </Button>
        )}
        
        {opportunity.buy_url && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleExternalClick(opportunity.buy_url)}
            icon={<ExternalLinkIcon className="w-4 h-4" />}
          />
        )}
      </div>

      {/* Glow effect for high-profit opportunities */}
      {isHighProfit && (
        <div className="absolute inset-0 bg-gradient-to-br from-neon-blue/10 via-transparent to-neon-purple/10 pointer-events-none rounded-2xl" />
      )}
    </Card>
  )
}

export default OpportunityCard
