// Core types for the Crypto Arbitrage Bot

export interface ArbitrageOpportunity {
  id: string
  type: 'dex_arbitrage' | 'cex_dex_arbitrage' | 'cross_chain_arbitrage'
  token_symbol: string
  base_token_address?: string
  quote_token_symbol: string
  chain_id: string
  
  // Buy side
  buy_dex_id?: string
  buy_exchange?: string
  buy_price_usd: number
  buy_liquidity_usd: number
  buy_url?: string
  
  // Sell side
  sell_dex_id?: string
  sell_exchange?: string
  sell_price_usd: number
  sell_liquidity_usd: number
  sell_url?: string
  
  // Profit analysis
  profit_percentage: number
  profit_usd?: number
  gas_fee_estimate?: number
  net_profit_percentage?: number
  
  // Risk assessment
  risk_score?: number
  slippage_estimate?: number
  liquidity_ratio?: number
  volume_24h?: number
  
  // ML enhancement
  ml_score?: MLScore
  ml_data?: MLData
  
  // Validation
  is_validated?: boolean
  validation_errors?: string[]
  
  // Timestamps
  discovered_at: string
  last_updated?: string
  expires_at?: string
  
  // Additional data
  metadata?: Record<string, any>
}

export interface MLScore {
  total_score: number
  profit_score: number
  prediction_factor: number
  sentiment_factor: number
  pattern_factor: number
  volume_factor: number
  risk_factor: number
  confidence: number
}

export interface MLData {
  price_prediction?: PricePrediction
  sentiment?: SentimentAnalysis
  patterns?: PatternAnalysis
}

export interface PricePrediction {
  predictions: Record<string, PredictionResult>
  data_points: number
  generated_at: string
}

export interface PredictionResult {
  status: string
  current_price?: number
  predicted_price?: number
  predicted_change_percentage?: number
  confidence_score?: number
  prediction_horizon_hours?: number
  timestamp?: string
}

export interface SentimentAnalysis {
  token_symbol: string
  overall_sentiment: {
    score: number
    label: 'positive' | 'negative' | 'neutral'
    confidence: number
  }
  sources: Record<string, SentimentSource>
  analysis_timestamp: string
  recommendation: string
}

export interface SentimentSource {
  sentiment_score: number
  post_count?: number
  tweet_count?: number
  article_count?: number
  confidence: number
  source: string
}

export interface PatternAnalysis {
  token_symbol: string
  data_points: number
  analysis_timestamp: string
  support_resistance: {
    support: number[]
    resistance: number[]
  }
  breakout: {
    type: string
    confidence: number
    level?: number
    current_price?: number
    volume_confirmation?: boolean
  }
  trend_reversal: {
    type: string
    confidence: number
    rsi?: number
  }
  consolidation: {
    is_consolidating: boolean
    price_range: number
    volatility: number
    confidence: number
  }
  volume_profile: {
    volume_trend: string
    volume_ratio: number
    volume_confirmation: boolean
    confidence: number
  }
  volume_spikes: Array<{
    timestamp: string
    volume: number
    volume_ratio: number
    price_change: number
    significance: number
  }>
  pattern_score: {
    bullish_score: number
    bearish_score: number
    neutral_score: number
  }
  signals: Array<{
    type: 'buy' | 'sell'
    strength: 'strong' | 'moderate' | 'weak'
    confidence: number
    reason: string
  }>
}

export interface ChainConfig {
  chain_id: string
  name: string
  chain_type: string
  native_token: string
  explorer_url: string
  major_dexes: string[]
  avg_block_time_seconds: number
  avg_gas_price_gwei: number
}

export interface ScanConfig {
  networks: string[]
  min_profit_percentage: number
  max_profit_percentage: number
  min_liquidity_usd: number
  min_volume_24h: number
  max_slippage_percentage: number
  include_cex_arbitrage: boolean
  include_cross_chain: boolean
  max_execution_time_minutes: number
  amount_usd: number
}

export interface ValidationConfig {
  max_slippage_percentage: number
  min_liquidity_usd: number
  max_gas_cost_percentage: number
  max_mev_risk_level: 'low' | 'medium' | 'high' | 'very_high'
}

export interface ValidationResult {
  is_valid: boolean
  confidence_score: number
  validation_errors: string[]
  warnings: string[]
  recommendations: string[]
  detailed_analysis: {
    liquidity?: any
    gas_costs?: any
    slippage?: any
    mev_risk?: any
    market_conditions?: any
  }
}

export interface WebSocketMessage {
  type: string
  data?: any
  timestamp?: string
  session_id?: string
  [key: string]: any
}

export interface ArbitrageEngineStatus {
  is_running: boolean
  session_id: string | null
  opportunities_count: number
  supported_networks: string[]
  websocket_connections: number
}

export interface GasEstimate {
  chain_id: string
  transaction_type: string
  priority: string
  estimated_gas: number
  gas_price_gwei: number
  gas_cost_native: number
  gas_cost_usd: number
  native_token: string
  native_token_price_usd: number
}

export interface TokenMetrics {
  token_symbol: string
  token_address: string
  chain_id: string
  current_price_usd: number
  price_change_1h?: number
  price_change_24h?: number
  price_change_7d?: number
  volume_24h?: number
  market_cap?: number
  total_liquidity?: number
  sentiment_score?: number
  volatility_24h?: number
  updated_at: string
}

export interface DashboardStats {
  total_opportunities: number
  avg_profit_percentage: number
  total_volume_analyzed: number
  active_chains: number
  scan_sessions_today: number
  best_opportunity_profit: number
}

export interface FilterOptions {
  chains: string[]
  min_profit: number
  max_profit: number
  min_liquidity: number
  risk_levels: string[]
  opportunity_types: string[]
  sort_by: 'profit_percentage' | 'ml_score' | 'discovered_at' | 'liquidity'
  sort_order: 'asc' | 'desc'
}

export interface ChartDataPoint {
  x: number | string
  y: number
  label?: string
  color?: string
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'scatter' | 'doughnut' | 'radar'
  data: ChartDataPoint[]
  options?: any
  responsive?: boolean
  maintainAspectRatio?: boolean
}

export interface NotificationConfig {
  enabled: boolean
  min_profit_threshold: number
  chains: string[]
  sound_enabled: boolean
  desktop_notifications: boolean
  email_notifications: boolean
}

export interface UserPreferences {
  theme: 'dark' | 'light'
  currency: 'USD' | 'EUR' | 'BTC' | 'ETH'
  notifications: NotificationConfig
  dashboard_layout: string[]
  auto_refresh_interval: number
  default_scan_config: ScanConfig
}

// API Response types
export interface ApiResponse<T = any> {
  status: 'success' | 'error'
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T = any> {
  status: 'success' | 'error'
  data: T[]
  total_count: number
  page: number
  per_page: number
  has_next: boolean
  has_prev: boolean
}

// Component prop types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface LoadingState {
  isLoading: boolean
  error?: string | null
}

export interface AsyncState<T> extends LoadingState {
  data?: T | null
}

// Store types
export interface ArbitrageStore {
  // State
  opportunities: ArbitrageOpportunity[]
  engineStatus: ArbitrageEngineStatus
  scanConfig: ScanConfig
  isScanning: boolean
  lastScanTime: string | null
  
  // Actions
  setOpportunities: (opportunities: ArbitrageOpportunity[]) => void
  addOpportunity: (opportunity: ArbitrageOpportunity) => void
  updateOpportunity: (id: string, updates: Partial<ArbitrageOpportunity>) => void
  removeOpportunity: (id: string) => void
  setEngineStatus: (status: ArbitrageEngineStatus) => void
  setScanConfig: (config: ScanConfig) => void
  setIsScanning: (isScanning: boolean) => void
  startScan: () => Promise<void>
  stopScan: () => Promise<void>
  refreshOpportunities: () => Promise<void>
}

export interface UIStore {
  // State
  sidebarOpen: boolean
  theme: 'dark' | 'light'
  notifications: any[]
  filters: FilterOptions
  selectedOpportunity: ArbitrageOpportunity | null
  
  // Actions
  toggleSidebar: () => void
  setTheme: (theme: 'dark' | 'light') => void
  addNotification: (notification: any) => void
  removeNotification: (id: string) => void
  setFilters: (filters: Partial<FilterOptions>) => void
  setSelectedOpportunity: (opportunity: ArbitrageOpportunity | null) => void
}

export interface WebSocketStore {
  // State
  isConnected: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  subscriptions: string[]
  lastMessage: WebSocketMessage | null
  
  // Actions
  connect: () => void
  disconnect: () => void
  subscribe: (topic: string) => void
  unsubscribe: (topic: string) => void
  sendMessage: (message: WebSocketMessage) => void
}
