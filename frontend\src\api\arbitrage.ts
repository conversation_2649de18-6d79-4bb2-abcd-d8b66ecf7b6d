import axios, { AxiosResponse } from 'axios'
import { 
  ScanConfig, 
  ValidationConfig, 
  ApiResponse, 
  ArbitrageOpportunity,
  ArbitrageEngineStatus,
  ChainConfig,
  GasEstimate
} from '@types/index'

// Create axios instance with default config
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('API Response Error:', error)
    
    if (error.response?.status === 401) {
      // Handle unauthorized access
      console.warn('Unauthorized access detected')
    } else if (error.response?.status >= 500) {
      // Handle server errors
      console.error('Server error detected')
    }
    
    return Promise.reject(error)
  }
)

export const arbitrageApi = {
  // Scan management
  async startScan(config: ScanConfig): Promise<ApiResponse<{ session_id: string }>> {
    try {
      const response: AxiosResponse<ApiResponse<{ session_id: string }>> = await api.post('/arbitrage/scan/start', config)
      return response.data
    } catch (error) {
      console.error('Failed to start scan:', error)
      throw error
    }
  },

  async stopScan(): Promise<ApiResponse> {
    try {
      const response: AxiosResponse<ApiResponse> = await api.post('/arbitrage/scan/stop')
      return response.data
    } catch (error) {
      console.error('Failed to stop scan:', error)
      throw error
    }
  },

  // Opportunities
  async getOpportunities(params: {
    limit?: number
    min_profit?: number
    sort_by?: string
  } = {}): Promise<ApiResponse<{
    opportunities: ArbitrageOpportunity[]
    total_count: number
    returned_count: number
    session_id: string | null
    is_scanning: boolean
  }>> {
    try {
      const response = await api.get('/arbitrage/opportunities', { params })
      return response.data
    } catch (error) {
      console.error('Failed to fetch opportunities:', error)
      throw error
    }
  },

  async validateOpportunity(
    opportunityId: string, 
    validationConfig?: ValidationConfig
  ): Promise<ApiResponse<{
    opportunity_id: string
    validation_result: any
  }>> {
    try {
      const response = await api.post(
        `/arbitrage/opportunities/${opportunityId}/validate`,
        validationConfig
      )
      return response.data
    } catch (error) {
      console.error('Failed to validate opportunity:', error)
      throw error
    }
  },

  // Engine status
  async getStatus(): Promise<ApiResponse<{
    engine_status: ArbitrageEngineStatus
  }>> {
    try {
      const response = await api.get('/arbitrage/status')
      return response.data
    } catch (error) {
      console.error('Failed to fetch status:', error)
      throw error
    }
  },

  // Chain management
  async getSupportedChains(): Promise<ApiResponse<{
    supported_chains: ChainConfig[]
    total_count: number
  }>> {
    try {
      const response = await api.get('/arbitrage/chains')
      return response.data
    } catch (error) {
      console.error('Failed to fetch supported chains:', error)
      throw error
    }
  },

  async getChainGasPrices(chainId: string): Promise<ApiResponse<{
    chain_id: string
    gas_estimate: GasEstimate
  }>> {
    try {
      const response = await api.get(`/arbitrage/chains/${chainId}/gas`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch gas prices:', error)
      throw error
    }
  },
}

export const analyticsApi = {
  // Analytics endpoints
  async getProfitAnalytics(timeframe: string = '24h'): Promise<ApiResponse<any>> {
    try {
      const response = await api.get('/analytics/profit', {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch profit analytics:', error)
      throw error
    }
  },

  async getNetworkAnalytics(): Promise<ApiResponse<any>> {
    try {
      const response = await api.get('/analytics/networks')
      return response.data
    } catch (error) {
      console.error('Failed to fetch network analytics:', error)
      throw error
    }
  },

  async getMLInsights(): Promise<ApiResponse<any>> {
    try {
      const response = await api.get('/analytics/ml-insights')
      return response.data
    } catch (error) {
      console.error('Failed to fetch ML insights:', error)
      throw error
    }
  },

  async getVolumeAnalytics(timeframe: string = '24h'): Promise<ApiResponse<any>> {
    try {
      const response = await api.get('/analytics/volume', {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch volume analytics:', error)
      throw error
    }
  },
}

export const tokenApi = {
  // Token information
  async getTokenInfo(tokenSymbol: string): Promise<ApiResponse<any>> {
    try {
      const response = await api.get(`/tokens/${tokenSymbol}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch token info:', error)
      throw error
    }
  },

  async getTokenPriceHistory(
    tokenSymbol: string, 
    timeframe: string = '24h'
  ): Promise<ApiResponse<any>> {
    try {
      const response = await api.get(`/tokens/${tokenSymbol}/price-history`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch token price history:', error)
      throw error
    }
  },

  async getTokenSentiment(tokenSymbol: string): Promise<ApiResponse<any>> {
    try {
      const response = await api.get(`/tokens/${tokenSymbol}/sentiment`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch token sentiment:', error)
      throw error
    }
  },

  async getTokenPatterns(tokenSymbol: string): Promise<ApiResponse<any>> {
    try {
      const response = await api.get(`/tokens/${tokenSymbol}/patterns`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch token patterns:', error)
      throw error
    }
  },
}

export const mlApi = {
  // Machine Learning endpoints
  async getPricePrediction(
    tokenSymbol: string,
    horizons: string[] = ['1h', '4h', '24h']
  ): Promise<ApiResponse<any>> {
    try {
      const response = await api.post('/ml/price-prediction', {
        token_symbol: tokenSymbol,
        prediction_horizons: horizons
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch price prediction:', error)
      throw error
    }
  },

  async getSentimentAnalysis(tokenSymbol: string): Promise<ApiResponse<any>> {
    try {
      const response = await api.get(`/ml/sentiment/${tokenSymbol}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch sentiment analysis:', error)
      throw error
    }
  },

  async getPatternAnalysis(tokenSymbol: string): Promise<ApiResponse<any>> {
    try {
      const response = await api.get(`/ml/patterns/${tokenSymbol}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch pattern analysis:', error)
      throw error
    }
  },

  async getMLModelStatus(): Promise<ApiResponse<any>> {
    try {
      const response = await api.get('/ml/status')
      return response.data
    } catch (error) {
      console.error('Failed to fetch ML model status:', error)
      throw error
    }
  },
}

export const configApi = {
  // Configuration endpoints
  async getDefaultScanConfig(): Promise<ApiResponse<ScanConfig>> {
    try {
      const response = await api.get('/config/scan-defaults')
      return response.data
    } catch (error) {
      console.error('Failed to fetch default scan config:', error)
      throw error
    }
  },

  async saveScanConfig(config: ScanConfig): Promise<ApiResponse> {
    try {
      const response = await api.post('/config/scan-config', config)
      return response.data
    } catch (error) {
      console.error('Failed to save scan config:', error)
      throw error
    }
  },

  async getValidationConfig(): Promise<ApiResponse<ValidationConfig>> {
    try {
      const response = await api.get('/config/validation-defaults')
      return response.data
    } catch (error) {
      console.error('Failed to fetch validation config:', error)
      throw error
    }
  },

  async saveValidationConfig(config: ValidationConfig): Promise<ApiResponse> {
    try {
      const response = await api.post('/config/validation-config', config)
      return response.data
    } catch (error) {
      console.error('Failed to save validation config:', error)
      throw error
    }
  },
}

// Export the main api instance for custom requests
export default api
