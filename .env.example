# Environment Configuration for Crypto Arbitrage Bot

# Application Settings
APP_NAME="Crypto Arbitrage Bot"
APP_VERSION="2.0.0"
DEBUG=false
HOST="0.0.0.0"
PORT=8000
RELOAD=false

# Database Configuration
DATABASE_URL="postgresql+asyncpg://user:password@localhost/arbitrage_bot"
DATABASE_ECHO=false

# Redis Cache Configuration
REDIS_URL="redis://localhost:6379/0"
CACHE_TTL=300

# API Configuration (All endpoints are public - no API keys required)
# Public API endpoints used:
# - CoinGecko: Free tier with 10-30 calls/minute
# - DexScreener: Free public API with 60 calls/minute
# - Binance: Public market data endpoints (1200 calls/minute)
# - Reddit: Public JSON endpoints (no authentication)
USE_PUBLIC_APIS_ONLY=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Scanning Configuration
DEFAULT_SCAN_INTERVAL=60
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30

# Arbitrage Settings
MIN_PROFIT_THRESHOLD=0.5
MAX_PROFIT_THRESHOLD=100.0
MIN_LIQUIDITY_USD=1000.0
MIN_VOLUME_24H=10000.0

# Supported Networks (comma-separated)
SUPPORTED_NETWORKS="ethereum,bsc,polygon,arbitrum,avalanche,solana"

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=100

# Logging Configuration
LOG_LEVEL="INFO"
LOG_FORMAT="json"

# Security
SECRET_KEY="your-secret-key-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Machine Learning
ML_MODEL_UPDATE_INTERVAL=3600
SENTIMENT_ANALYSIS_ENABLED=true
PATTERN_RECOGNITION_ENABLED=true
