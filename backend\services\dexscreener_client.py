"""
DexScreener API client implementation
"""
from typing import Dict, Any, List, Optional
from backend.services.api_client import BaseAPIClient
from backend.config.settings import settings


class DexScreenerClient(BaseAPIClient):
    """Client for DexScreener API"""
    
    def __init__(self):
        super().__init__(
            base_url="https://api.dexscreener.com/latest/dex",
            requests_per_minute=300,  # DexScreener is quite generous
            timeout=15
        )
    
    def get_auth_headers(self) -> Dict[str, str]:
        """DexScreener doesn't require authentication"""
        return {}
    
    async def search_pairs(self, query: str) -> Optional[List[Dict[str, Any]]]:
        """Search for trading pairs"""
        result = await self.get(f"/search", params={"q": query})
        return result.get("pairs", []) if result else []
    
    async def get_token_pairs(self, token_address: str) -> Optional[List[Dict[str, Any]]]:
        """Get all pairs for a specific token"""
        result = await self.get(f"/tokens/{token_address}")
        return result.get("pairs", []) if result else []
    
    async def get_pair_info(self, chain_id: str, pair_address: str) -> Optional[Dict[str, Any]]:
        """Get detailed information for a specific pair"""
        result = await self.get(f"/pairs/{chain_id}/{pair_address}")
        return result.get("pair") if result else None
    
    async def get_pairs_by_chain(self, chain_id: str) -> Optional[List[Dict[str, Any]]]:
        """Get pairs by blockchain"""
        result = await self.get(f"/pairs/{chain_id}")
        return result.get("pairs", []) if result else []
    
    async def get_trending_pairs(self) -> Optional[List[Dict[str, Any]]]:
        """Get trending pairs (if available)"""
        # DexScreener doesn't have a direct trending endpoint
        # We'll implement this by searching for high-volume tokens
        trending_queries = ["eth", "btc", "usdc", "usdt", "bnb"]
        all_pairs = []
        
        for query in trending_queries:
            pairs = await self.search_pairs(query)
            if pairs:
                # Filter for high volume and recent activity
                filtered_pairs = [
                    pair for pair in pairs
                    if (pair.get("volume", {}).get("h24", 0) > 100000 and
                        pair.get("txns", {}).get("h1", {}).get("buys", 0) > 10)
                ]
                all_pairs.extend(filtered_pairs[:10])  # Top 10 per query
        
        # Sort by volume and return top pairs
        all_pairs.sort(key=lambda x: x.get("volume", {}).get("h24", 0), reverse=True)
        return all_pairs[:50]  # Return top 50
    
    async def get_new_pairs(self, hours: int = 24) -> Optional[List[Dict[str, Any]]]:
        """Get newly created pairs"""
        # Search for various tokens and filter by creation time
        search_queries = ["new", "launch", "token"]
        all_pairs = []
        
        for query in search_queries:
            pairs = await self.search_pairs(query)
            if pairs:
                # Filter for recently created pairs
                import time
                cutoff_time = (time.time() - hours * 3600) * 1000  # Convert to milliseconds
                
                new_pairs = [
                    pair for pair in pairs
                    if pair.get("pairCreatedAt", 0) > cutoff_time
                ]
                all_pairs.extend(new_pairs)
        
        # Remove duplicates and sort by creation time
        seen_addresses = set()
        unique_pairs = []
        for pair in all_pairs:
            pair_addr = pair.get("pairAddress")
            if pair_addr and pair_addr not in seen_addresses:
                seen_addresses.add(pair_addr)
                unique_pairs.append(pair)
        
        unique_pairs.sort(key=lambda x: x.get("pairCreatedAt", 0), reverse=True)
        return unique_pairs[:100]  # Return top 100 newest
    
    async def validate_pair_data(self, pair: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize pair data"""
        validated_pair = {
            "pair_address": pair.get("pairAddress", ""),
            "chain_id": pair.get("chainId", ""),
            "dex_id": pair.get("dexId", ""),
            "url": pair.get("url", ""),
            "base_token": {
                "address": pair.get("baseToken", {}).get("address", ""),
                "name": pair.get("baseToken", {}).get("name", ""),
                "symbol": pair.get("baseToken", {}).get("symbol", "")
            },
            "quote_token": {
                "address": pair.get("quoteToken", {}).get("address", ""),
                "name": pair.get("quoteToken", {}).get("name", ""),
                "symbol": pair.get("quoteToken", {}).get("symbol", "")
            },
            "price_native": float(pair.get("priceNative", 0)),
            "price_usd": float(pair.get("priceUsd", 0)),
            "txns": {
                "m5": {
                    "buys": pair.get("txns", {}).get("m5", {}).get("buys", 0),
                    "sells": pair.get("txns", {}).get("m5", {}).get("sells", 0)
                },
                "h1": {
                    "buys": pair.get("txns", {}).get("h1", {}).get("buys", 0),
                    "sells": pair.get("txns", {}).get("h1", {}).get("sells", 0)
                },
                "h6": {
                    "buys": pair.get("txns", {}).get("h6", {}).get("buys", 0),
                    "sells": pair.get("txns", {}).get("h6", {}).get("sells", 0)
                },
                "h24": {
                    "buys": pair.get("txns", {}).get("h24", {}).get("buys", 0),
                    "sells": pair.get("txns", {}).get("h24", {}).get("sells", 0)
                }
            },
            "volume": {
                "h24": float(pair.get("volume", {}).get("h24", 0)),
                "h6": float(pair.get("volume", {}).get("h6", 0)),
                "h1": float(pair.get("volume", {}).get("h1", 0)),
                "m5": float(pair.get("volume", {}).get("m5", 0))
            },
            "price_change": {
                "m5": float(pair.get("priceChange", {}).get("m5", 0)),
                "h1": float(pair.get("priceChange", {}).get("h1", 0)),
                "h6": float(pair.get("priceChange", {}).get("h6", 0)),
                "h24": float(pair.get("priceChange", {}).get("h24", 0))
            },
            "liquidity": {
                "usd": float(pair.get("liquidity", {}).get("usd", 0)),
                "base": float(pair.get("liquidity", {}).get("base", 0)),
                "quote": float(pair.get("liquidity", {}).get("quote", 0))
            },
            "fdv": float(pair.get("fdv", 0)),
            "market_cap": float(pair.get("marketCap", 0)),
            "pair_created_at": pair.get("pairCreatedAt", 0),
            "info": pair.get("info", {}),
            "boosts": pair.get("boosts", {})
        }
        
        return validated_pair
    
    async def bulk_search(self, queries: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """Search multiple queries concurrently"""
        import asyncio
        
        async def search_single(query: str) -> tuple[str, List[Dict[str, Any]]]:
            pairs = await self.search_pairs(query)
            return query, pairs or []
        
        # Execute searches concurrently
        tasks = [search_single(query) for query in queries]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        search_results = {}
        for result in results:
            if isinstance(result, tuple):
                query, pairs = result
                search_results[query] = pairs
            else:
                # Handle exceptions
                self.logger.logger.error(f"Error in bulk search: {result}")
        
        return search_results
