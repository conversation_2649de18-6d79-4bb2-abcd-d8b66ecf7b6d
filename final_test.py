#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import json

async def final_v3_test():
    """Final verification of v3.0 features"""
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("🎯 FINAL v3.0 VERIFICATION TEST")
        print("=" * 50)
        
        # Test v3.0 performance metrics
        try:
            async with session.get(f"{base_url}/api/v3/performance-metrics") as response:
                if response.status == 200:
                    data = await response.json()
                    metrics = data.get('performance_metrics', {})
                    v3_features = metrics.get('v3_features', {})
                    
                    print("🚀 v3.0 Features Status:")
                    print(f"   Dynamic Discovery: {v3_features.get('dynamic_discovery_enabled', False)}")
                    print(f"   Tier Management: {v3_features.get('tier_management_enabled', False)}")
                    print(f"   Adaptive Thresholds: {v3_features.get('adaptive_thresholds_enabled', False)}")
                    print(f"   Max Tokens: {v3_features.get('max_total_tokens', 0)}")
                    
                    # Check tier performance
                    tier_perf = metrics.get('tier_performance', {})
                    if 'tier_stats' in tier_perf:
                        print(f"\n🎯 Tier Statistics:")
                        tier_stats = tier_perf['tier_stats']
                        total_tokens = tier_perf.get('total_tokens', 0)
                        print(f"   Total Tokens: {total_tokens}")
                        
                        for tier, stats in tier_stats.items():
                            token_count = stats.get('token_count', 0)
                            utilization = stats.get('utilization', 0)
                            print(f"   {tier}: {token_count} tokens ({utilization:.1f}% utilization)")
                    
                else:
                    print(f"❌ Performance metrics failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"❌ Performance metrics error: {e}")
        
        print("\n" + "=" * 50)
        
        # Test token distribution
        try:
            async with session.get(f"{base_url}/api/v3/token-distribution") as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'token_distribution' in data:
                        dist = data['token_distribution']
                        total = dist.get('total_tokens', 0)
                        blockchain_dist = dist.get('blockchain_distribution', {})
                        tier_dist = dist.get('tier_distribution', {})
                        
                        print("🌐 Token Distribution:")
                        print(f"   Total Tokens: {total}")
                        
                        print(f"\n   Blockchain Distribution:")
                        for blockchain, count in blockchain_dist.items():
                            percentage = (count / total * 100) if total > 0 else 0
                            print(f"      {blockchain.title()}: {count} ({percentage:.1f}%)")
                        
                        print(f"\n   Tier Distribution:")
                        for tier, count in tier_dist.items():
                            print(f"      {tier}: {count} tokens")
                        
                        # Check if we meet v3.0 targets
                        targets = dist.get('target_distribution', {})
                        print(f"\n   Target vs Actual:")
                        for blockchain, target in targets.items():
                            actual = blockchain_dist.get(blockchain, 0)
                            status = "✅" if actual >= target * 0.8 else "⚠️"  # 80% of target
                            print(f"      {blockchain.title()}: {actual}/{target} {status}")
                
                else:
                    print(f"❌ Token distribution failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"❌ Token distribution error: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 FINAL VERIFICATION COMPLETE!")
        print("✅ Enhanced Crypto Arbitrage Bot v3.0 is FULLY OPERATIONAL!")

if __name__ == "__main__":
    asyncio.run(final_v3_test())
