<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Enhanced Crypto Arbitrage Bot v3.0 - BOBACHEESE</title>

    <!-- Futuristic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;800&display=swap" rel="stylesheet">

    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>

    <!-- Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
            --bg-tertiary: #2a2a2a;
            --accent-cyan: #00ffff;
            --accent-purple: #8a2be2;
            --accent-green: #00ff41;
            --accent-red: #ff073a;
            --accent-orange: #ff8c00;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --text-muted: #666666;
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --shadow-glow: 0 0 20px rgba(0, 255, 255, 0.3);
            --shadow-purple: 0 0 20px rgba(138, 43, 226, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Exo 2', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            overflow-x: hidden;
            min-height: 100vh;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 255, 65, 0.05) 0%, transparent 50%);
        }

        .cyberpunk-grid {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: -1;
        }

        .header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow-glow);
        }

        .header h1 {
            font-family: 'Orbitron', monospace;
            font-weight: 900;
            font-size: 2rem;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
            animation: glow-pulse 2s ease-in-out infinite alternate;
        }

        @keyframes glow-pulse {
            from { filter: brightness(1); }
            to { filter: brightness(1.2); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .glass-panel {
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow-glow);
            transition: all 0.3s ease;
        }

        .glass-panel:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-purple);
        }

        .panel-title {
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: var(--accent-cyan);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--accent-green);
            animation: pulse 1.5s ease-in-out infinite;
        }

        .status-indicator.idle {
            background: var(--text-muted);
            animation: none;
        }

        .status-indicator.running {
            background: var(--accent-green);
        }

        .status-indicator.error {
            background: var(--accent-red);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .control-section {
            grid-column: 1 / -1;
        }

        .control-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .cyber-button {
            font-family: 'Orbitron', monospace;
            font-weight: 600;
            padding: 0.8rem 2rem;
            border: 2px solid var(--accent-cyan);
            background: transparent;
            color: var(--accent-cyan);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .cyber-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .cyber-button:hover::before {
            left: 100%;
        }

        .cyber-button:hover {
            background: rgba(0, 255, 255, 0.1);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .cyber-button.start {
            border-color: var(--accent-green);
            color: var(--accent-green);
        }

        .cyber-button.start:hover {
            background: rgba(0, 255, 65, 0.1);
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
        }

        .cyber-button.stop {
            border-color: var(--accent-red);
            color: var(--accent-red);
        }

        .cyber-button.stop:hover {
            background: rgba(255, 7, 58, 0.1);
            box-shadow: 0 0 20px rgba(255, 7, 58, 0.5);
        }

        .cyber-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Progress Bar Styles */
        .progress-section {
            margin-top: 2rem;
        }

        .progress-container {
            margin: 1rem 0;
        }

        .progress-bar {
            width: 100%;
            height: 30px;
            background: var(--bg-secondary);
            border-radius: 15px;
            border: 1px solid var(--glass-border);
            position: relative;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple));
            border-radius: 15px;
            transition: width 0.3s ease;
            width: 0%;
            position: relative;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            color: var(--text-primary);
            text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
        }

        .progress-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .progress-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem;
            background: var(--bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--glass-border);
        }

        .progress-item .label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .progress-item .value {
            color: var(--accent-cyan);
            font-family: 'Orbitron', monospace;
            font-weight: 600;
        }

        /* Parameter Control Styles */
        .parameter-section {
            margin-bottom: 2rem;
        }

        .parameter-section h4 {
            color: var(--accent-purple);
            font-family: 'Orbitron', monospace;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .parameter-group {
            margin-bottom: 1rem;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--glass-border);
        }

        .parameter-group label {
            display: block;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .range-inputs {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .range-inputs input[type="range"] {
            flex: 1;
            height: 6px;
            background: var(--bg-tertiary);
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }

        .range-inputs input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: var(--accent-cyan);
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }

        .range-inputs span {
            min-width: 50px;
            text-align: center;
            color: var(--accent-green);
            font-family: 'Orbitron', monospace;
            font-size: 0.8rem;
        }

        /* Toggle Switch Styles */
        .blockchain-toggles {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
        }

        .toggle-switch {
            position: relative;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: relative;
            width: 40px;
            height: 20px;
            background: var(--bg-tertiary);
            border-radius: 20px;
            transition: 0.3s;
            border: 1px solid var(--glass-border);
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            top: 1px;
            background: var(--text-muted);
            border-radius: 50%;
            transition: 0.3s;
        }

        .toggle-switch input:checked + .slider {
            background: var(--accent-green);
            box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
        }

        .toggle-switch input:checked + .slider:before {
            transform: translateX(18px);
            background: white;
        }

        /* Opportunities Display Styles */
        .opportunities-container {
            max-height: 400px;
            overflow-y: auto;
            padding: 1rem 0;
        }

        .no-opportunities {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
        }

        .pulse-icon {
            font-size: 3rem;
            animation: pulse 2s ease-in-out infinite;
        }

        .opportunity-card {
            background: var(--bg-secondary);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .opportunity-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-green), var(--accent-cyan));
        }

        .opportunity-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
        }

        .opportunity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .token-symbol {
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--accent-cyan);
        }

        .profit-badge {
            background: linear-gradient(45deg, var(--accent-green), var(--accent-cyan));
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-family: 'Orbitron', monospace;
            font-weight: 600;
            font-size: 0.9rem;
            box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
        }

        .opportunity-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
        }

        .detail-label {
            color: var(--text-secondary);
        }

        .detail-value {
            color: var(--text-primary);
            font-family: 'Orbitron', monospace;
        }

        .dex-links {
            margin-top: 1rem;
            display: flex;
            gap: 0.5rem;
        }

        .dex-link {
            flex: 1;
            padding: 0.5rem;
            background: var(--bg-tertiary);
            border: 1px solid var(--glass-border);
            border-radius: 5px;
            text-decoration: none;
            color: var(--accent-cyan);
            text-align: center;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .dex-link:hover {
            background: rgba(0, 255, 255, 0.1);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        /* Statistics Styles */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: 10px;
            border: 1px solid var(--glass-border);
        }

        .stat-value {
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--accent-green);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .best-opportunity {
            background: var(--bg-secondary);
            border-radius: 10px;
            border: 1px solid var(--glass-border);
            padding: 1rem;
        }

        .best-opportunity h4 {
            color: var(--accent-orange);
            font-family: 'Orbitron', monospace;
            margin-bottom: 1rem;
        }

        /* Logs Styles */
        .logs-container {
            max-height: 300px;
            overflow-y: auto;
            background: var(--bg-secondary);
            border-radius: 10px;
            border: 1px solid var(--glass-border);
            padding: 1rem;
            font-family: 'Courier New', monospace;
        }

        .log-entry {
            padding: 0.3rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            font-size: 0.9rem;
            animation: fadeInUp 0.3s ease;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .log-time {
            color: var(--text-muted);
            margin-right: 0.5rem;
        }

        .log-message {
            color: var(--text-primary);
        }

        .log-entry.info .log-message {
            color: var(--accent-cyan);
        }

        .log-entry.success .log-message {
            color: var(--accent-green);
        }

        .log-entry.warning .log-message {
            color: var(--accent-orange);
        }

        .log-entry.error .log-message {
            color: var(--accent-red);
        }

        .log-entry.validation .log-message {
            color: var(--accent-purple);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                padding: 1rem;
            }

            .control-buttons {
                flex-direction: column;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .opportunity-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="cyberpunk-grid"></div>

    <header class="header">
        <h1>🚀 Enhanced Crypto Arbitrage Bot v3.0</h1>
        <div style="text-align: center; margin-top: 0.5rem; font-family: 'Orbitron', monospace; color: var(--accent-purple);">
            by BOBACHEESE | Advanced Multi-Chain Arbitrage Detection
        </div>
    </header>

    <div class="container">
        <!-- Control Section -->
        <div class="glass-panel control-section">
            <div class="panel-title">
                <span class="status-indicator" id="statusIndicator"></span>
                🎮 Bot Control Center
            </div>

            <div class="control-buttons">
                <button class="cyber-button start" id="startBtn" onclick="startBot()">
                    ▶️ Start Bot
                </button>
                <button class="cyber-button stop" id="stopBtn" onclick="stopBot()" disabled>
                    ⏹️ Stop Bot
                </button>
                <button class="cyber-button" onclick="resetStats()">
                    🔄 Reset Stats
                </button>
            </div>

            <!-- Real-time Progress Display -->
            <div class="progress-section">
                <div class="panel-title">📊 Real-time Progress</div>

                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                        <div class="progress-text" id="progressText">0%</div>
                    </div>
                </div>

                <div class="progress-details">
                    <div class="progress-item">
                        <span class="label">Current Action:</span>
                        <span class="value" id="currentAction">Menunggu...</span>
                    </div>
                    <div class="progress-item">
                        <span class="label">Tokens Scanned:</span>
                        <span class="value" id="tokensScanned">0 / 0</span>
                    </div>
                    <div class="progress-item">
                        <span class="label">Speed:</span>
                        <span class="value" id="scanSpeed">0 tokens/sec</span>
                    </div>
                    <div class="progress-item">
                        <span class="label">API Calls:</span>
                        <span class="value" id="apiCalls">0</span>
                    </div>
                    <div class="progress-item">
                        <span class="label">Opportunities Found:</span>
                        <span class="value" id="opportunitiesFound">0</span>
                    </div>
                    <div class="progress-item">
                        <span class="label">ETA:</span>
                        <span class="value" id="estimatedTime">--</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Parameter Control Panel -->
        <div class="glass-panel">
            <div class="panel-title">⚙️ Real-time Parameters</div>

            <div class="parameter-section">
                <h4>💰 Profit Thresholds (%)</h4>

                <div class="parameter-group">
                    <label>Stablecoins (Min-Max):</label>
                    <div class="range-inputs">
                        <input type="range" min="0.1" max="2" step="0.1" value="0.1"
                               onchange="updateParameter('profit_threshold', 'stablecoins', 'min', this.value)">
                        <span id="stablecoin-min">0.1%</span>
                        <input type="range" min="0.1" max="2" step="0.1" value="0.7"
                               onchange="updateParameter('profit_threshold', 'stablecoins', 'max', this.value)">
                        <span id="stablecoin-max">0.7%</span>
                    </div>
                </div>

                <div class="parameter-group">
                    <label>Blue Chips (Min-Max):</label>
                    <div class="range-inputs">
                        <input type="range" min="0.1" max="3" step="0.1" value="0.3"
                               onchange="updateParameter('profit_threshold', 'blue_chips', 'min', this.value)">
                        <span id="bluechip-min">0.3%</span>
                        <input type="range" min="0.1" max="3" step="0.1" value="1.4"
                               onchange="updateParameter('profit_threshold', 'blue_chips', 'max', this.value)">
                        <span id="bluechip-max">1.4%</span>
                    </div>
                </div>

                <div class="parameter-group">
                    <label>DeFi Tokens (Min-Max):</label>
                    <div class="range-inputs">
                        <input type="range" min="0.1" max="5" step="0.1" value="0.7"
                               onchange="updateParameter('profit_threshold', 'defi', 'min', this.value)">
                        <span id="defi-min">0.7%</span>
                        <input type="range" min="0.1" max="5" step="0.1" value="2.1"
                               onchange="updateParameter('profit_threshold', 'defi', 'max', this.value)">
                        <span id="defi-max">2.1%</span>
                    </div>
                </div>

                <div class="parameter-group">
                    <label>Meme Coins (Min-Max):</label>
                    <div class="range-inputs">
                        <input type="range" min="0.5" max="10" step="0.1" value="1.0"
                               onchange="updateParameter('profit_threshold', 'meme_coins', 'min', this.value)">
                        <span id="meme-min">1.0%</span>
                        <input type="range" min="0.5" max="10" step="0.1" value="5.6"
                               onchange="updateParameter('profit_threshold', 'meme_coins', 'max', this.value)">
                        <span id="meme-max">5.6%</span>
                    </div>
                </div>
            </div>

            <div class="parameter-section">
                <h4>⏱️ Scan Intervals (seconds)</h4>

                <div class="parameter-group">
                    <label>Priority Tier:</label>
                    <input type="range" min="10" max="120" step="10" value="30"
                           onchange="updateParameter('scan_interval', 'priority_tier', null, this.value)">
                    <span id="priority-interval">30s</span>
                </div>

                <div class="parameter-group">
                    <label>Regular Tier:</label>
                    <input type="range" min="60" max="300" step="30" value="120"
                           onchange="updateParameter('scan_interval', 'regular_tier', null, this.value)">
                    <span id="regular-interval">120s</span>
                </div>

                <div class="parameter-group">
                    <label>Discovery Tier:</label>
                    <input type="range" min="180" max="600" step="60" value="300"
                           onchange="updateParameter('scan_interval', 'discovery_tier', null, this.value)">
                    <span id="discovery-interval">300s</span>
                </div>
            </div>

            <div class="parameter-section">
                <h4>🔗 Blockchain Selection</h4>

                <div class="blockchain-toggles">
                    <label class="toggle-switch">
                        <input type="checkbox" checked onchange="updateParameter('blockchain', 'ethereum', null, this.checked)">
                        <span class="slider"></span>
                        Ethereum
                    </label>
                    <label class="toggle-switch">
                        <input type="checkbox" checked onchange="updateParameter('blockchain', 'solana', null, this.checked)">
                        <span class="slider"></span>
                        Solana
                    </label>
                    <label class="toggle-switch">
                        <input type="checkbox" checked onchange="updateParameter('blockchain', 'bsc', null, this.checked)">
                        <span class="slider"></span>
                        BSC
                    </label>
                    <label class="toggle-switch">
                        <input type="checkbox" checked onchange="updateParameter('blockchain', 'polygon', null, this.checked)">
                        <span class="slider"></span>
                        Polygon
                    </label>
                    <label class="toggle-switch">
                        <input type="checkbox" checked onchange="updateParameter('blockchain', 'arbitrum', null, this.checked)">
                        <span class="slider"></span>
                        Arbitrum
                    </label>
                </div>
            </div>
        </div>

        <!-- Arbitrage Opportunities Display -->
        <div class="glass-panel">
            <div class="panel-title">💎 Live Arbitrage Opportunities</div>

            <div class="opportunities-container" id="opportunitiesContainer">
                <div class="no-opportunities">
                    <div class="pulse-icon">🔍</div>
                    <p>Menunggu peluang arbitrase...</p>
                </div>
            </div>
        </div>

        <!-- Statistics Panel -->
        <div class="glass-panel">
            <div class="panel-title">📈 Bot Statistics</div>

            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalScans">0</div>
                    <div class="stat-label">Total Scans</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalOpportunities">0</div>
                    <div class="stat-label">Opportunities Found</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgProfit">0%</div>
                    <div class="stat-label">Avg Profit</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="uptime">00:00:00</div>
                    <div class="stat-label">Uptime</div>
                </div>
            </div>

            <div class="best-opportunity" id="bestOpportunity">
                <h4>🏆 Best Opportunity</h4>
                <div class="best-opp-content">
                    <p>Belum ada data</p>
                </div>
            </div>
        </div>

        <!-- Real-time Logs -->
        <div class="glass-panel" style="grid-column: 1 / -1;">
            <div class="panel-title">📋 Real-time Logs</div>

            <div class="logs-container" id="logsContainer">
                <div class="log-entry info">
                    <span class="log-time">[00:00:00]</span>
                    <span class="log-message">🚀 Enhanced Crypto Arbitrage Bot v3.0 siap digunakan</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket Connection
        const socket = io();

        // UI State Management
        let isRunning = false;
        let currentParameters = {};

        // Initialize WebSocket Event Listeners
        socket.on('connect', function() {
            console.log('Connected to WebSocket');
            addLogEntry('🔗 Terhubung ke server', 'info');
        });

        socket.on('disconnect', function() {
            console.log('Disconnected from WebSocket');
            addLogEntry('🔌 Terputus dari server', 'warning');
        });

        socket.on('new_log', function(logData) {
            addLogEntry(logData.message, logData.level);
        });

        socket.on('progress_update', function(progressData) {
            updateProgressDisplay(progressData);
        });

        socket.on('status_update', function(statusData) {
            updateBotStatus(statusData);
        });

        socket.on('parameter_updated', function(paramData) {
            addLogEntry(`⚙️ Parameter updated: ${paramData.type}.${paramData.key}`, 'success');
        });

        socket.on('new_opportunities', function(opportunities) {
            displayOpportunities(opportunities);
        });

        // Bot Control Functions
        function startBot() {
            if (!isRunning) {
                socket.emit('start_bot');
                updateButtonStates(true);
                addLogEntry('🚀 Memulai bot...', 'info');
            }
        }

        function stopBot() {
            if (isRunning) {
                socket.emit('stop_bot');
                updateButtonStates(false);
                addLogEntry('🛑 Menghentikan bot...', 'warning');
            }
        }

        function resetStats() {
            // Reset local statistics display
            document.getElementById('totalScans').textContent = '0';
            document.getElementById('totalOpportunities').textContent = '0';
            document.getElementById('avgProfit').textContent = '0%';
            document.getElementById('uptime').textContent = '00:00:00';

            // Clear opportunities
            const container = document.getElementById('opportunitiesContainer');
            container.innerHTML = `
                <div class="no-opportunities">
                    <div class="pulse-icon">🔍</div>
                    <p>Menunggu peluang arbitrase...</p>
                </div>
            `;

            addLogEntry('🔄 Statistik direset', 'info');
        }

        // Parameter Update Function
        function updateParameter(type, key, subkey, value) {
            const paramData = {
                type: type,
                key: key,
                value: value
            };

            if (subkey) {
                paramData.category = key;
                paramData.threshold_type = subkey;
            }

            socket.emit('update_parameters', paramData);

            // Update UI display
            updateParameterDisplay(type, key, subkey, value);
        }

        function updateParameterDisplay(type, key, subkey, value) {
            if (type === 'profit_threshold') {
                const elementId = `${key.replace('_', '')}-${subkey}`;
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = `${value}%`;
                }
            } else if (type === 'scan_interval') {
                const elementId = `${key.replace('_', '')}-interval`;
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = `${value}s`;
                }
            }
        }

        // UI Update Functions
        function updateButtonStates(running) {
            isRunning = running;
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const statusIndicator = document.getElementById('statusIndicator');

            if (running) {
                startBtn.disabled = true;
                stopBtn.disabled = false;
                statusIndicator.className = 'status-indicator running';
            } else {
                startBtn.disabled = false;
                stopBtn.disabled = true;
                statusIndicator.className = 'status-indicator idle';
            }
        }

        function updateProgressDisplay(progressData) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const currentAction = document.getElementById('currentAction');
            const tokensScanned = document.getElementById('tokensScanned');
            const scanSpeed = document.getElementById('scanSpeed');
            const apiCalls = document.getElementById('apiCalls');
            const opportunitiesFound = document.getElementById('opportunitiesFound');
            const estimatedTime = document.getElementById('estimatedTime');

            progressFill.style.width = `${progressData.percentage}%`;
            progressText.textContent = `${Math.round(progressData.percentage)}%`;
            currentAction.textContent = progressData.current_action;
            tokensScanned.textContent = `${progressData.tokens_scanned} / ${progressData.total_tokens}`;
            scanSpeed.textContent = `${progressData.tokens_per_second} tokens/sec`;
            apiCalls.textContent = progressData.api_calls_made;
            opportunitiesFound.textContent = progressData.opportunities_found;

            if (progressData.estimated_time_remaining > 0) {
                const minutes = Math.floor(progressData.estimated_time_remaining / 60);
                const seconds = progressData.estimated_time_remaining % 60;
                estimatedTime.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            } else {
                estimatedTime.textContent = '--';
            }
        }

        function updateBotStatus(statusData) {
            isRunning = statusData.is_running;
            currentParameters = statusData.parameters;
            updateButtonStates(isRunning);

            // Update statistics
            if (statusData.statistics) {
                const stats = statusData.statistics;
                document.getElementById('totalScans').textContent = stats.total_scans || '0';
                document.getElementById('totalOpportunities').textContent = stats.total_opportunities || '0';
                document.getElementById('avgProfit').textContent = `${stats.avg_profit_percentage || 0}%`;

                if (stats.uptime_seconds) {
                    const hours = Math.floor(stats.uptime_seconds / 3600);
                    const minutes = Math.floor((stats.uptime_seconds % 3600) / 60);
                    const seconds = stats.uptime_seconds % 60;
                    document.getElementById('uptime').textContent =
                        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            }
        }

        // Opportunities Display Functions
        function displayOpportunities(opportunities) {
            const container = document.getElementById('opportunitiesContainer');

            if (opportunities.length === 0) {
                container.innerHTML = `
                    <div class="no-opportunities">
                        <div class="pulse-icon">🔍</div>
                        <p>Menunggu peluang arbitrase...</p>
                    </div>
                `;
                return;
            }

            let html = '';
            opportunities.forEach(opp => {
                html += createOpportunityCard(opp);
            });

            container.innerHTML = html;
        }

        function createOpportunityCard(opportunity) {
            const profitClass = opportunity.profit_percentage > 5 ? 'high-profit' :
                               opportunity.profit_percentage > 2 ? 'medium-profit' : 'low-profit';

            return `
                <div class="opportunity-card ${profitClass}">
                    <div class="opportunity-header">
                        <div class="token-symbol">${opportunity.token_symbol}</div>
                        <div class="profit-badge">${opportunity.profit_percentage}%</div>
                    </div>

                    <div class="validated-pair" style="color: var(--accent-purple); font-size: 0.9rem; margin-bottom: 0.5rem;">
                        ✅ Validated Pair: ${opportunity.validated_pair || 'N/A'}
                    </div>

                    <div class="opportunity-details">
                        <div class="detail-item">
                            <span class="detail-label">Buy Price:</span>
                            <span class="detail-value">$${opportunity.buy_price}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Sell Price:</span>
                            <span class="detail-value">$${opportunity.sell_price}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Buy DEX:</span>
                            <span class="detail-value">${opportunity.buy_dex_name}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Sell DEX:</span>
                            <span class="detail-value">${opportunity.sell_dex_name}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Liquidity:</span>
                            <span class="detail-value">$${formatNumber(opportunity.min_liquidity)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Status:</span>
                            <span class="detail-value" style="color: var(--accent-green);">${opportunity.validation_status || '✅ VALIDATED'}</span>
                        </div>
                    </div>

                    <div class="dex-links">
                        <a href="${opportunity.buy_dexscreener_link}" target="_blank" class="dex-link">
                            📊 Buy on ${opportunity.buy_dex_name}
                        </a>
                        <a href="${opportunity.sell_dexscreener_link}" target="_blank" class="dex-link">
                            📊 Sell on ${opportunity.sell_dex_name}
                        </a>
                    </div>
                </div>
            `;
        }

        // Logs Management
        function addLogEntry(message, level = 'info') {
            const logsContainer = document.getElementById('logsContainer');
            const timestamp = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.innerHTML = `
                <span class="log-time">[${timestamp}]</span>
                <span class="log-message">${message}</span>
            `;

            // Insert at the beginning
            logsContainer.insertBefore(logEntry, logsContainer.firstChild);

            // Keep only latest 100 logs
            while (logsContainer.children.length > 100) {
                logsContainer.removeChild(logsContainer.lastChild);
            }
        }

        // Utility Functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toFixed(0);
        }

        // Initialize UI on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateButtonStates(false);
            addLogEntry('🚀 Enhanced Crypto Arbitrage Bot v3.0 siap digunakan', 'info');
            addLogEntry('💡 Gunakan panel kontrol untuk mengatur parameter secara real-time', 'info');
            addLogEntry('🔍 Bot akan mencari peluang arbitrase dengan validasi pair yang ketat', 'info');
        });

        // Auto-scroll logs to top when new entries are added
        const logsContainer = document.getElementById('logsContainer');
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    logsContainer.scrollTop = 0;
                }
            });
        });
        observer.observe(logsContainer, { childList: true });
    </script>
</body>
</html>