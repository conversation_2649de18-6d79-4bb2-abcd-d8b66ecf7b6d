"""
Machine Learning price prediction models
"""
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import os
from datetime import datetime, timedelta
from backend.core.logging import get_logger
from backend.core.cache import cache
import ta  # Technical Analysis library

logger = get_logger(__name__)


class TechnicalIndicators:
    """Calculate technical indicators for price prediction"""
    
    @staticmethod
    def calculate_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """Calculate various technical indicators"""
        if len(df) < 20:  # Need minimum data for indicators
            return df
        
        # Price-based indicators
        df['sma_5'] = ta.trend.sma_indicator(df['close'], window=5)
        df['sma_10'] = ta.trend.sma_indicator(df['close'], window=10)
        df['sma_20'] = ta.trend.sma_indicator(df['close'], window=20)
        df['ema_12'] = ta.trend.ema_indicator(df['close'], window=12)
        df['ema_26'] = ta.trend.ema_indicator(df['close'], window=26)
        
        # MACD
        df['macd'] = ta.trend.macd_diff(df['close'])
        df['macd_signal'] = ta.trend.macd_signal(df['close'])
        
        # RSI
        df['rsi'] = ta.momentum.rsi(df['close'], window=14)
        
        # Bollinger Bands
        bb = ta.volatility.BollingerBands(df['close'])
        df['bb_upper'] = bb.bollinger_hband()
        df['bb_middle'] = bb.bollinger_mavg()
        df['bb_lower'] = bb.bollinger_lband()
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Volume indicators
        if 'volume' in df.columns:
            df['volume_sma'] = ta.volume.volume_sma(df['close'], df['volume'], window=10)
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            df['obv'] = ta.volume.on_balance_volume(df['close'], df['volume'])
        
        # Volatility
        df['atr'] = ta.volatility.average_true_range(df['high'], df['low'], df['close'])
        
        # Price momentum
        df['momentum_5'] = df['close'] / df['close'].shift(5) - 1
        df['momentum_10'] = df['close'] / df['close'].shift(10) - 1
        
        # Price position relative to recent high/low
        df['high_20'] = df['high'].rolling(window=20).max()
        df['low_20'] = df['low'].rolling(window=20).min()
        df['price_position'] = (df['close'] - df['low_20']) / (df['high_20'] - df['low_20'])
        
        return df
    
    @staticmethod
    def create_features(df: pd.DataFrame) -> pd.DataFrame:
        """Create feature matrix for ML models"""
        features = df.copy()
        
        # Calculate returns
        features['return_1h'] = features['close'].pct_change(1)
        features['return_4h'] = features['close'].pct_change(4)
        features['return_24h'] = features['close'].pct_change(24)
        
        # Volatility features
        features['volatility_1h'] = features['return_1h'].rolling(window=24).std()
        features['volatility_24h'] = features['return_1h'].rolling(window=24*7).std()
        
        # Volume features
        if 'volume' in features.columns:
            features['volume_change'] = features['volume'].pct_change(1)
            features['volume_ma_ratio'] = features['volume'] / features['volume'].rolling(window=24).mean()
        
        # Time-based features
        features['hour'] = pd.to_datetime(features.index).hour
        features['day_of_week'] = pd.to_datetime(features.index).dayofweek
        features['is_weekend'] = features['day_of_week'].isin([5, 6]).astype(int)
        
        # Lag features
        for lag in [1, 2, 3, 6, 12, 24]:
            features[f'close_lag_{lag}'] = features['close'].shift(lag)
            features[f'volume_lag_{lag}'] = features['volume'].shift(lag) if 'volume' in features.columns else 0
        
        return features


class PricePredictionModel:
    """Machine Learning model for price prediction"""
    
    def __init__(self, model_type: str = "random_forest"):
        self.model_type = model_type
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.is_trained = False
        self.model_path = f"models/price_predictor_{model_type}.joblib"
        self.scaler_path = f"models/price_predictor_scaler_{model_type}.joblib"
        
        # Create models directory
        os.makedirs("models", exist_ok=True)
        
        # Initialize model
        if model_type == "random_forest":
            self.model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
        elif model_type == "gradient_boosting":
            self.model = GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )
        else:
            raise ValueError(f"Unsupported model type: {model_type}")
    
    def prepare_data(self, df: pd.DataFrame, target_hours: int = 1) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for training/prediction"""
        # Calculate technical indicators
        df_with_indicators = TechnicalIndicators.calculate_indicators(df)
        
        # Create features
        features_df = TechnicalIndicators.create_features(df_with_indicators)
        
        # Create target variable (future price change)
        features_df['target'] = features_df['close'].shift(-target_hours) / features_df['close'] - 1
        
        # Select feature columns (exclude non-numeric and target)
        numeric_columns = features_df.select_dtypes(include=[np.number]).columns
        feature_columns = [col for col in numeric_columns if col not in ['target', 'close']]
        
        # Remove rows with NaN values
        clean_df = features_df[feature_columns + ['target']].dropna()
        
        if len(clean_df) < 50:  # Need minimum data for training
            raise ValueError("Insufficient data for training")
        
        X = clean_df[feature_columns].values
        y = clean_df['target'].values
        
        self.feature_columns = feature_columns
        
        return X, y
    
    def train(self, df: pd.DataFrame, target_hours: int = 1) -> Dict[str, float]:
        """Train the model"""
        try:
            X, y = self.prepare_data(df, target_hours)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=False
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train model
            self.model.fit(X_train_scaled, y_train)
            
            # Evaluate
            y_pred = self.model.predict(X_test_scaled)
            
            metrics = {
                "mse": mean_squared_error(y_test, y_pred),
                "mae": mean_absolute_error(y_test, y_pred),
                "r2": r2_score(y_test, y_pred),
                "training_samples": len(X_train),
                "test_samples": len(X_test)
            }
            
            self.is_trained = True
            
            # Save model
            self.save_model()
            
            logger.info(f"Model trained successfully. R² Score: {metrics['r2']:.4f}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error training model: {e}")
            raise
    
    def predict(self, df: pd.DataFrame, target_hours: int = 1) -> Dict[str, Any]:
        """Make price predictions"""
        if not self.is_trained:
            self.load_model()
        
        if not self.is_trained:
            raise ValueError("Model is not trained")
        
        try:
            # Prepare features for the latest data point
            df_with_indicators = TechnicalIndicators.calculate_indicators(df)
            features_df = TechnicalIndicators.create_features(df_with_indicators)
            
            # Get the latest row
            latest_features = features_df[self.feature_columns].iloc[-1:].values
            
            # Handle NaN values
            if np.isnan(latest_features).any():
                # Fill NaN with column means from training data
                latest_features = np.nan_to_num(latest_features, nan=0.0)
            
            # Scale features
            latest_features_scaled = self.scaler.transform(latest_features)
            
            # Make prediction
            prediction = self.model.predict(latest_features_scaled)[0]
            
            # Calculate confidence based on feature importance and data quality
            confidence = self._calculate_prediction_confidence(latest_features, df)
            
            current_price = df['close'].iloc[-1]
            predicted_price = current_price * (1 + prediction)
            
            return {
                "current_price": current_price,
                "predicted_price": predicted_price,
                "predicted_change_percentage": prediction * 100,
                "confidence_score": confidence,
                "prediction_horizon_hours": target_hours,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error making prediction: {e}")
            raise
    
    def _calculate_prediction_confidence(self, features: np.ndarray, df: pd.DataFrame) -> float:
        """Calculate confidence score for prediction"""
        confidence_factors = []
        
        # Data quality factor
        data_completeness = 1 - (np.isnan(features).sum() / features.size)
        confidence_factors.append(data_completeness * 0.3)
        
        # Data recency factor (more recent data = higher confidence)
        if len(df) >= 24:
            confidence_factors.append(0.2)
        elif len(df) >= 12:
            confidence_factors.append(0.15)
        else:
            confidence_factors.append(0.1)
        
        # Volatility factor (lower volatility = higher confidence)
        if len(df) >= 24:
            recent_volatility = df['close'].pct_change().tail(24).std()
            volatility_factor = max(0, 0.2 - recent_volatility * 10)  # Normalize volatility
            confidence_factors.append(volatility_factor)
        
        # Model performance factor (if available)
        confidence_factors.append(0.3)  # Base model confidence
        
        return min(1.0, sum(confidence_factors))
    
    def save_model(self):
        """Save trained model and scaler"""
        if self.is_trained:
            joblib.dump(self.model, self.model_path)
            joblib.dump(self.scaler, self.scaler_path)
            
            # Save feature columns
            feature_path = f"models/features_{self.model_type}.joblib"
            joblib.dump(self.feature_columns, feature_path)
            
            logger.info(f"Model saved to {self.model_path}")
    
    def load_model(self):
        """Load trained model and scaler"""
        try:
            if os.path.exists(self.model_path) and os.path.exists(self.scaler_path):
                self.model = joblib.load(self.model_path)
                self.scaler = joblib.load(self.scaler_path)
                
                # Load feature columns
                feature_path = f"models/features_{self.model_type}.joblib"
                if os.path.exists(feature_path):
                    self.feature_columns = joblib.load(feature_path)
                
                self.is_trained = True
                logger.info(f"Model loaded from {self.model_path}")
            else:
                logger.warning(f"Model files not found: {self.model_path}")
        except Exception as e:
            logger.error(f"Error loading model: {e}")
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance from trained model"""
        if not self.is_trained or not hasattr(self.model, 'feature_importances_'):
            return {}
        
        importance_dict = {}
        for i, feature in enumerate(self.feature_columns):
            importance_dict[feature] = float(self.model.feature_importances_[i])
        
        # Sort by importance
        return dict(sorted(importance_dict.items(), key=lambda x: x[1], reverse=True))


class PricePredictionService:
    """Service for managing multiple prediction models"""
    
    def __init__(self):
        self.models = {
            "1h": PricePredictionModel("random_forest"),
            "4h": PricePredictionModel("gradient_boosting"),
            "24h": PricePredictionModel("random_forest")
        }
        self.last_training = {}
    
    async def get_price_prediction(
        self, 
        token_symbol: str, 
        price_data: List[Dict[str, Any]],
        prediction_horizons: List[str] = ["1h", "4h", "24h"]
    ) -> Dict[str, Any]:
        """Get price predictions for multiple time horizons"""
        
        cache_key = f"price_prediction:{token_symbol}:{len(price_data)}"
        cached_result = await cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # Convert price data to DataFrame
        df = pd.DataFrame(price_data)
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
        
        # Ensure required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                if col == 'volume':
                    df[col] = 0  # Default volume if not available
                else:
                    logger.error(f"Missing required column: {col}")
                    return {"error": f"Missing required column: {col}"}
        
        predictions = {}
        
        for horizon in prediction_horizons:
            if horizon not in self.models:
                continue
            
            try:
                model = self.models[horizon]
                target_hours = int(horizon.rstrip('h'))
                
                # Check if model needs training
                if not model.is_trained or self._needs_retraining(token_symbol, horizon):
                    if len(df) >= 100:  # Need sufficient data for training
                        logger.info(f"Training {horizon} model for {token_symbol}")
                        metrics = model.train(df, target_hours)
                        self.last_training[f"{token_symbol}_{horizon}"] = datetime.utcnow()
                        predictions[horizon] = {
                            "status": "model_trained",
                            "metrics": metrics
                        }
                    else:
                        predictions[horizon] = {
                            "status": "insufficient_data",
                            "required_samples": 100,
                            "available_samples": len(df)
                        }
                        continue
                
                # Make prediction
                prediction = model.predict(df, target_hours)
                predictions[horizon] = {
                    "status": "success",
                    **prediction
                }
                
            except Exception as e:
                logger.error(f"Error predicting {horizon} for {token_symbol}: {e}")
                predictions[horizon] = {
                    "status": "error",
                    "error": str(e)
                }
        
        result = {
            "token_symbol": token_symbol,
            "predictions": predictions,
            "data_points": len(df),
            "generated_at": datetime.utcnow().isoformat()
        }
        
        # Cache for 5 minutes
        await cache.set(cache_key, result, ttl=300)
        
        return result
    
    def _needs_retraining(self, token_symbol: str, horizon: str) -> bool:
        """Check if model needs retraining"""
        key = f"{token_symbol}_{horizon}"
        if key not in self.last_training:
            return True
        
        # Retrain every 24 hours
        time_since_training = datetime.utcnow() - self.last_training[key]
        return time_since_training > timedelta(hours=24)


# Global price prediction service
price_prediction_service = PricePredictionService()
