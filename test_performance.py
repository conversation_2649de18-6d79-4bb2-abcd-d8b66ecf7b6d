#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import json
import time

async def test_performance_metrics():
    """Test v3.0 performance metrics endpoint"""
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("🚀 Testing Enhanced Crypto Arbitrage Bot v3.0 Performance Metrics\n")
        
        # Wait for server to be ready
        print("⏳ Waiting for server to be ready...")
        for i in range(10):
            try:
                async with session.get(f"{base_url}/api/status") as response:
                    if response.status == 200:
                        print("✅ Server is ready!")
                        break
            except:
                pass
            await asyncio.sleep(2)
        
        # Test performance metrics endpoint
        print("\n📊 Testing performance metrics endpoint...")
        
        try:
            async with session.get(f"{base_url}/api/v3/performance-metrics") as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ Performance metrics endpoint working!")
                    
                    if 'performance_metrics' in data:
                        metrics = data['performance_metrics']
                        
                        # Scan performance
                        scan_perf = metrics.get('scan_performance', {})
                        print(f"\n⚡ Scan Performance:")
                        print(f"   Total tokens: {scan_perf.get('total_tokens', 0)}")
                        print(f"   Tokens/second: {scan_perf.get('tokens_per_second', 0):.1f}")
                        print(f"   Cache hit rate: {scan_perf.get('cache_hit_rate', 0):.1f}%")
                        print(f"   Success rate: {scan_perf.get('success_rate', 0):.1f}%")
                        print(f"   Scan duration: {scan_perf.get('scan_duration', 0):.2f}s")
                        
                        # Tier performance
                        tier_perf = metrics.get('tier_performance', {})
                        if 'tier_stats' in tier_perf:
                            print(f"\n🎯 Tier Performance:")
                            tier_stats = tier_perf['tier_stats']
                            for tier, stats in tier_stats.items():
                                print(f"   {tier}: {stats.get('token_count', 0)} tokens, {stats.get('utilization', 0):.1f}% utilization")
                        
                        # v3.0 features
                        v3_features = metrics.get('v3_features', {})
                        print(f"\n🚀 v3.0 Features Status:")
                        print(f"   Dynamic discovery: {'✅' if v3_features.get('dynamic_discovery_enabled', False) else '❌'}")
                        print(f"   Tier management: {'✅' if v3_features.get('tier_management_enabled', False) else '❌'}")
                        print(f"   Adaptive thresholds: {'✅' if v3_features.get('adaptive_thresholds_enabled', False) else '❌'}")
                        print(f"   Max tokens: {v3_features.get('max_total_tokens', 0)}")
                        
                        # Threshold performance
                        threshold_perf = metrics.get('threshold_performance', {})
                        if threshold_perf:
                            print(f"\n📈 Threshold Performance:")
                            print(f"   Total categories: {threshold_perf.get('total_categories', 0)}")
                            print(f"   Custom overrides: {threshold_perf.get('custom_overrides_count', 0)}")
                            print(f"   Current volatility: {threshold_perf.get('current_volatility', 'unknown')}")
                        
                        # Discovery performance
                        discovery_perf = metrics.get('discovery_performance', {})
                        if discovery_perf:
                            print(f"\n🔍 Discovery Performance:")
                            print(f"   Total discovered: {discovery_perf.get('total_discovered', 0)}")
                            by_chain = discovery_perf.get('by_chain', {})
                            for chain, count in by_chain.items():
                                print(f"   {chain}: {count} tokens")
                
                else:
                    print(f"❌ Performance metrics failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"❌ Performance metrics error: {e}")
        
        print("\n🎯 Performance Metrics Test Complete!")

if __name__ == "__main__":
    asyncio.run(test_performance_metrics())
