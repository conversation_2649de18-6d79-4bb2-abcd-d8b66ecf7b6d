version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: arbitrage_postgres
    environment:
      POSTGRES_DB: arbitrage_bot
      POSTGRES_USER: arbitrage_user
      POSTGRES_PASSWORD: arbitrage_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - arbitrage_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U arbitrage_user -d arbitrage_bot"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: arbitrage_redis
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - arbitrage_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: arbitrage_backend
    environment:
      - DATABASE_URL=postgresql+asyncpg://arbitrage_user:arbitrage_password@postgres:5432/arbitrage_bot
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
    volumes:
      - ./backend:/app/backend
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    networks:
      - arbitrage_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: arbitrage_frontend
    environment:
      - VITE_API_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:8000
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
    ports:
      - "3000:3000"
    networks:
      - arbitrage_network
    depends_on:
      - backend
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: arbitrage_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - arbitrage_network
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: arbitrage_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - arbitrage_network
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: arbitrage_grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=grafana_password
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3001:3000"
    networks:
      - arbitrage_network
    depends_on:
      - prometheus
    restart: unless-stopped

  # ML Model Server (Optional)
  ml_server:
    build:
      context: ./ml_server
      dockerfile: Dockerfile
    container_name: arbitrage_ml_server
    environment:
      - MODEL_PATH=/app/models
      - REDIS_URL=redis://:redis_password@redis:6379/1
    volumes:
      - ./ml_models:/app/models
      - ml_cache:/app/cache
    ports:
      - "8001:8001"
    networks:
      - arbitrage_network
    depends_on:
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  ml_cache:
    driver: local

networks:
  arbitrage_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
