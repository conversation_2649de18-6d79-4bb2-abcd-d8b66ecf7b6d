#!/usr/bin/env python3
"""
Conda Setup Script for Crypto Arbitrage Bot v2.0
Optimized for Windows 11 with Miniconda/Anaconda
"""
import subprocess
import sys
import os
import time

def print_banner():
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🐍 Crypto Arbitrage Bot v2.0 - Conda Setup          ║
    ║                                                              ║
    ║        ✅ Pre-compiled Packages                              ║
    ║        ✅ No Compilation Issues                              ║
    ║        ✅ Windows 11 Optimized                               ║
    ║        ✅ Full ML Support                                    ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def run_command(command, description, timeout=300):
    """Run a command with error handling and timeout"""
    print(f"🔄 {description}...")
    
    try:
        # Use shell=True for Windows conda commands
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        
        if result.returncode == 0:
            print(f"✅ {description} - Success")
            return True
        else:
            print(f"❌ {description} - Failed")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - Timeout after {timeout} seconds")
        return False
    except Exception as e:
        print(f"❌ {description} - Error: {e}")
        return False

def check_conda():
    """Check if conda is available"""
    try:
        result = subprocess.run(["conda", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Conda found: {result.stdout.strip()}")
            return True
        else:
            print("❌ Conda not found in PATH")
            return False
    except FileNotFoundError:
        print("❌ Conda not installed or not in PATH")
        return False

def create_environment():
    """Create conda environment for the project"""
    env_name = "arbitrage-bot"
    
    # Check if environment already exists
    result = subprocess.run(
        ["conda", "env", "list"], 
        capture_output=True, 
        text=True, 
        shell=True
    )
    
    if env_name in result.stdout:
        print(f"⚠️  Environment '{env_name}' already exists")
        response = input("Do you want to remove and recreate it? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            run_command(
                f"conda env remove -n {env_name} -y",
                f"Removing existing environment '{env_name}'"
            )
        else:
            print(f"Using existing environment '{env_name}'")
            return env_name
    
    # Create new environment
    success = run_command(
        f"conda create -n {env_name} python=3.11 -y",
        f"Creating conda environment '{env_name}'"
    )
    
    return env_name if success else None

def install_conda_packages(env_name):
    """Install packages via conda"""
    packages = [
        # ML and Data Science (pre-compiled)
        ("scikit-learn pandas numpy", "ML and data science packages"),
        
        # Web Framework
        ("fastapi uvicorn", "Web framework packages"),
        
        # HTTP and Networking
        ("aiohttp requests", "HTTP client packages"),
        
        # Additional utilities
        ("python-dateutil", "Date utilities"),
    ]
    
    success_count = 0
    
    for package_list, description in packages:
        success = run_command(
            f"conda install -n {env_name} -c conda-forge {package_list} -y",
            f"Installing {description}"
        )
        if success:
            success_count += 1
    
    return success_count >= len(packages) - 1  # Allow 1 failure

def install_pip_packages(env_name):
    """Install remaining packages via pip in conda environment"""
    
    # Get conda environment python path
    if os.name == 'nt':  # Windows
        python_path = f"conda run -n {env_name} python"
        pip_path = f"conda run -n {env_name} pip"
    else:
        python_path = f"conda run -n {env_name} python"
        pip_path = f"conda run -n {env_name} pip"
    
    packages = [
        "httpx>=0.25.0",
        "python-multipart>=0.0.6", 
        "websockets>=11.0.0",
        "python-dotenv>=1.0.0",
        "pydantic>=2.0.0",
        "structlog>=23.0.0",
        "aiofiles>=23.0.0",
    ]
    
    success_count = 0
    
    for package in packages:
        success = run_command(
            f"{pip_path} install {package}",
            f"Installing {package}"
        )
        if success:
            success_count += 1
    
    return success_count >= len(packages) - 2  # Allow 2 failures

def test_installation(env_name):
    """Test if all packages can be imported"""
    test_script = '''
import sys
print(f"Python version: {sys.version}")

packages = [
    "fastapi", "uvicorn", "httpx", "pydantic", 
    "sklearn", "pandas", "numpy", "aiohttp", 
    "websockets", "dotenv"
]

success = []
failed = []

for package in packages:
    try:
        __import__(package)
        success.append(package)
        print(f"✅ {package}")
    except ImportError as e:
        failed.append(package)
        print(f"❌ {package}: {e}")

print(f"\\nSummary: {len(success)}/{len(packages)} packages working")
if len(success) >= 8:
    print("🎉 Installation successful!")
    exit(0)
else:
    print("⚠️  Some packages missing, but core functionality should work")
    exit(1)
'''
    
    # Write test script to temporary file
    with open("test_imports.py", "w") as f:
        f.write(test_script)
    
    try:
        success = run_command(
            f"conda run -n {env_name} python test_imports.py",
            "Testing package imports"
        )
        return success
    finally:
        # Clean up test file
        if os.path.exists("test_imports.py"):
            os.remove("test_imports.py")

def create_activation_script(env_name):
    """Create convenient activation script"""
    
    if os.name == 'nt':  # Windows
        script_content = f'''@echo off
echo 🐍 Activating Crypto Arbitrage Bot environment...
call conda activate {env_name}
echo ✅ Environment activated!
echo.
echo 🚀 Available commands:
echo   python main.py          - Start full version
echo   python main_simple.py   - Start simplified version  
echo   python test_public_apis.py - Test API connections
echo.
echo 🌐 After starting, open: http://localhost:8000
cmd /k
'''
        script_name = "activate_bot.bat"
    else:  # Linux/Mac
        script_content = f'''#!/bin/bash
echo "🐍 Activating Crypto Arbitrage Bot environment..."
conda activate {env_name}
echo "✅ Environment activated!"
echo ""
echo "🚀 Available commands:"
echo "  python main.py          - Start full version"
echo "  python main_simple.py   - Start simplified version"
echo "  python test_public_apis.py - Test API connections"
echo ""
echo "🌐 After starting, open: http://localhost:8000"
bash
'''
        script_name = "activate_bot.sh"
    
    try:
        with open(script_name, "w") as f:
            f.write(script_content)
        
        if os.name != 'nt':
            os.chmod(script_name, 0o755)
        
        print(f"✅ Created activation script: {script_name}")
        return script_name
    except Exception as e:
        print(f"❌ Failed to create activation script: {e}")
        return None

def main():
    print_banner()
    
    # Check if conda is available
    if not check_conda():
        print("\n❌ Conda not found!")
        print("💡 Please install Miniconda or Anaconda first:")
        print("   https://docs.conda.io/en/latest/miniconda.html")
        return False
    
    # Create environment
    print("\n🔧 Setting up conda environment...")
    env_name = create_environment()
    if not env_name:
        print("❌ Failed to create conda environment")
        return False
    
    # Install conda packages
    print("\n📦 Installing packages via conda...")
    if not install_conda_packages(env_name):
        print("❌ Failed to install core conda packages")
        return False
    
    # Install pip packages
    print("\n📦 Installing additional packages via pip...")
    if not install_pip_packages(env_name):
        print("⚠️  Some pip packages failed, but continuing...")
    
    # Test installation
    print("\n🧪 Testing installation...")
    if test_installation(env_name):
        print("✅ All tests passed!")
    else:
        print("⚠️  Some tests failed, but basic functionality should work")
    
    # Create activation script
    print("\n📝 Creating activation script...")
    script_name = create_activation_script(env_name)
    
    # Final instructions
    print("\n" + "="*60)
    print("🎉 Setup completed successfully!")
    print("\n🚀 To start the bot:")
    print(f"   1. conda activate {env_name}")
    print("   2. python main.py")
    print("\n💡 Or use the activation script:")
    if script_name:
        print(f"   {script_name}")
    
    print("\n🌐 Then open: http://localhost:8000")
    print("📚 API docs: http://localhost:8000/docs")
    
    # Ask if user wants to start now
    try:
        start_now = input("\n❓ Activate environment and start bot now? (y/n): ").lower().strip()
        if start_now in ['y', 'yes']:
            print(f"\n🚀 Activating environment '{env_name}'...")
            print("Run: python main.py")
            
            # On Windows, we can't directly activate in the same process
            # So we'll create a batch file to do it
            if os.name == 'nt':
                start_script = f'''@echo off
call conda activate {env_name}
echo 🚀 Starting Crypto Arbitrage Bot...
python main.py
pause
'''
                with open("start_bot.bat", "w") as f:
                    f.write(start_script)
                print("✅ Created start_bot.bat - double-click to run!")
                os.system("start_bot.bat")
            else:
                os.system(f"conda activate {env_name} && python main.py")
                
    except KeyboardInterrupt:
        print(f"\n👋 Setup complete! Run 'conda activate {env_name}' then 'python main.py'")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
