#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import json
import time

async def comprehensive_v3_test():
    """Comprehensive test of all v3.0 features"""
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("🚀 COMPREHENSIVE ENHANCED CRYPTO ARBITRAGE BOT v3.0 TEST")
        print("=" * 70)
        
        # Wait for server
        print("⏳ Initializing server connection...")
        for i in range(5):
            try:
                async with session.get(f"{base_url}/api/status") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ Server ready - Status: {data.get('status', 'unknown')}")
                        break
            except:
                pass
            await asyncio.sleep(1)
        
        print("\n" + "=" * 70)
        print("📊 TESTING v3.0 API ENDPOINTS")
        print("=" * 70)
        
        # Test all v3.0 endpoints
        v3_endpoints = [
            ("/api/v3/tier-stats", "Tier Statistics"),
            ("/api/v3/profit-thresholds", "Profit Thresholds"),
            ("/api/v3/token-distribution", "Token Distribution"),
            ("/api/v3/performance-metrics", "Performance Metrics")
        ]
        
        for endpoint, name in v3_endpoints:
            try:
                async with session.get(f"{base_url}{endpoint}") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ {name}: SUCCESS")
                        
                        if endpoint == "/api/v3/tier-stats":
                            stats = data.get('tier_statistics', {})
                            print(f"   📊 Total tokens: {stats.get('total_tokens', 0)}")
                            
                        elif endpoint == "/api/v3/token-distribution":
                            dist = data.get('token_distribution', {})
                            total = dist.get('total_tokens', 0)
                            blockchain_dist = dist.get('blockchain_distribution', {})
                            print(f"   🌐 Total: {total} tokens")
                            for chain, count in list(blockchain_dist.items())[:3]:
                                print(f"      {chain}: {count}")
                        
                    else:
                        print(f"❌ {name}: HTTP {response.status}")
                        
            except Exception as e:
                print(f"❌ {name}: Error - {e}")
        
        print("\n" + "=" * 70)
        print("🔍 TESTING SCANNING FUNCTIONALITY")
        print("=" * 70)
        
        # Test basic scan
        print("🎯 Testing basic scan functionality...")
        scan_start = time.time()
        
        try:
            async with session.get(f"{base_url}/api/scan") as response:
                if response.status == 200:
                    data = await response.json()
                    scan_duration = time.time() - scan_start
                    
                    print(f"✅ Scan completed in {scan_duration:.2f} seconds")
                    print(f"📊 Status: {data.get('status', 'unknown')}")
                    
                    opportunities = data.get('opportunities', [])
                    print(f"🎯 Opportunities found: {len(opportunities)}")
                    
                    if 'summary' in data:
                        summary = data['summary']
                        tokens_scanned = summary.get('tokens_scanned', 0)
                        tokens_per_sec = summary.get('tokens_per_second', 0)
                        print(f"⚡ Performance: {tokens_scanned} tokens at {tokens_per_sec:.1f} tokens/sec")
                
                else:
                    print(f"❌ Scan failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"❌ Scan error: {e}")
        
        print("\n" + "=" * 70)
        print("📈 TESTING CONFIGURATION FEATURES")
        print("=" * 70)
        
        # Test configuration endpoints
        config_endpoints = [
            ("/api/token-categories", "Token Categories"),
            ("/api/config", "Configuration"),
            ("/api/simulation-summary", "Simulation Summary")
        ]
        
        for endpoint, name in config_endpoints:
            try:
                async with session.get(f"{base_url}{endpoint}") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ {name}: Available")
                        
                        if endpoint == "/api/token-categories":
                            categories = data.get('categories', {})
                            print(f"   📂 Categories: {len(categories)}")
                            
                        elif endpoint == "/api/config":
                            config = data.get('config', {})
                            enabled_chains = config.get('enabled_chains', [])
                            print(f"   🌐 Enabled chains: {len(enabled_chains)}")
                            
                    else:
                        print(f"❌ {name}: HTTP {response.status}")
                        
            except Exception as e:
                print(f"❌ {name}: Error - {e}")
        
        print("\n" + "=" * 70)
        print("🎯 v3.0 FEATURE SUMMARY")
        print("=" * 70)
        
        # Final feature summary
        try:
            async with session.get(f"{base_url}/api/v3/performance-metrics") as response:
                if response.status == 200:
                    data = await response.json()
                    metrics = data.get('performance_metrics', {})
                    v3_features = metrics.get('v3_features', {})
                    
                    print("🚀 v3.0 Enhanced Features Status:")
                    features = [
                        ("Dynamic Token Discovery", v3_features.get('dynamic_discovery_enabled', False)),
                        ("Intelligent Tier Management", v3_features.get('tier_management_enabled', False)),
                        ("Adaptive Profit Thresholds", v3_features.get('adaptive_thresholds_enabled', False)),
                        ("1000+ Token Support", v3_features.get('max_total_tokens', 0) >= 1000)
                    ]
                    
                    for feature, enabled in features:
                        status = "✅ ENABLED" if enabled else "❌ DISABLED"
                        print(f"   {feature}: {status}")
                    
                    max_tokens = v3_features.get('max_total_tokens', 0)
                    print(f"\n📊 Maximum Token Capacity: {max_tokens}")
                    
        except Exception as e:
            print(f"❌ Feature summary error: {e}")
        
        print("\n" + "=" * 70)
        print("🎉 COMPREHENSIVE v3.0 TEST COMPLETE!")
        print("=" * 70)
        print("✅ Enhanced Crypto Arbitrage Bot v3.0 is fully operational!")
        print("🌐 Web Interface: http://localhost:8000")
        print("📚 API Documentation: http://localhost:8000/docs")

if __name__ == "__main__":
    asyncio.run(comprehensive_v3_test())
