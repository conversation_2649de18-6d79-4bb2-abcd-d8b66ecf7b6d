{"name": "crypto-arbitrage-bot-frontend", "version": "2.0.0", "description": "Advanced Crypto Arbitrage Bot Frontend with ML and Multi-Chain Support", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-query": "^3.39.3", "axios": "^1.3.0", "socket.io-client": "^4.6.1", "zustand": "^4.3.6", "framer-motion": "^10.0.0", "chart.js": "^4.2.1", "react-chartjs-2": "^5.2.0", "d3": "^7.8.2", "react-hot-toast": "^2.4.0", "react-hook-form": "^7.43.0", "react-select": "^5.7.0", "react-table": "^7.8.0", "react-window": "^1.8.8", "react-beautiful-dnd": "^13.1.1", "date-fns": "^2.29.3", "numeral": "^2.0.6", "classnames": "^2.3.2", "react-use": "^17.4.0", "react-intersection-observer": "^9.4.3"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/d3": "^7.4.0", "@types/numeral": "^2.0.2", "@types/react-table": "^7.7.14", "@types/react-window": "^1.8.5", "@types/react-beautiful-dnd": "^13.1.4", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.14", "eslint": "^8.35.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "typescript": "^4.9.5", "vite": "^4.1.4"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}