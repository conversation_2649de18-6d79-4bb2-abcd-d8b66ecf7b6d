"""
Enhanced Crypto Arbitrage Bot v2.0 - With Token Categories and Trading Simulation
FastAPI backend with comprehensive token analysis and modal simulation
"""
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, JSONResponse
import asyncio
import uvicorn
from contextlib import asynccontextmanager
from typing import Dict, Any, List
import time
import httpx
import json
from datetime import datetime

# Simple logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Simple WebSocket manager
class SimpleWebSocketManager:
    def __init__(self):
        self.connections = []
    
    async def connect(self, websocket):
        await websocket.accept()
        self.connections.append(websocket)
    
    def disconnect(self, websocket):
        if websocket in self.connections:
            self.connections.remove(websocket)
    
    async def broadcast(self, message):
        for connection in self.connections:
            try:
                await connection.send_text(json.dumps(message))
            except:
                pass

# Simple API client for public APIs
class SimpleAPIClient:
    def __init__(self, base_url: str, rate_limit: int = 30):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def get(self, endpoint: str, params: Dict = None) -> Dict:
        try:
            url = f"{self.base_url}/{endpoint}"
            response = await self.client.get(url, params=params or {})
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            logger.error(f"API request failed: {e}")
            return {}

# Enhanced arbitrage detector with token categories and simulation
class EnhancedArbitrageDetector:
    def __init__(self):
        self.opportunities = []
        self.last_scan = None
        self.scan_logs = []
        
        # Comprehensive token categories
        self.token_categories = {
            "stablecoins": {
                "name": "Stablecoins",
                "tokens": ["USDC", "USDT", "DAI", "BUSD", "FRAX", "TUSD", "LUSD", "MIM", "FDUSD", "PYUSD"],
                "priority": 1
            },
            "blue_chips": {
                "name": "Blue Chip Tokens", 
                "tokens": ["WETH", "WBTC", "BNB", "ADA", "SOL", "DOT", "AVAX", "LINK", "LTC", "BCH", "XLM", "ALGO", "ATOM", "ICP", "FIL", "VET", "THETA", "EOS", "AAVE", "MKR"],
                "priority": 2
            },
            "defi": {
                "name": "DeFi Tokens",
                "tokens": ["UNI", "SUSHI", "COMP", "YFI", "CRV", "BAL", "1INCH", "SNX", "RUNE", "ALPHA", "CREAM", "BADGER", "CVX", "FXS", "LDO", "RPL", "ALCX", "SPELL", "ICE", "DYDX"],
                "priority": 3
            },
            "layer1_2": {
                "name": "Layer 1/2 Tokens",
                "tokens": ["MATIC", "ARB", "OP", "METIS", "IMX", "LRC", "MINA", "NEAR", "FTM", "ONE", "CELO", "KAVA", "ROSE", "MOVR", "GLMR", "ASTR", "CFG", "PHA", "RING", "CKB"],
                "priority": 4
            },
            "meme_coins": {
                "name": "Meme Coins",
                "tokens": ["DOGE", "SHIB", "PEPE", "FLOKI", "BONK", "WIF", "BOME", "MEME", "WOJAK", "LADYS", "TURBO", "AIDOGE", "BABYDOGE", "KISHU", "ELON", "AKITA", "HOGE", "SAFEMOON", "DOGELON", "CATGIRL"],
                "priority": 5
            }
        }
        
        # Extended chain support
        self.supported_chains = {
            "ethereum": {"name": "Ethereum", "symbol": "ETH", "priority": 1},
            "bsc": {"name": "Binance Smart Chain", "symbol": "BNB", "priority": 2},
            "polygon": {"name": "Polygon", "symbol": "MATIC", "priority": 3},
            "arbitrum": {"name": "Arbitrum", "symbol": "ARB", "priority": 4},
            "optimism": {"name": "Optimism", "symbol": "OP", "priority": 5},
            "avalanche": {"name": "Avalanche", "symbol": "AVAX", "priority": 6},
            "fantom": {"name": "Fantom", "symbol": "FTM", "priority": 7},
            "base": {"name": "Base", "symbol": "ETH", "priority": 8}
        }
        
        # Enhanced configuration with simulation parameters
        self.config = {
            "profit_min": 0.05,
            "profit_max": 25.0,
            "min_liquidity": 5000,
            "min_volume_24h": 500,
            "enabled_chains": ["ethereum", "bsc", "polygon", "arbitrum"],
            "auto_scan_interval": 0,
            "enabled_token_categories": ["stablecoins", "blue_chips", "defi"],
            "max_tokens_per_category": 10,
            "simulation_capital": 100,
            "max_slippage": 0.5,
            "capital_per_trade": 100,
            "min_liquidity_ratio": 10,
            "risk_tolerance": "medium"
        }
    
    def add_log(self, message: str, level: str = "info"):
        """Add log entry with timestamp"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "level": level
        }
        self.scan_logs.append(log_entry)
        if len(self.scan_logs) > 50:
            self.scan_logs = self.scan_logs[-50:]
        logger.info(f"[{level.upper()}] {message}")
    
    def update_config(self, new_config: dict):
        """Update scanning configuration"""
        self.config.update(new_config)
        self.add_log(f"Konfigurasi diperbarui", "info")
    
    def get_enabled_tokens(self) -> List[str]:
        """Get list of enabled tokens based on configuration"""
        enabled_tokens = []
        
        for category in self.config["enabled_token_categories"]:
            if category in self.token_categories:
                category_tokens = self.token_categories[category]["tokens"]
                max_tokens = self.config["max_tokens_per_category"]
                enabled_tokens.extend(category_tokens[:max_tokens])
        
        return list(set(enabled_tokens))
    
    def calculate_slippage_estimate(self, order_size_usd: float, liquidity_usd: float) -> float:
        """Estimate slippage based on order size vs liquidity"""
        if liquidity_usd <= 0:
            return 100.0
        
        liquidity_ratio = order_size_usd / liquidity_usd
        
        if liquidity_ratio <= 0.001:
            return 0.01
        elif liquidity_ratio <= 0.01:
            return 0.1 + (liquidity_ratio * 10)
        elif liquidity_ratio <= 0.05:
            return 1.0 + (liquidity_ratio * 20)
        else:
            return min(50.0, 2.0 + (liquidity_ratio * 100))
    
    def calculate_risk_level(self, liquidity_ratio: float, slippage: float) -> str:
        """Calculate risk level based on liquidity ratio and slippage"""
        if liquidity_ratio >= 20 and slippage <= 0.5:
            return "Low"
        elif liquidity_ratio >= 10 and slippage <= 1.0:
            return "Medium"
        else:
            return "High"
    
    def filter_opportunities_by_simulation(self, opportunities: List[Dict]) -> List[Dict]:
        """Filter opportunities based on simulation parameters"""
        filtered_opportunities = []
        capital = self.config["simulation_capital"]
        max_slippage = self.config["max_slippage"]
        capital_per_trade = (self.config["capital_per_trade"] / 100) * capital
        min_liquidity_ratio = self.config["min_liquidity_ratio"]
        
        for opp in opportunities:
            min_liquidity = opp.get("min_liquidity", 0)
            
            required_liquidity = capital_per_trade * min_liquidity_ratio
            if min_liquidity < required_liquidity:
                continue
            
            buy_slippage = self.calculate_slippage_estimate(capital_per_trade, min_liquidity)
            sell_slippage = self.calculate_slippage_estimate(capital_per_trade, min_liquidity)
            total_slippage = buy_slippage + sell_slippage
            
            if total_slippage > max_slippage:
                continue
            
            liquidity_ratio = min_liquidity / capital_per_trade
            risk_level = self.calculate_risk_level(liquidity_ratio, total_slippage)
            
            profit_percentage = opp.get("profit_percentage", 0)
            estimated_profit_usd = (profit_percentage / 100) * capital_per_trade
            
            opp.update({
                "simulation_capital": capital_per_trade,
                "estimated_profit_usd": round(estimated_profit_usd, 2),
                "buy_slippage": round(buy_slippage, 3),
                "sell_slippage": round(sell_slippage, 3),
                "total_slippage": round(total_slippage, 3),
                "liquidity_ratio": round(liquidity_ratio, 1),
                "risk_level": risk_level,
                "recommended_order_size": round(min(capital_per_trade, min_liquidity * 0.01), 2)
            })
            
            filtered_opportunities.append(opp)
        
        return filtered_opportunities
    
    async def scan_opportunities(self) -> List[Dict[str, Any]]:
        """Scan for arbitrage opportunities with enhanced filtering"""
        self.add_log("Memulai pemindaian arbitrase enhanced...", "info")
        opportunities = []
        
        try:
            enabled_tokens = self.get_enabled_tokens()
            self.add_log(f"Memindai {len(enabled_tokens)} token yang diaktifkan...", "info")
            
            # Simulate some opportunities for demo
            for i, token in enumerate(enabled_tokens[:5]):  # Limit for demo
                for chain in self.config["enabled_chains"][:2]:  # Limit chains for demo
                    # Create mock opportunity
                    profit_pct = 0.1 + (i * 0.2)  # 0.1% to 1.0%
                    if profit_pct <= self.config["profit_max"]:
                        opportunities.append({
                            "id": f"{token}_{chain}_{int(time.time())}_{i}",
                            "token_symbol": token,
                            "buy_exchange": f"{chain}_uniswap",
                            "sell_exchange": f"{chain}_sushiswap",
                            "buy_price": 100.0,
                            "sell_price": 100.0 + profit_pct,
                            "profit_percentage": profit_pct,
                            "profit_usd": profit_pct * 10,
                            "min_liquidity": 50000 + (i * 10000),
                            "buy_chain": chain,
                            "sell_chain": chain,
                            "buy_dex_name": "uniswap",
                            "sell_dex_name": "sushiswap",
                            "timestamp": datetime.now().isoformat(),
                            "type": "same_chain_arbitrage"
                        })
            
            # Apply simulation filtering
            filtered_opportunities = self.filter_opportunities_by_simulation(opportunities)
            
            self.opportunities = filtered_opportunities[:20]
            self.last_scan = datetime.now().isoformat()
            
            total_found = len(opportunities)
            feasible_count = len(filtered_opportunities)
            self.add_log(f"Pemindaian selesai! Ditemukan {total_found} peluang, {feasible_count} layak dengan modal simulasi", "success")
            
        except Exception as e:
            self.add_log(f"Error pemindaian: {str(e)}", "error")
            logger.error(f"Scan error: {e}")
        
        return self.opportunities

# Global instances
websocket_manager = SimpleWebSocketManager()
coingecko = SimpleAPIClient("https://api.coingecko.com/api/v3", 10)
dexscreener = SimpleAPIClient("https://api.dexscreener.com/latest", 60)
binance = SimpleAPIClient("https://api.binance.com/api/v3", 300)
detector = EnhancedArbitrageDetector()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting Enhanced Crypto Arbitrage Bot v2.0...")
    
    try:
        logger.info("Application startup complete - Enhanced version with token categories and simulation")
        yield
        
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise
    finally:
        logger.info("Shutting down application...")
        await coingecko.client.aclose()
        await dexscreener.client.aclose()
        await binance.client.aclose()
        logger.info("Application shutdown complete")

# Create FastAPI app
app = FastAPI(
    title="Enhanced Crypto Arbitrage Bot v2.0",
    description="Advanced cryptocurrency arbitrage detection with token categories and trading simulation",
    version="2.0.0-enhanced",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the enhanced frontend application"""
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Enhanced Crypto Arbitrage Bot v2.0</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            @import url('https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap');

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: #ffffff;
                overflow-x: hidden;
            }

            .background-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
                z-index: -1;
            }

            .container {
                max-width: 1400px;
                margin: 0 auto;
                padding: 20px;
                position: relative;
                z-index: 1;
            }

            .header {
                text-align: center;
                margin-bottom: 40px;
                padding: 40px 20px;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border-radius: 30px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }

            .header h1 {
                font-size: 3.5rem;
                font-weight: 700;
                background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 10px;
                text-shadow: 0 0 30px rgba(255, 154, 158, 0.5);
            }

            .header p {
                font-size: 1.2rem;
                opacity: 0.8;
                font-weight: 300;
            }

            .card {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 30px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                margin-bottom: 30px;
            }

            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            }

            .btn {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 15px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 0.95rem;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
                margin: 5px;
            }

            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            }

            .btn-scan {
                background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
                box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);
            }

            .btn-scan:hover {
                box-shadow: 0 8px 25px rgba(255, 154, 158, 0.6);
            }

            .grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 30px;
                margin-bottom: 30px;
            }

            .opportunity {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                padding: 25px;
                margin: 20px 0;
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
            }

            .opportunity:hover {
                transform: scale(1.02);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            }

            .risk-badge {
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 0.8rem;
                font-weight: 600;
                text-transform: uppercase;
                display: inline-block;
                margin-bottom: 10px;
            }

            .risk-low {
                background: rgba(16, 185, 129, 0.2);
                color: #10b981;
                border: 1px solid rgba(16, 185, 129, 0.3);
            }

            .risk-medium {
                background: rgba(245, 158, 11, 0.2);
                color: #f59e0b;
                border: 1px solid rgba(245, 158, 11, 0.3);
            }

            .risk-high {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .simulation-metrics {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 15px;
                margin: 15px 0;
                padding: 15px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
            }

            .metric-item {
                text-align: center;
            }

            .metric-value {
                font-size: 1.1rem;
                font-weight: 700;
                color: #fecfef;
                margin-bottom: 5px;
            }

            .metric-label {
                font-size: 0.8rem;
                opacity: 0.8;
            }

            .profit-positive {
                color: #a7f3d0;
                font-weight: 700;
                font-size: 1.1rem;
                text-shadow: 0 0 10px rgba(167, 243, 208, 0.5);
            }

            .status-indicator {
                display: inline-block;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin-right: 10px;
                animation: pulse 2s infinite;
                background: #34d399;
                box-shadow: 0 0 10px rgba(52, 211, 153, 0.5);
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
        </style>
    </head>
    <body>
        <div class="background-overlay"></div>
        <div class="container">
            <div class="header">
                <h1>🚀 Enhanced Arbitrage Bot v2.0</h1>
                <p><span class="status-indicator"></span>Token Categories • Trading Simulation • Enhanced Filtering</p>
            </div>

            <div class="grid">
                <div class="card">
                    <h2>🎯 Enhanced Control Center</h2>
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-scan" onclick="scanOpportunities()">🔍 Scan with Simulation</button>
                        <button class="btn" onclick="checkStatus()">📊 Status</button>
                        <button class="btn" onclick="updateSimulation()">💼 Update Simulation</button>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                        <div style="text-align: center; padding: 15px; background: rgba(255, 255, 255, 0.1); border-radius: 12px;">
                            <div style="font-size: 1.5rem; font-weight: 700; color: #fecfef;" id="opportunities-count">0</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">Peluang Layak</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: rgba(255, 255, 255, 0.1); border-radius: 12px;">
                            <div style="font-size: 1.5rem; font-weight: 700; color: #fecfef;" id="total-profit">$0</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">Profit Potensial</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: rgba(255, 255, 255, 0.1); border-radius: 12px;">
                            <div style="font-size: 1.5rem; font-weight: 700; color: #fecfef;" id="avg-slippage">0%</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">Avg Slippage</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2>💼 Trading Simulation</h2>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Modal ($)</label>
                            <input type="number" id="simulation-capital" value="100" min="10" max="10000"
                                   style="width: 100%; padding: 8px; border-radius: 8px; border: 1px solid rgba(255,255,255,0.3); background: rgba(255,255,255,0.1); color: white;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Max Slippage (%)</label>
                            <input type="number" id="max-slippage" value="0.5" min="0.1" max="5" step="0.1"
                                   style="width: 100%; padding: 8px; border-radius: 8px; border: 1px solid rgba(255,255,255,0.3); background: rgba(255,255,255,0.1); color: white;">
                        </div>
                    </div>
                    <button class="btn" onclick="updateSimulationConfig()">💾 Update Simulation</button>
                </div>
            </div>

            <div class="card">
                <h2>💰 Enhanced Arbitrage Opportunities</h2>
                <div id="opportunities">
                    <div style="text-align: center; padding: 60px 20px; opacity: 0.7;">
                        <div style="font-size: 3rem; margin-bottom: 20px;">🔍</div>
                        <p style="font-size: 1.2rem; margin-bottom: 10px;">Ready for Enhanced Arbitrage Scanning</p>
                        <p style="opacity: 0.8;">Click "Scan with Simulation" to start analysis with token categories and trading simulation</p>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let isScanning = false;

            async function scanOpportunities() {
                if (isScanning) return;

                isScanning = true;

                document.getElementById('opportunities').innerHTML = `
                    <div style="text-align: center; padding: 60px 20px;">
                        <div style="font-size: 3rem; margin-bottom: 20px; animation: pulse 1s infinite;">🔍</div>
                        <p style="font-size: 1.2rem; margin-bottom: 10px;">Enhanced scanning with simulation filtering...</p>
                        <p style="opacity: 0.8;">Analyzing token categories and calculating trading simulation</p>
                    </div>
                `;

                try {
                    const response = await fetch('/api/scan');
                    const data = await response.json();

                    document.getElementById('opportunities-count').textContent = data.count || 0;

                    if (data.opportunities && data.opportunities.length > 0) {
                        let html = '';
                        data.opportunities.forEach((opp, index) => {
                            const riskClass = opp.risk_level ? `risk-${opp.risk_level.toLowerCase()}` : 'risk-medium';

                            html += `
                                <div class="opportunity" style="animation-delay: ${index * 0.1}s;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                        <h3>💎 ${opp.token_symbol} Enhanced Opportunity</h3>
                                        <span class="risk-badge ${riskClass}">${opp.risk_level || 'Medium'} Risk</span>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0;">
                                        <div style="background: rgba(248, 113, 113, 0.1); padding: 15px; border-radius: 12px; border-left: 3px solid #f87171;">
                                            <p><strong>🛒 Buy:</strong> ${opp.buy_exchange}</p>
                                            <p style="font-size: 1.2rem; color: #fca5a5; font-weight: 600; margin: 8px 0;">$${opp.buy_price}</p>
                                        </div>
                                        <div style="background: rgba(52, 211, 153, 0.1); padding: 15px; border-radius: 12px; border-left: 3px solid #34d399;">
                                            <p><strong>💰 Sell:</strong> ${opp.sell_exchange}</p>
                                            <p style="font-size: 1.2rem; color: #a7f3d0; font-weight: 600; margin: 8px 0;">$${opp.sell_price}</p>
                                        </div>
                                    </div>

                                    <div class="simulation-metrics">
                                        <div class="metric-item">
                                            <div class="metric-value">$${opp.estimated_profit_usd || 0}</div>
                                            <div class="metric-label">Estimated Profit</div>
                                        </div>
                                        <div class="metric-item">
                                            <div class="metric-value">${opp.total_slippage || 0}%</div>
                                            <div class="metric-label">Total Slippage</div>
                                        </div>
                                        <div class="metric-item">
                                            <div class="metric-value">${opp.liquidity_ratio || 0}x</div>
                                            <div class="metric-label">Liquidity Ratio</div>
                                        </div>
                                        <div class="metric-item">
                                            <div class="metric-value">$${opp.recommended_order_size || 0}</div>
                                            <div class="metric-label">Recommended Size</div>
                                        </div>
                                    </div>

                                    <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 12px; margin: 10px 0;">
                                        <p><strong>📊 Profit:</strong> <span class="profit-positive">${opp.profit_percentage}%</span></p>
                                        <p><strong>💧 Min Liquidity:</strong> $${opp.min_liquidity?.toLocaleString()}</p>
                                        <p><strong>🔗 Blockchain:</strong> ${opp.buy_chain}</p>
                                        <p><strong>⏰ Detected:</strong> ${new Date(opp.timestamp).toLocaleString()}</p>
                                    </div>
                                </div>
                            `;
                        });
                        document.getElementById('opportunities').innerHTML = html;

                        // Update summary
                        updateSummaryStats(data.opportunities);

                    } else {
                        document.getElementById('opportunities').innerHTML = `
                            <div style="text-align: center; padding: 60px 20px; opacity: 0.7;">
                                <div style="font-size: 3rem; margin-bottom: 20px;">😔</div>
                                <p style="font-size: 1.2rem; margin-bottom: 10px;">No feasible opportunities found</p>
                                <p style="opacity: 0.8;">Try adjusting simulation parameters or scanning again</p>
                                <button class="btn" onclick="scanOpportunities()" style="margin-top: 20px;">🔄 Scan Again</button>
                            </div>
                        `;
                    }

                } catch (error) {
                    document.getElementById('opportunities').innerHTML = `
                        <div style="text-align: center; padding: 60px 20px;">
                            <div style="font-size: 3rem; margin-bottom: 20px;">❌</div>
                            <p style="font-size: 1.2rem; margin-bottom: 10px;">Scan Error</p>
                            <p style="opacity: 0.8;">${error.message}</p>
                            <button class="btn" onclick="scanOpportunities()" style="margin-top: 20px;">🔄 Try Again</button>
                        </div>
                    `;
                }

                isScanning = false;
            }

            function updateSummaryStats(opportunities) {
                const totalProfit = opportunities.reduce((sum, opp) => sum + (opp.estimated_profit_usd || 0), 0);
                const avgSlippage = opportunities.length > 0 ?
                    opportunities.reduce((sum, opp) => sum + (opp.total_slippage || 0), 0) / opportunities.length : 0;

                document.getElementById('total-profit').textContent = `$${totalProfit.toFixed(2)}`;
                document.getElementById('avg-slippage').textContent = `${avgSlippage.toFixed(2)}%`;
            }

            async function updateSimulationConfig() {
                const capital = parseFloat(document.getElementById('simulation-capital').value);
                const maxSlippage = parseFloat(document.getElementById('max-slippage').value);

                try {
                    const response = await fetch('/api/config', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            simulation_capital: capital,
                            max_slippage: maxSlippage
                        })
                    });

                    if (response.ok) {
                        alert('✅ Simulation configuration updated!');
                    }
                } catch (error) {
                    alert('❌ Error updating configuration');
                }
            }

            async function checkStatus() {
                try {
                    const response = await fetch('/api/status');
                    const data = await response.json();
                    alert(`✅ Status: ${data.status}\\n📊 Version: ${data.version}\\n💰 Opportunities: ${data.opportunities_count}`);
                } catch (error) {
                    alert('❌ Error: ' + error.message);
                }
            }

            async function updateSimulation() {
                try {
                    const response = await fetch('/api/simulation-summary');
                    const data = await response.json();

                    if (data.summary) {
                        const summary = data.summary;
                        alert(`💼 Simulation Summary:\\n` +
                              `💰 Total Capital: $${summary.total_capital}\\n` +
                              `🎯 Feasible Opportunities: ${summary.feasible_opportunities}\\n` +
                              `📈 Total Potential Profit: $${summary.total_potential_profit}\\n` +
                              `📉 Average Slippage: ${summary.average_slippage}%`);
                    }
                } catch (error) {
                    alert('❌ Error fetching simulation summary');
                }
            }
        </script>
    </body>
    </html>
    """, status_code=200)

@app.get("/api/status")
async def get_status():
    """Get application status"""
    return {
        "status": "healthy",
        "version": "2.0.0-enhanced",
        "mode": "enhanced_with_simulation",
        "timestamp": datetime.now().isoformat(),
        "last_scan": detector.last_scan,
        "opportunities_count": len(detector.opportunities)
    }

@app.get("/api/scan")
async def scan_opportunities():
    """Scan for arbitrage opportunities with enhanced filtering"""
    try:
        opportunities = await detector.scan_opportunities()

        return {
            "status": "success",
            "opportunities": opportunities,
            "count": len(opportunities),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/config")
async def get_config():
    """Get current configuration"""
    return {
        "status": "success",
        "config": detector.config
    }

@app.post("/api/config")
async def update_config(config_data: dict):
    """Update scanning configuration"""
    try:
        detector.update_config(config_data)

        return {
            "status": "success",
            "message": "Configuration updated successfully",
            "config": detector.config
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error updating config: {str(e)}")

@app.get("/api/token-categories")
async def get_token_categories():
    """Get available token categories"""
    return {
        "status": "success",
        "categories": detector.token_categories,
        "chains": detector.supported_chains
    }

@app.get("/api/simulation-summary")
async def get_simulation_summary():
    """Get simulation summary statistics"""
    try:
        opportunities = detector.opportunities
        config = detector.config

        if not opportunities:
            return {
                "status": "success",
                "summary": {
                    "total_capital": config["simulation_capital"],
                    "feasible_opportunities": 0,
                    "total_potential_profit": 0,
                    "average_slippage": 0,
                    "risk_distribution": {"Low": 0, "Medium": 0, "High": 0}
                }
            }

        # Calculate summary statistics
        total_capital = config["simulation_capital"]
        feasible_count = len(opportunities)
        total_potential_profit = sum(opp.get("estimated_profit_usd", 0) for opp in opportunities)
        average_slippage = sum(opp.get("total_slippage", 0) for opp in opportunities) / feasible_count if feasible_count > 0 else 0

        # Risk distribution
        risk_distribution = {"Low": 0, "Medium": 0, "High": 0}
        for opp in opportunities:
            risk_level = opp.get("risk_level", "High")
            risk_distribution[risk_level] += 1

        return {
            "status": "success",
            "summary": {
                "total_capital": total_capital,
                "feasible_opportunities": feasible_count,
                "total_potential_profit": round(total_potential_profit, 2),
                "average_slippage": round(average_slippage, 3),
                "risk_distribution": risk_distribution,
                "capital_utilization": round((total_potential_profit / total_capital) * 100, 2) if total_capital > 0 else 0
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating summary: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "2.0.0-enhanced",
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    print("🚀 Starting Enhanced Crypto Arbitrage Bot v2.0")
    print("📊 Features: Token Categories, Trading Simulation, Enhanced Filtering")
    print("🌐 Web interface: http://localhost:8000")
    print("📚 API docs: http://localhost:8000/docs")
    print("=" * 60)
    
    uvicorn.run(
        "main_enhanced:app",
        host="0.0.0.0",
        port=8001,
        reload=True
    )
