#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import json

async def test_v3_config():
    """Test v3_config initialization"""
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("🔍 TESTING v3_config INITIALIZATION")
        print("=" * 50)
        
        # Wait for server
        for i in range(3):
            try:
                async with session.get(f"{base_url}/api/status") as response:
                    if response.status == 200:
                        print("✅ Server ready!")
                        break
            except:
                pass
            await asyncio.sleep(1)
        
        # Test performance metrics endpoint
        try:
            async with session.get(f"{base_url}/api/v3/performance-metrics") as response:
                if response.status == 200:
                    data = await response.json()
                    
                    print(f"📊 Performance Metrics Response:")
                    print(f"   Status: {data.get('status', 'unknown')}")
                    print(f"   Version: {data.get('version', 'unknown')}")
                    
                    metrics = data.get('performance_metrics', {})
                    v3_features = metrics.get('v3_features', {})
                    
                    print(f"\n🚀 v3_features content:")
                    for key, value in v3_features.items():
                        print(f"   {key}: {value}")
                    
                    # Check if v3_config exists
                    v3_config_exists = v3_features.get('v3_config_exists', False)
                    v3_config_content = v3_features.get('v3_config_content', {})
                    
                    print(f"\n🔧 v3_config Analysis:")
                    print(f"   v3_config exists: {v3_config_exists}")
                    print(f"   v3_config content: {v3_config_content}")
                    
                    if v3_config_content:
                        print(f"   Demo mode enabled: {v3_config_content.get('enable_demo_mode', 'NOT FOUND')}")
                    else:
                        print(f"   ❌ v3_config is empty or not initialized")
                
                else:
                    print(f"❌ Performance metrics failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print(f"\n" + "=" * 50)
        print("🎯 v3_config TEST COMPLETE")

if __name__ == "__main__":
    asyncio.run(test_v3_config())
