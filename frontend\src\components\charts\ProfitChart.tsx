import React, { useMemo } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js'
import { Line, Bar } from 'react-chartjs-2'
import { motion } from 'framer-motion'
import Card, { CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import { ArbitrageOpportunity } from '@types/index'
import { formatPercentage, formatCurrency, formatTimeAgo } from '@utils/formatters'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

interface ProfitChartProps {
  opportunities: ArbitrageOpportunity[]
  timeframe: '1h' | '6h' | '24h' | '7d'
  chartType: 'line' | 'bar'
}

const ProfitChart: React.FC<ProfitChartProps> = ({
  opportunities,
  timeframe,
  chartType
}) => {
  const chartData = useMemo(() => {
    // Group opportunities by time intervals
    const now = new Date()
    const intervals: { [key: string]: ArbitrageOpportunity[] } = {}
    
    // Define time intervals based on timeframe
    const getTimeInterval = (date: Date) => {
      switch (timeframe) {
        case '1h':
          return `${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
        case '6h':
          return `${date.getHours()}:00`
        case '24h':
          return `${date.getHours()}:00`
        case '7d':
          return `${date.getMonth() + 1}/${date.getDate()}`
        default:
          return date.toISOString()
      }
    }

    // Group opportunities by time intervals
    opportunities.forEach(opp => {
      const date = new Date(opp.discovered_at)
      const interval = getTimeInterval(date)
      
      if (!intervals[interval]) {
        intervals[interval] = []
      }
      intervals[interval].push(opp)
    })

    // Calculate average profit for each interval
    const labels = Object.keys(intervals).sort()
    const avgProfits = labels.map(label => {
      const opps = intervals[label]
      const totalProfit = opps.reduce((sum, opp) => sum + opp.profit_percentage, 0)
      return totalProfit / opps.length
    })

    const maxProfits = labels.map(label => {
      const opps = intervals[label]
      return Math.max(...opps.map(opp => opp.profit_percentage))
    })

    const counts = labels.map(label => intervals[label].length)

    return {
      labels,
      datasets: [
        {
          label: 'Average Profit %',
          data: avgProfits,
          borderColor: 'rgba(139, 92, 246, 1)',
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          borderWidth: 2,
          fill: chartType === 'line',
          tension: 0.4,
          pointBackgroundColor: 'rgba(139, 92, 246, 1)',
          pointBorderColor: 'rgba(255, 255, 255, 0.8)',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6,
        },
        {
          label: 'Max Profit %',
          data: maxProfits,
          borderColor: 'rgba(16, 185, 129, 1)',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
          pointBackgroundColor: 'rgba(16, 185, 129, 1)',
          pointBorderColor: 'rgba(255, 255, 255, 0.8)',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6,
        },
        {
          label: 'Opportunity Count',
          data: counts,
          borderColor: 'rgba(6, 182, 212, 1)',
          backgroundColor: 'rgba(6, 182, 212, 0.2)',
          borderWidth: 2,
          yAxisID: 'y1',
          type: 'bar' as const,
        }
      ]
    }
  }, [opportunities, timeframe, chartType])

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Inter',
            size: 12,
          },
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      tooltip: {
        backgroundColor: 'rgba(15, 15, 35, 0.95)',
        titleColor: 'rgba(255, 255, 255, 0.9)',
        bodyColor: 'rgba(255, 255, 255, 0.8)',
        borderColor: 'rgba(139, 92, 246, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            const label = context.dataset.label || ''
            const value = context.parsed.y
            
            if (label.includes('Profit')) {
              return `${label}: ${formatPercentage(value)}`
            } else if (label.includes('Count')) {
              return `${label}: ${value} opportunities`
            }
            return `${label}: ${value}`
          }
        }
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
          drawBorder: false,
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Inter',
            size: 11,
          },
        },
      },
      y: {
        type: 'linear' as const,
        display: true,
        position: 'left' as const,
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
          drawBorder: false,
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Inter',
            size: 11,
          },
          callback: function(value: any) {
            return formatPercentage(value)
          }
        },
        title: {
          display: true,
          text: 'Profit Percentage',
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Inter',
            size: 12,
          },
        },
      },
      y1: {
        type: 'linear' as const,
        display: true,
        position: 'right' as const,
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Inter',
            size: 11,
          },
        },
        title: {
          display: true,
          text: 'Count',
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Inter',
            size: 12,
          },
        },
      },
    },
    elements: {
      point: {
        hoverBackgroundColor: 'rgba(255, 255, 255, 1)',
      },
    },
  }

  const ChartComponent = chartType === 'line' ? Line : Bar

  return (
    <Card variant="glass" className="h-96">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Profit Analysis - {timeframe.toUpperCase()}</span>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-neon-purple rounded-full"></div>
            <span className="text-sm text-gray-400">Live Data</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="h-full"
        >
          <ChartComponent data={chartData} options={options} />
        </motion.div>
      </CardContent>
    </Card>
  )
}

export default ProfitChart
