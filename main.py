"""
Advanced Crypto Arbitrage Bot - Main Application
FastAPI backend with ML integration and real-time WebSocket support
Uses only public APIs - no API keys required
"""
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
import asyncio
import uvicorn
from contextlib import asynccontextmanager
from typing import Dict, Any, List
import os
from pathlib import Path

# Backend imports
from backend.api.arbitrage import router as arbitrage_router
from backend.api.analytics import router as analytics_router
from backend.api.websocket import WebSocketManager
from backend.core.logging import get_logger, setup_logging
from backend.core.database import init_db
from backend.core.cache import init_cache
from backend.services.public_api_clients import cleanup_clients

logger = get_logger(__name__)

# Global WebSocket manager
websocket_manager = WebSocketManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Crypto Arbitrage Bot v2.0...")
    
    try:
        # Setup logging
        setup_logging()
        
        # Initialize database
        await init_db()
        logger.info("Database initialized")
        
        # Initialize cache
        await init_cache()
        logger.info("Cache initialized")
        
        logger.info("✅ Application startup complete - Using public APIs only!")
        
        yield
        
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down application...")
        
        # Cleanup HTTP clients
        await cleanup_clients()
        logger.info("HTTP clients cleaned up")
        
        logger.info("✅ Application shutdown complete")


# Create FastAPI app
app = FastAPI(
    title="Crypto Arbitrage Bot v2.0",
    description="Advanced cryptocurrency arbitrage detection with ML integration - Public APIs only",
    version="2.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(arbitrage_router, prefix="/api/arbitrage", tags=["arbitrage"])
app.include_router(analytics_router, prefix="/api/analytics", tags=["analytics"])

# Serve static files (frontend)
frontend_path = Path(__file__).parent / "frontend" / "dist"
if frontend_path.exists():
    app.mount("/static", StaticFiles(directory=str(frontend_path)), name="static")


@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the frontend application"""
    frontend_index = frontend_path / "index.html"
    if frontend_index.exists():
        return HTMLResponse(content=frontend_index.read_text(), status_code=200)
    else:
        return HTMLResponse(
            content="""
            <html>
                <head><title>Crypto Arbitrage Bot v2.0</title></head>
                <body>
                    <h1>🚀 Crypto Arbitrage Bot v2.0</h1>
                    <p>Advanced cryptocurrency arbitrage detection platform</p>
                    <h2>✅ Features (Public APIs Only)</h2>
                    <ul>
                        <li>🔍 Real-time arbitrage detection</li>
                        <li>🤖 ML-enhanced opportunity scoring</li>
                        <li>🌐 Multi-chain support (Ethereum, BSC, Polygon, etc.)</li>
                        <li>📊 Advanced analytics and visualization</li>
                        <li>💬 Sentiment analysis from Reddit</li>
                        <li>⚡ WebSocket real-time updates</li>
                    </ul>
                    <h2>🔗 API Endpoints</h2>
                    <ul>
                        <li><a href="/docs">📚 API Documentation (Swagger)</a></li>
                        <li><a href="/api/arbitrage/status">🔍 Engine Status</a></li>
                        <li><a href="/api/arbitrage/opportunities">💰 Current Opportunities</a></li>
                        <li><a href="/api/analytics/profit">📈 Profit Analytics</a></li>
                    </ul>
                    <h2>🌐 Data Sources (Public APIs)</h2>
                    <ul>
                        <li>🦎 <strong>CoinGecko</strong>: Market data and token information</li>
                        <li>📊 <strong>DexScreener</strong>: DEX trading pairs and liquidity</li>
                        <li>🔶 <strong>Binance</strong>: CEX market data and order books</li>
                        <li>💬 <strong>Reddit</strong>: Sentiment analysis from crypto communities</li>
                    </ul>
                    <p><em>No API keys required - all endpoints are public!</em></p>
                </body>
            </html>
            """,
            status_code=200
        )


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "api_mode": "public_only",
        "timestamp": "2024-01-01T00:00:00Z"
    }


@app.get("/api/status")
async def api_status():
    """API status endpoint"""
    return {
        "api_version": "2.0.0",
        "mode": "public_apis_only",
        "supported_chains": [
            "ethereum", "bsc", "polygon", "arbitrum", "avalanche", "solana"
        ],
        "data_sources": {
            "coingecko": {
                "status": "active",
                "rate_limit": "10 requests/minute",
                "endpoints": ["price", "market_data", "coin_info"]
            },
            "dexscreener": {
                "status": "active", 
                "rate_limit": "60 requests/minute",
                "endpoints": ["pairs", "search", "token_data"]
            },
            "binance": {
                "status": "active",
                "rate_limit": "1200 requests/minute", 
                "endpoints": ["ticker", "orderbook", "exchange_info"]
            },
            "reddit": {
                "status": "active",
                "rate_limit": "60 requests/minute",
                "endpoints": ["posts", "search", "sentiment"]
            }
        },
        "features": {
            "arbitrage_detection": True,
            "ml_scoring": True,
            "sentiment_analysis": True,
            "real_time_updates": True,
            "multi_chain": True,
            "risk_assessment": True
        }
    }


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            
            # Handle different message types
            if data == "ping":
                await websocket.send_text("pong")
            elif data.startswith("subscribe:"):
                topic = data.split(":", 1)[1]
                await websocket_manager.subscribe(websocket, topic)
            elif data.startswith("unsubscribe:"):
                topic = data.split(":", 1)[1]
                await websocket_manager.unsubscribe(websocket, topic)
                
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code,
            "timestamp": "2024-01-01T00:00:00Z"
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "status_code": 500,
            "timestamp": "2024-01-01T00:00:00Z"
        }
    )


if __name__ == "__main__":
    # Development server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
