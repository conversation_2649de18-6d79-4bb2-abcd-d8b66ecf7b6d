import React from 'react'
import { motion } from 'framer-motion'
import classNames from 'classnames'

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'outline'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
  glow?: boolean
  gradient?: boolean
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  glow = false,
  gradient = false,
  className,
  disabled,
  ...props
}) => {
  const baseClasses = [
    'inline-flex items-center justify-center',
    'font-medium rounded-xl transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-dark-900',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    'relative overflow-hidden',
  ]

  const variantClasses = {
    primary: [
      'bg-gradient-to-r from-neon-purple to-neon-blue',
      'text-white border border-transparent',
      'hover:shadow-glow-lg hover:scale-105',
      'focus:ring-neon-purple',
      'active:scale-95',
    ],
    secondary: [
      'bg-dark-800 text-gray-300 border border-dark-600',
      'hover:bg-dark-700 hover:text-white hover:border-dark-500',
      'focus:ring-dark-500',
    ],
    success: [
      'bg-gradient-to-r from-success to-neon-green',
      'text-white border border-transparent',
      'hover:shadow-neon-green hover:scale-105',
      'focus:ring-success',
    ],
    warning: [
      'bg-gradient-to-r from-warning to-neon-yellow',
      'text-dark-900 border border-transparent',
      'hover:shadow-lg hover:scale-105',
      'focus:ring-warning',
    ],
    error: [
      'bg-gradient-to-r from-error to-neon-pink',
      'text-white border border-transparent',
      'hover:shadow-lg hover:scale-105',
      'focus:ring-error',
    ],
    ghost: [
      'bg-transparent text-gray-300 border border-transparent',
      'hover:bg-dark-800 hover:text-white',
      'focus:ring-dark-500',
    ],
    outline: [
      'bg-transparent text-neon-blue border border-neon-blue',
      'hover:bg-neon-blue hover:text-dark-900',
      'focus:ring-neon-blue',
    ],
  }

  const sizeClasses = {
    xs: 'px-2.5 py-1.5 text-xs',
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg',
  }

  const glowClasses = glow ? 'shadow-glow hover:shadow-glow-lg' : ''
  const gradientClasses = gradient ? 'bg-gradient-cyber' : ''
  const fullWidthClasses = fullWidth ? 'w-full' : ''

  const buttonClasses = classNames(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    glowClasses,
    gradientClasses,
    fullWidthClasses,
    className
  )

  const iconClasses = classNames(
    'flex-shrink-0',
    {
      'mr-2': iconPosition === 'left' && children,
      'ml-2': iconPosition === 'right' && children,
    }
  )

  const LoadingSpinner = () => (
    <svg
      className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  )

  return (
    <motion.button
      className={buttonClasses}
      disabled={disabled || loading}
      whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
      whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
      transition={{ type: 'spring', stiffness: 400, damping: 17 }}
      {...props}
    >
      {/* Background gradient animation */}
      {gradient && (
        <div className="absolute inset-0 bg-gradient-cyber opacity-0 hover:opacity-100 transition-opacity duration-300" />
      )}
      
      {/* Content */}
      <span className="relative flex items-center">
        {loading && <LoadingSpinner />}
        {!loading && icon && iconPosition === 'left' && (
          <span className={iconClasses}>{icon}</span>
        )}
        {children}
        {!loading && icon && iconPosition === 'right' && (
          <span className={iconClasses}>{icon}</span>
        )}
      </span>
    </motion.button>
  )
}

export default Button
