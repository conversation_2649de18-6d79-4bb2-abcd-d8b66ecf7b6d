#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import json
import time

async def test_comprehensive_fixes():
    """Test all comprehensive fixes for real arbitrage detection"""
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("🔧 TESTING COMPREHENSIVE ARBITRAGE FIXES v3.0")
        print("=" * 70)
        
        # Wait for server
        print("⏳ Waiting for server...")
        for i in range(5):
            try:
                async with session.get(f"{base_url}/api/status") as response:
                    if response.status == 200:
                        print("✅ Server ready!")
                        break
            except:
                pass
            await asyncio.sleep(2)
        
        # Test 1: API Health Status
        print("\n📡 TEST 1: API Health & Multi-Source Integration")
        print("-" * 50)
        try:
            async with session.get(f"{base_url}/api/v3/api-health") as response:
                if response.status == 200:
                    data = await response.json()
                    
                    api_health = data.get('api_health', {})
                    print(f"   Overall API Health: {api_health.get('overall_health', 'Unknown')}")
                    print(f"   Healthy APIs: {api_health.get('healthy_apis', 0)}/{api_health.get('total_apis', 0)}")
                    
                    apis = api_health.get('apis', {})
                    for api_name, status in apis.items():
                        health_status = status.get('status', 'unknown')
                        error_count = status.get('error_count', 0)
                        print(f"   {api_name}: {health_status} (errors: {error_count})")
                    
                    data_quality = data.get('data_quality', {})
                    print(f"   Data Validation: {'✅' if data_quality.get('validation_enabled') else '❌'}")
                    print(f"   Multi-Source: {'✅' if data_quality.get('multi_source_consolidation') else '❌'}")
                    print(f"   Fallback Mechanisms: {'✅' if data_quality.get('fallback_mechanisms') else '❌'}")
                    
                    multi_api = data.get('multi_api_features', {})
                    print(f"   Enhanced DexScreener: {'✅' if multi_api.get('dexscreener_enhanced') else '❌'}")
                    print(f"   CoinGecko Integration: {'✅' if multi_api.get('coingecko_integration') else '❌'}")
                    print(f"   Cross-Validation: {'✅' if multi_api.get('cross_validation') else '❌'}")
                
                else:
                    print(f"   ❌ API Health check failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 2: Enhanced Profit Thresholds
        print("\n📊 TEST 2: Dynamic Profit Thresholds")
        print("-" * 50)
        try:
            async with session.get(f"{base_url}/api/v3/profit-thresholds") as response:
                if response.status == 200:
                    data = await response.json()
                    
                    thresholds = data.get('profit_thresholds', {})
                    volatility = thresholds.get('current_volatility', 'unknown')
                    threshold_data = thresholds.get('thresholds', {})
                    
                    print(f"   Market Volatility: {volatility}")
                    print(f"   Active Thresholds:")
                    
                    for category, thresh in list(threshold_data.items())[:8]:
                        if isinstance(thresh, dict):
                            min_thresh = thresh.get('min', 0)
                            max_thresh = thresh.get('max', 0)
                            print(f"      {category}: {min_thresh:.1f}% - {max_thresh:.1f}%")
                    
                    # Check if thresholds are low enough for detection
                    min_threshold = min(thresh.get('min', 999) for thresh in threshold_data.values() if isinstance(thresh, dict))
                    print(f"   Lowest Threshold: {min_threshold:.1f}% {'✅ Good' if min_threshold <= 0.5 else '⚠️ May be too high'}")
                
                else:
                    print(f"   ❌ Profit thresholds check failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 3: Enhanced Scanning with Multi-API
        print("\n🚀 TEST 3: Enhanced Multi-API Scanning")
        print("-" * 50)
        
        scan_start = time.time()
        try:
            async with session.get(f"{base_url}/api/scan") as response:
                scan_duration = time.time() - scan_start
                
                if response.status == 200:
                    data = await response.json()
                    
                    print(f"✅ Scan completed in {scan_duration:.2f} seconds")
                    print(f"📊 Status: {data.get('status', 'unknown')}")
                    
                    opportunities = data.get('opportunities', [])
                    print(f"🎯 Total Opportunities: {len(opportunities)}")
                    
                    if opportunities:
                        # Analyze opportunity types
                        real_count = sum(1 for opp in opportunities if not opp.get('is_demo', False))
                        demo_count = sum(1 for opp in opportunities if opp.get('is_demo', False))
                        
                        print(f"   Real Opportunities: {real_count}")
                        print(f"   Demo Opportunities: {demo_count}")
                        
                        # Analyze profit distribution
                        profits = [opp.get('profit_percentage', 0) for opp in opportunities]
                        if profits:
                            avg_profit = sum(profits) / len(profits)
                            max_profit = max(profits)
                            min_profit = min(profits)
                            
                            print(f"   Profit Range: {min_profit:.2f}% - {max_profit:.2f}%")
                            print(f"   Average Profit: {avg_profit:.2f}%")
                        
                        # Show top opportunities
                        print(f"\n💰 Top Opportunities:")
                        sorted_opps = sorted(opportunities, key=lambda x: x.get('profit_percentage', 0), reverse=True)
                        
                        for i, opp in enumerate(sorted_opps[:5]):
                            token = opp.get('token_symbol', 'Unknown')
                            profit = opp.get('profit_percentage', 0)
                            buy_exchange = opp.get('buy_exchange', 'Unknown')
                            sell_exchange = opp.get('sell_exchange', 'Unknown')
                            is_demo = opp.get('is_demo', False)
                            opp_type = opp.get('type', 'unknown')
                            validation_score = opp.get('validation_score', 0)
                            
                            demo_indicator = "🧪 DEMO" if is_demo else "🔴 REAL"
                            
                            print(f"   {i+1}. {token}: {profit:.2f}% profit {demo_indicator}")
                            print(f"      {buy_exchange} → {sell_exchange}")
                            print(f"      Type: {opp_type} | Validation: {validation_score}")
                        
                        # Analyze data sources
                        if real_count > 0:
                            print(f"\n📡 Data Source Analysis:")
                            source_types = {}
                            for opp in opportunities:
                                if not opp.get('is_demo', False):
                                    sources = opp.get('data_sources', ['unknown'])
                                    for source in sources:
                                        source_types[source] = source_types.get(source, 0) + 1
                            
                            for source, count in source_types.items():
                                print(f"   {source}: {count} opportunities")
                    
                    else:
                        print("   ❌ No opportunities found")
                        print("   🔍 Checking scan logs for detailed analysis...")
                    
                    # Check scan summary
                    if 'summary' in data:
                        summary = data['summary']
                        print(f"\n📈 Scan Summary:")
                        print(f"   Tokens scanned: {summary.get('tokens_scanned', 0)}")
                        print(f"   Performance: {summary.get('tokens_per_second', 0):.1f} tokens/sec")
                        print(f"   Cache hit rate: {summary.get('cache_hit_rate', 0):.1f}%")
                
                else:
                    print(f"❌ Scan failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"❌ Scan error: {e}")
        
        # Test 4: Performance Metrics
        print("\n📊 TEST 4: Enhanced Performance Metrics")
        print("-" * 50)
        try:
            async with session.get(f"{base_url}/api/v3/performance-metrics") as response:
                if response.status == 200:
                    data = await response.json()
                    
                    metrics = data.get('performance_metrics', {})
                    scan_perf = metrics.get('scan_performance', {})
                    v3_features = metrics.get('v3_features', {})
                    
                    print(f"   Scan Performance:")
                    print(f"      Tokens/sec: {scan_perf.get('tokens_per_second', 0):.1f}")
                    print(f"      Cache hit rate: {scan_perf.get('cache_hit_rate', 0):.1f}%")
                    print(f"      Success rate: {scan_perf.get('success_rate', 0):.1f}%")
                    
                    print(f"   v3.0 Features:")
                    print(f"      Demo mode: {'✅' if v3_features.get('enable_demo_mode') else '❌'}")
                    print(f"      Multi-API: {'✅' if v3_features.get('v3_config_exists') else '❌'}")
                    print(f"      Dynamic discovery: {'✅' if v3_features.get('dynamic_discovery_enabled') else '❌'}")
                    print(f"      Tier management: {'✅' if v3_features.get('tier_management_enabled') else '❌'}")
                
                else:
                    print(f"   ❌ Performance metrics failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print("\n" + "=" * 70)
        print("🎯 COMPREHENSIVE FIXES TEST COMPLETE")
        
        # Final Assessment
        if opportunities and len(opportunities) > 0:
            real_opportunities = [opp for opp in opportunities if not opp.get('is_demo', False)]
            demo_opportunities = [opp for opp in opportunities if opp.get('is_demo', False)]
            
            if real_opportunities:
                print("🎉 SUCCESS: Real arbitrage opportunities detected!")
                print(f"   Found {len(real_opportunities)} real opportunities")
                print("   ✅ Multi-API data provider working")
                print("   ✅ Enhanced validation working")
                print("   ✅ Profit thresholds optimized")
            elif demo_opportunities:
                print("🧪 PARTIAL SUCCESS: Demo mode working correctly")
                print(f"   Generated {len(demo_opportunities)} demo opportunities")
                print("   ✅ Fallback system working")
                print("   ⚠️ Real data may need further optimization")
            else:
                print("❌ ISSUE: No opportunities found")
        else:
            print("❌ CRITICAL: No opportunities detected at all")
            print("   🔧 Further debugging required")

if __name__ == "__main__":
    asyncio.run(test_comprehensive_fixes())
