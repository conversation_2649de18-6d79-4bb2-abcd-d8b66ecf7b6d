import React from 'react'
import { NavLink, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import Badge from '@components/ui/Badge'
import {
  HomeIcon,
  ChartBarIcon,
  CogIcon,
  XMarkIcon,
  CpuChipIcon,
  GlobeAltIcon,
  BoltIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
  wsConnected: boolean
  isScanning: boolean
}

const Sidebar: React.FC<SidebarProps> = ({
  isOpen,
  onClose,
  wsConnected,
  isScanning
}) => {
  const location = useLocation()

  const navigation = [
    {
      name: 'Dashboard',
      href: '/',
      icon: HomeIcon,
      description: 'Overview and opportunities'
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: ChartBarIcon,
      description: 'Charts and insights'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: CogIcon,
      description: 'Configuration'
    },
  ]

  const features = [
    {
      name: 'ML Enhanced',
      icon: CpuChipIcon,
      status: 'active',
      description: 'Machine learning analysis'
    },
    {
      name: 'Multi-Chain',
      icon: GlobeAltIcon,
      status: 'active',
      description: 'Cross-chain arbitrage'
    },
    {
      name: 'Real-time',
      icon: BoltIcon,
      status: wsConnected ? 'active' : 'inactive',
      description: 'Live market data'
    },
    {
      name: 'Risk Analysis',
      icon: ShieldCheckIcon,
      status: 'active',
      description: 'Advanced validation'
    },
  ]

  const sidebarVariants = {
    open: {
      width: 280,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      width: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 }
  }

  return (
    <motion.div
      variants={sidebarVariants}
      initial="closed"
      animate={isOpen ? "open" : "closed"}
      className="relative h-full bg-dark-900/90 backdrop-blur-lg border-r border-dark-700/50 overflow-hidden"
    >
      <div className="flex flex-col h-full w-70">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-dark-700/50">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-cyber rounded-lg flex items-center justify-center">
              <BoltIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gradient">ArbiBot</h2>
              <p className="text-xs text-gray-400">v2.0 Advanced</p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="p-1 rounded-lg hover:bg-dark-800 transition-colors lg:hidden"
          >
            <XMarkIcon className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* Status */}
        <div className="p-4 border-b border-dark-700/50">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Scanning Status</span>
              <Badge 
                variant={isScanning ? 'success' : 'default'} 
                pulse={isScanning}
                size="xs"
              >
                {isScanning ? 'Active' : 'Stopped'}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Connection</span>
              <Badge 
                variant={wsConnected ? 'success' : 'error'} 
                pulse={!wsConnected}
                size="xs"
              >
                {wsConnected ? 'Online' : 'Offline'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          <div className="text-xs font-medium text-gray-400 uppercase tracking-wider mb-4">
            Navigation
          </div>
          
          {navigation.map((item, index) => {
            const isActive = location.pathname === item.href
            
            return (
              <motion.div
                key={item.name}
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                transition={{ delay: index * 0.1 }}
              >
                <NavLink
                  to={item.href}
                  className={({ isActive }) =>
                    `group flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 ${
                      isActive
                        ? 'bg-gradient-to-r from-neon-purple/20 to-neon-blue/20 text-white border border-neon-purple/30 shadow-glow-sm'
                        : 'text-gray-300 hover:text-white hover:bg-dark-800/50'
                    }`
                  }
                >
                  <item.icon
                    className={`mr-3 h-5 w-5 transition-colors ${
                      isActive ? 'text-neon-blue' : 'text-gray-400 group-hover:text-gray-300'
                    }`}
                  />
                  <div className="flex-1">
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs text-gray-500 group-hover:text-gray-400">
                      {item.description}
                    </div>
                  </div>
                  
                  {isActive && (
                    <motion.div
                      layoutId="activeIndicator"
                      className="w-2 h-2 bg-neon-blue rounded-full"
                      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    />
                  )}
                </NavLink>
              </motion.div>
            )
          })}
        </nav>

        {/* Features */}
        <div className="p-4 border-t border-dark-700/50">
          <div className="text-xs font-medium text-gray-400 uppercase tracking-wider mb-4">
            Features
          </div>
          
          <div className="space-y-3">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                transition={{ delay: (navigation.length + index) * 0.1 }}
                className="flex items-center space-x-3"
              >
                <div className={`p-1.5 rounded-lg ${
                  feature.status === 'active' 
                    ? 'bg-success/20 text-success' 
                    : 'bg-gray-700/50 text-gray-400'
                }`}>
                  <feature.icon className="w-4 h-4" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-white">
                    {feature.name}
                  </div>
                  <div className="text-xs text-gray-500 truncate">
                    {feature.description}
                  </div>
                </div>
                
                <div className={`w-2 h-2 rounded-full ${
                  feature.status === 'active' ? 'bg-success' : 'bg-gray-600'
                }`} />
              </motion.div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-dark-700/50">
          <div className="text-center">
            <div className="text-xs text-gray-500">
              Powered by Advanced ML
            </div>
            <div className="text-xs text-gray-600 mt-1">
              © 2024 ArbiBot Pro
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default Sidebar
