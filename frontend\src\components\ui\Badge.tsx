import React from 'react'
import { motion } from 'framer-motion'
import classNames from 'classnames'

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info' | 'purple' | 'cyan' | 'gradient'
  size?: 'xs' | 'sm' | 'md' | 'lg'
  dot?: boolean
  pulse?: boolean
  glow?: boolean
  icon?: React.ReactNode
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'sm',
  dot = false,
  pulse = false,
  glow = false,
  icon,
  className,
  ...props
}) => {
  const baseClasses = [
    'inline-flex items-center justify-center',
    'font-medium rounded-full transition-all duration-200',
    'whitespace-nowrap',
  ]

  const variantClasses = {
    default: [
      'bg-dark-700 text-gray-300 border border-dark-600',
    ],
    success: [
      'bg-success/20 text-success border border-success/30',
      glow ? 'shadow-neon-green' : '',
    ],
    warning: [
      'bg-warning/20 text-warning border border-warning/30',
    ],
    error: [
      'bg-error/20 text-error border border-error/30',
    ],
    info: [
      'bg-info/20 text-info border border-info/30',
    ],
    purple: [
      'bg-neon-purple/20 text-neon-purple border border-neon-purple/30',
      glow ? 'shadow-glow' : '',
    ],
    cyan: [
      'bg-neon-cyan/20 text-neon-cyan border border-neon-cyan/30',
      glow ? 'shadow-neon-blue' : '',
    ],
    gradient: [
      'bg-gradient-to-r from-neon-purple to-neon-blue text-white border border-transparent',
      glow ? 'shadow-glow' : '',
    ],
  }

  const sizeClasses = {
    xs: dot ? 'w-2 h-2' : 'px-2 py-0.5 text-xs',
    sm: dot ? 'w-2.5 h-2.5' : 'px-2.5 py-1 text-xs',
    md: dot ? 'w-3 h-3' : 'px-3 py-1.5 text-sm',
    lg: dot ? 'w-4 h-4' : 'px-4 py-2 text-base',
  }

  const pulseClasses = pulse ? 'animate-pulse' : ''

  const badgeClasses = classNames(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    pulseClasses,
    className
  )

  const iconClasses = classNames(
    'flex-shrink-0',
    {
      'mr-1': icon && children && size !== 'xs',
      'mr-0.5': icon && children && size === 'xs',
    }
  )

  if (dot) {
    return (
      <motion.span
        className={badgeClasses}
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: 'spring', stiffness: 500, damping: 30 }}
        {...props}
      />
    )
  }

  return (
    <motion.span
      className={badgeClasses}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ type: 'spring', stiffness: 500, damping: 30 }}
      {...props}
    >
      {icon && <span className={iconClasses}>{icon}</span>}
      {children}
    </motion.span>
  )
}

// Predefined badge components for common use cases
export const StatusBadge: React.FC<{
  status: 'active' | 'inactive' | 'pending' | 'error'
  children?: React.ReactNode
}> = ({ status, children }) => {
  const statusConfig = {
    active: { variant: 'success' as const, text: 'Active' },
    inactive: { variant: 'default' as const, text: 'Inactive' },
    pending: { variant: 'warning' as const, text: 'Pending' },
    error: { variant: 'error' as const, text: 'Error' },
  }

  const config = statusConfig[status]

  return (
    <Badge variant={config.variant} pulse={status === 'pending'}>
      {children || config.text}
    </Badge>
  )
}

export const ProfitBadge: React.FC<{
  profit: number
  showSign?: boolean
}> = ({ profit, showSign = true }) => {
  const isPositive = profit > 0
  const isNeutral = profit === 0

  return (
    <Badge
      variant={isPositive ? 'success' : isNeutral ? 'default' : 'error'}
      glow={Math.abs(profit) > 5}
    >
      {showSign && (isPositive ? '+' : '')}{profit.toFixed(2)}%
    </Badge>
  )
}

export const RiskBadge: React.FC<{
  risk: 'low' | 'medium' | 'high' | 'very_high'
}> = ({ risk }) => {
  const riskConfig = {
    low: { variant: 'success' as const, text: 'Low Risk' },
    medium: { variant: 'warning' as const, text: 'Medium Risk' },
    high: { variant: 'error' as const, text: 'High Risk' },
    very_high: { variant: 'error' as const, text: 'Very High Risk' },
  }

  const config = riskConfig[risk]

  return (
    <Badge variant={config.variant} pulse={risk === 'very_high'}>
      {config.text}
    </Badge>
  )
}

export const ChainBadge: React.FC<{
  chain: string
  icon?: React.ReactNode
}> = ({ chain, icon }) => {
  const chainColors = {
    ethereum: 'purple',
    bsc: 'warning',
    polygon: 'purple',
    arbitrum: 'cyan',
    avalanche: 'error',
    solana: 'gradient',
  } as const

  const variant = chainColors[chain as keyof typeof chainColors] || 'default'

  return (
    <Badge variant={variant} icon={icon}>
      {chain.toUpperCase()}
    </Badge>
  )
}

export const MLScoreBadge: React.FC<{
  score: number
  confidence?: number
}> = ({ score, confidence }) => {
  const getVariant = () => {
    if (score >= 0.8) return 'success'
    if (score >= 0.6) return 'warning'
    if (score >= 0.4) return 'info'
    return 'default'
  }

  const getGlow = () => score >= 0.8 && (confidence || 0) >= 0.7

  return (
    <Badge variant={getVariant()} glow={getGlow()}>
      ML: {(score * 100).toFixed(0)}%
    </Badge>
  )
}

export default Badge
