# 🔧 Troubleshooting Guide - Crypto Arbitrage Bot v2.0

## 🚨 **<PERSON><PERSON><PERSON> yang Anda Alami: Scikit-learn Compilation Error**

### **<PERSON><PERSON><PERSON>b Masalah**
Error yang Anda alami disebabkan oleh:
1. **Cython compilation error** saat menginstall scikit-learn
2. **Missing C++ compiler** atau build tools
3. **Incompatible package versions**

### **✅ Solusi Cepat (Recommended)**

#### **Opsi 1: Gunakan Versi Simplified**
```bash
# Install dependencies minimal (tanpa ML yang berat)
python install_simple.py

# Jalankan versi simplified
python main_simple.py
```

#### **Opsi 2: Install Pre-compiled Packages**
```bash
# Install scikit-learn pre-compiled
pip install --only-binary=all scikit-learn

# Atau gunakan conda (lebih reliable)
conda install scikit-learn pandas numpy
```

#### **Opsi 3: Skip ML Dependencies**
```bash
# Install hanya core dependencies
pip install fastapi uvicorn httpx pydantic python-multipart

# Jalankan tanpa ML features
python main_simple.py
```

## 🛠️ **Detailed Solutions**

### **1. Install Microsoft Visual C++ Build Tools (Windows)**

Jika Anda ingin menggunakan full version dengan ML:

```bash
# Download dan install Visual Studio Build Tools
# https://visualstudio.microsoft.com/visual-cpp-build-tools/

# Atau install via chocolatey
choco install visualstudio2022buildtools --package-parameters "--add Microsoft.VisualStudio.Workload.VCTools"
```

### **2. Use Conda Instead of Pip**

```bash
# Install miniconda jika belum ada
# https://docs.conda.io/en/latest/miniconda.html

# Create new environment
conda create -n arbitrage python=3.11
conda activate arbitrage

# Install packages via conda (pre-compiled)
conda install fastapi uvicorn httpx pandas scikit-learn numpy
pip install python-multipart aiohttp websockets
```

### **3. Use Docker (Easiest)**

```bash
# Build dan run dengan Docker
docker-compose up -d

# Atau build manual
docker build -t arbitrage-bot .
docker run -p 8000:8000 arbitrage-bot
```

### **4. Install Dependencies One by One**

```bash
# Install core packages first
pip install fastapi
pip install uvicorn[standard]
pip install httpx
pip install pydantic
pip install python-multipart

# Test if basic app works
python main_simple.py

# Then add optional packages
pip install aiohttp
pip install websockets
pip install python-dateutil
```

## 🎯 **Quick Start Commands**

### **Untuk Mengatasi Error Anda Sekarang:**

```bash
# 1. Bersihkan pip cache
pip cache purge

# 2. Upgrade pip dan setuptools
python -m pip install --upgrade pip setuptools wheel

# 3. Install minimal requirements
pip install -r requirements-minimal.txt

# 4. Jalankan versi simplified
python main_simple.py
```

### **Atau Gunakan Script Installer:**

```bash
# Jalankan installer otomatis
python install_simple.py
```

## 📊 **Versi Simplified vs Full**

### **Simplified Version (main_simple.py)**
- ✅ **No compilation issues**
- ✅ **Minimal dependencies**
- ✅ **Core arbitrage detection**
- ✅ **Public APIs integration**
- ✅ **Web interface**
- ❌ Advanced ML features
- ❌ Database integration
- ❌ Complex analytics

### **Full Version (main.py)**
- ✅ **Complete ML integration**
- ✅ **Database support**
- ✅ **Advanced analytics**
- ✅ **Sentiment analysis**
- ❌ Requires compilation
- ❌ More dependencies
- ❌ Potential compatibility issues

## 🔍 **Debugging Steps**

### **1. Check Python Environment**
```bash
python --version          # Should be 3.8+
pip --version            # Should be latest
pip list                 # Check installed packages
```

### **2. Check Compiler Availability**
```bash
# Windows
where cl                 # Should find MSVC compiler

# Linux/Mac
which gcc               # Should find GCC compiler
gcc --version           # Check version
```

### **3. Test Individual Components**
```bash
# Test FastAPI
python -c "import fastapi; print('FastAPI OK')"

# Test HTTP clients
python -c "import httpx; print('HTTPX OK')"

# Test if simplified version works
python -c "
import asyncio
import httpx

async def test():
    async with httpx.AsyncClient() as client:
        response = await client.get('https://api.coingecko.com/api/v3/ping')
        print('CoinGecko API:', response.status_code)

asyncio.run(test())
"
```

## 🚀 **Recommended Workflow**

### **Step 1: Start with Simplified Version**
```bash
# Clone repository
cd "C:\Users\<USER>\project 4"

# Install minimal deps
python install_simple.py

# Start simplified bot
python main_simple.py
```

### **Step 2: Test Basic Functionality**
- Open http://localhost:8000
- Click "Scan for Opportunities"
- Verify API connections work

### **Step 3: Upgrade to Full Version (Optional)**
```bash
# If simplified works, try full version
conda install scikit-learn pandas numpy
python main.py
```

## 📞 **Still Having Issues?**

### **Common Error Messages & Solutions**

#### **"Microsoft Visual C++ 14.0 is required"**
```bash
# Solution: Install Visual Studio Build Tools
# Or use pre-compiled packages
pip install --only-binary=all scikit-learn
```

#### **"Failed building wheel for scikit-learn"**
```bash
# Solution: Use conda or pre-compiled
conda install scikit-learn
# Or skip ML features
python main_simple.py
```

#### **"No module named 'sklearn'"**
```bash
# Solution: Install scikit-learn
pip install scikit-learn
# Or use simplified version without ML
```

#### **"Port 8000 already in use"**
```bash
# Solution: Use different port
python main_simple.py --port 8001
# Or kill existing process
netstat -ano | findstr :8000
taskkill /PID <PID> /F
```

## 🎉 **Success Indicators**

You'll know it's working when you see:

```
🚀 Starting Crypto Arbitrage Bot v2.0 - Simplified Version
📊 Using public APIs: CoinGecko, DexScreener, Binance
🌐 Web interface: http://localhost:8000
📚 API docs: http://localhost:8000/docs
============================================================
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000
```

## 💡 **Pro Tips**

1. **Always start with simplified version first**
2. **Use conda for ML packages when possible**
3. **Docker is most reliable for complex setups**
4. **Check firewall/antivirus if connection issues**
5. **Use virtual environment to avoid conflicts**

---

**🎯 Bottom Line: Use `python main_simple.py` to get started quickly without compilation issues!**
