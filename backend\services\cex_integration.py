"""
Centralized Exchange (CEX) integration for arbitrage detection
"""
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from backend.services.api_client import APIClientFactory
from backend.core.logging import get_logger
from backend.core.cache import cache
from backend.config.settings import settings

logger = get_logger(__name__)


class CEXManager:
    """Manages multiple CEX integrations"""
    
    def __init__(self):
        self.supported_exchanges = {
            "binance": {
                "name": "Binance",
                "api_client": "binance",
                "base_pairs": ["USDT", "BUSD", "BTC", "ETH"],
                "fee_percentage": 0.1,  # 0.1% trading fee
                "withdrawal_fees": {
                    "BTC": 0.0005,
                    "ETH": 0.005,
                    "USDT": 1.0,
                    "USDC": 1.0
                }
            },
            "coinbase": {
                "name": "Coinbase Pro",
                "api_client": "coinbase",
                "base_pairs": ["USD", "USDC", "BTC", "ETH"],
                "fee_percentage": 0.5,  # 0.5% trading fee
                "withdrawal_fees": {
                    "BTC": 0.0005,
                    "ETH": 0.005,
                    "USDT": 2.5,
                    "USDC": 2.5
                }
            }
        }
        self.clients = {}
    
    async def initialize(self):
        """Initialize CEX API clients"""
        try:
            # Initialize Binance client
            if settings.BINANCE_API_KEY:
                self.clients["binance"] = await APIClientFactory.get_client("binance")
                logger.info("Binance client initialized")
            
            # Initialize Coinbase client (if implemented)
            if settings.COINBASE_API_KEY:
                # self.clients["coinbase"] = await APIClientFactory.get_client("coinbase")
                logger.info("Coinbase client would be initialized here")
            
        except Exception as e:
            logger.error(f"Error initializing CEX clients: {e}")
    
    async def get_cex_prices(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get current prices from all available CEXs"""
        all_prices = {}
        
        # Get prices from each exchange
        for exchange_id, exchange_config in self.supported_exchanges.items():
            if exchange_id not in self.clients:
                continue
            
            try:
                exchange_prices = await self._get_exchange_prices(exchange_id, symbols)
                
                for symbol, price_data in exchange_prices.items():
                    if symbol not in all_prices:
                        all_prices[symbol] = {}
                    
                    all_prices[symbol][exchange_id] = {
                        **price_data,
                        "exchange": exchange_id,
                        "exchange_name": exchange_config["name"],
                        "trading_fee": exchange_config["fee_percentage"]
                    }
                    
            except Exception as e:
                logger.error(f"Error getting prices from {exchange_id}: {e}")
                continue
        
        return all_prices
    
    async def _get_exchange_prices(self, exchange_id: str, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get prices from a specific exchange"""
        if exchange_id == "binance":
            return await self._get_binance_prices(symbols)
        elif exchange_id == "coinbase":
            return await self._get_coinbase_prices(symbols)
        else:
            return {}
    
    async def _get_binance_prices(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get prices from Binance"""
        client = self.clients.get("binance")
        if not client:
            return {}
        
        prices = {}
        
        # Convert symbols to Binance format (add USDT)
        binance_symbols = []
        for symbol in symbols:
            if symbol.upper() not in ["USDT", "BUSD", "BTC", "ETH"]:
                binance_symbols.append(f"{symbol.upper()}USDT")
        
        if not binance_symbols:
            return prices
        
        try:
            # Get 24hr ticker data for all symbols
            all_tickers = await client.get_ticker_24hr()
            
            if not all_tickers:
                return prices
            
            # Filter for requested symbols
            for ticker in all_tickers:
                symbol = ticker.get("symbol", "")
                
                if symbol in binance_symbols:
                    base_symbol = symbol.replace("USDT", "").replace("BUSD", "")
                    
                    # Get order book for spread calculation
                    order_book = await client.get_order_book(symbol, limit=5)
                    
                    spread = 0.0
                    if order_book and order_book.get("bids") and order_book.get("asks"):
                        best_bid = float(order_book["bids"][0][0])
                        best_ask = float(order_book["asks"][0][0])
                        spread = ((best_ask - best_bid) / best_ask) * 100
                    
                    prices[base_symbol] = {
                        "symbol": symbol,
                        "price": float(ticker.get("lastPrice", 0)),
                        "bid": float(order_book["bids"][0][0]) if order_book and order_book.get("bids") else 0,
                        "ask": float(order_book["asks"][0][0]) if order_book and order_book.get("asks") else 0,
                        "spread_percentage": spread,
                        "volume_24h": float(ticker.get("volume", 0)),
                        "quote_volume_24h": float(ticker.get("quoteVolume", 0)),
                        "price_change_24h": float(ticker.get("priceChangePercent", 0)),
                        "high_24h": float(ticker.get("highPrice", 0)),
                        "low_24h": float(ticker.get("lowPrice", 0)),
                        "timestamp": int(datetime.utcnow().timestamp() * 1000)
                    }
        
        except Exception as e:
            logger.error(f"Error getting Binance prices: {e}")
        
        return prices
    
    async def _get_coinbase_prices(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get prices from Coinbase (placeholder implementation)"""
        # This would be implemented with actual Coinbase Pro API
        return {}
    
    async def calculate_cex_dex_arbitrage(
        self, 
        token_symbol: str,
        dex_prices: Dict[str, Any],  # From DexScreener
        amount_usd: float = 1000
    ) -> List[Dict[str, Any]]:
        """Calculate CEX vs DEX arbitrage opportunities"""
        
        opportunities = []
        
        # Get CEX prices
        cex_prices = await self.get_cex_prices([token_symbol])
        token_cex_data = cex_prices.get(token_symbol, {})
        
        if not token_cex_data:
            return opportunities
        
        # Compare each CEX with DEX prices
        for exchange_id, cex_data in token_cex_data.items():
            cex_price = cex_data.get("price", 0)
            if cex_price <= 0:
                continue
            
            # Compare with each DEX
            for dex_pair in dex_prices.get("all_pairs", []):
                dex_price = dex_pair.get("priceUsd")
                if not dex_price:
                    continue
                
                dex_price = float(dex_price)
                dex_liquidity = dex_pair.get("liquidity", {}).get("usd", 0)
                
                # Skip low liquidity pairs
                if dex_liquidity < 10000:  # Minimum $10k liquidity
                    continue
                
                # Calculate arbitrage opportunities
                opportunities.extend(
                    self._calculate_cex_dex_opportunity(
                        token_symbol, exchange_id, cex_data, dex_pair, amount_usd
                    )
                )
        
        # Sort by profit and return top opportunities
        opportunities.sort(key=lambda x: x.get("net_profit_percentage", 0), reverse=True)
        return opportunities[:20]  # Top 20 opportunities
    
    def _calculate_cex_dex_opportunity(
        self,
        token_symbol: str,
        exchange_id: str,
        cex_data: Dict[str, Any],
        dex_pair: Dict[str, Any],
        amount_usd: float
    ) -> List[Dict[str, Any]]:
        """Calculate specific CEX-DEX arbitrage opportunity"""
        
        opportunities = []
        
        cex_price = cex_data.get("price", 0)
        dex_price = float(dex_pair.get("priceUsd", 0))
        
        if cex_price <= 0 or dex_price <= 0:
            return opportunities
        
        exchange_config = self.supported_exchanges.get(exchange_id, {})
        
        # CEX to DEX arbitrage (buy CEX, sell DEX)
        if cex_price < dex_price:
            gross_profit_percentage = ((dex_price - cex_price) / cex_price) * 100
            
            # Calculate costs
            cex_trading_fee = exchange_config.get("fee_percentage", 0.1)
            dex_trading_fee = 0.3  # Typical DEX fee
            
            # Withdrawal fees (convert to percentage)
            withdrawal_fee_usd = exchange_config.get("withdrawal_fees", {}).get(token_symbol, 0)
            withdrawal_fee_percentage = (withdrawal_fee_usd / amount_usd) * 100 if amount_usd > 0 else 0
            
            # Gas fees for DEX transaction
            chain_id = dex_pair.get("chainId", "ethereum")
            gas_cost_usd = self._estimate_dex_gas_cost(chain_id)
            gas_cost_percentage = (gas_cost_usd / amount_usd) * 100 if amount_usd > 0 else 0
            
            total_cost_percentage = (
                cex_trading_fee + 
                dex_trading_fee + 
                withdrawal_fee_percentage + 
                gas_cost_percentage
            )
            
            net_profit_percentage = gross_profit_percentage - total_cost_percentage
            
            if net_profit_percentage > 0.5:  # Minimum 0.5% profit
                opportunities.append({
                    "type": "cex_to_dex",
                    "token_symbol": token_symbol,
                    "buy_exchange": exchange_id,
                    "sell_dex": dex_pair.get("dexId", ""),
                    "buy_price": cex_price,
                    "sell_price": dex_price,
                    "chain_id": dex_pair.get("chainId", ""),
                    "gross_profit_percentage": gross_profit_percentage,
                    "net_profit_percentage": net_profit_percentage,
                    "costs": {
                        "cex_trading_fee_percentage": cex_trading_fee,
                        "dex_trading_fee_percentage": dex_trading_fee,
                        "withdrawal_fee_percentage": withdrawal_fee_percentage,
                        "gas_cost_percentage": gas_cost_percentage,
                        "total_cost_percentage": total_cost_percentage
                    },
                    "liquidity_usd": dex_pair.get("liquidity", {}).get("usd", 0),
                    "execution_steps": [
                        f"Buy {token_symbol} on {exchange_config.get('name', exchange_id)}",
                        f"Withdraw {token_symbol} to {chain_id} wallet",
                        f"Sell {token_symbol} on {dex_pair.get('dexId', '')} DEX"
                    ],
                    "estimated_time_minutes": self._estimate_cex_dex_time(exchange_id, chain_id),
                    "risk_level": self._assess_cex_dex_risk(exchange_id, chain_id),
                    "amount_usd": amount_usd
                })
        
        # DEX to CEX arbitrage (buy DEX, sell CEX)
        if dex_price < cex_price:
            gross_profit_percentage = ((cex_price - dex_price) / dex_price) * 100
            
            # Calculate costs
            dex_trading_fee = 0.3  # Typical DEX fee
            cex_trading_fee = exchange_config.get("fee_percentage", 0.1)
            
            # Gas fees for DEX transaction
            chain_id = dex_pair.get("chainId", "ethereum")
            gas_cost_usd = self._estimate_dex_gas_cost(chain_id)
            gas_cost_percentage = (gas_cost_usd / amount_usd) * 100 if amount_usd > 0 else 0
            
            # Deposit fees (usually free but include time cost)
            deposit_time_cost = 0.1  # 0.1% for time/opportunity cost
            
            total_cost_percentage = (
                dex_trading_fee + 
                cex_trading_fee + 
                gas_cost_percentage + 
                deposit_time_cost
            )
            
            net_profit_percentage = gross_profit_percentage - total_cost_percentage
            
            if net_profit_percentage > 0.5:  # Minimum 0.5% profit
                opportunities.append({
                    "type": "dex_to_cex",
                    "token_symbol": token_symbol,
                    "buy_dex": dex_pair.get("dexId", ""),
                    "sell_exchange": exchange_id,
                    "buy_price": dex_price,
                    "sell_price": cex_price,
                    "chain_id": dex_pair.get("chainId", ""),
                    "gross_profit_percentage": gross_profit_percentage,
                    "net_profit_percentage": net_profit_percentage,
                    "costs": {
                        "dex_trading_fee_percentage": dex_trading_fee,
                        "cex_trading_fee_percentage": cex_trading_fee,
                        "gas_cost_percentage": gas_cost_percentage,
                        "deposit_time_cost_percentage": deposit_time_cost,
                        "total_cost_percentage": total_cost_percentage
                    },
                    "liquidity_usd": dex_pair.get("liquidity", {}).get("usd", 0),
                    "execution_steps": [
                        f"Buy {token_symbol} on {dex_pair.get('dexId', '')} DEX",
                        f"Transfer {token_symbol} to {exchange_config.get('name', exchange_id)}",
                        f"Sell {token_symbol} on {exchange_config.get('name', exchange_id)}"
                    ],
                    "estimated_time_minutes": self._estimate_dex_cex_time(chain_id, exchange_id),
                    "risk_level": self._assess_cex_dex_risk(exchange_id, chain_id),
                    "amount_usd": amount_usd
                })
        
        return opportunities
    
    def _estimate_dex_gas_cost(self, chain_id: str) -> float:
        """Estimate gas cost for DEX transaction"""
        gas_costs = {
            "ethereum": 50.0,  # $50 for ETH mainnet
            "bsc": 1.0,        # $1 for BSC
            "polygon": 0.5,    # $0.50 for Polygon
            "arbitrum": 2.0,   # $2 for Arbitrum
            "avalanche": 3.0,  # $3 for Avalanche
            "solana": 0.01     # $0.01 for Solana
        }
        
        return gas_costs.get(chain_id, 10.0)  # Default $10
    
    def _estimate_cex_dex_time(self, exchange_id: str, chain_id: str) -> int:
        """Estimate execution time for CEX to DEX arbitrage"""
        # Base times in minutes
        cex_withdrawal_times = {
            "binance": 30,  # 30 minutes for withdrawal processing
            "coinbase": 60  # 60 minutes for withdrawal processing
        }
        
        chain_confirmation_times = {
            "ethereum": 15,   # 15 minutes for confirmations
            "bsc": 5,         # 5 minutes
            "polygon": 5,     # 5 minutes
            "arbitrum": 10,   # 10 minutes
            "avalanche": 5,   # 5 minutes
            "solana": 2       # 2 minutes
        }
        
        withdrawal_time = cex_withdrawal_times.get(exchange_id, 45)
        confirmation_time = chain_confirmation_times.get(chain_id, 10)
        
        return withdrawal_time + confirmation_time + 5  # +5 minutes for DEX execution
    
    def _estimate_dex_cex_time(self, chain_id: str, exchange_id: str) -> int:
        """Estimate execution time for DEX to CEX arbitrage"""
        # Base times in minutes
        chain_confirmation_times = {
            "ethereum": 15,   # 15 minutes for confirmations
            "bsc": 5,         # 5 minutes
            "polygon": 5,     # 5 minutes
            "arbitrum": 10,   # 10 minutes
            "avalanche": 5,   # 5 minutes
            "solana": 2       # 2 minutes
        }
        
        cex_deposit_times = {
            "binance": 10,  # 10 minutes for deposit processing
            "coinbase": 15  # 15 minutes for deposit processing
        }
        
        confirmation_time = chain_confirmation_times.get(chain_id, 10)
        deposit_time = cex_deposit_times.get(exchange_id, 15)
        
        return 5 + confirmation_time + deposit_time  # +5 minutes for DEX execution
    
    def _assess_cex_dex_risk(self, exchange_id: str, chain_id: str) -> str:
        """Assess risk level for CEX-DEX arbitrage"""
        risk_score = 0
        
        # Exchange risk
        if exchange_id == "binance":
            risk_score += 0  # Low risk
        else:
            risk_score += 1  # Medium risk for other exchanges
        
        # Chain risk
        if chain_id in ["ethereum", "bsc"]:
            risk_score += 0  # Low risk
        elif chain_id in ["polygon", "arbitrum", "avalanche"]:
            risk_score += 1  # Medium risk
        else:
            risk_score += 2  # High risk
        
        # Time exposure risk
        if chain_id == "ethereum":
            risk_score += 1  # Higher risk due to longer confirmation times
        
        if risk_score <= 1:
            return "low"
        elif risk_score <= 2:
            return "medium"
        else:
            return "high"


# Global CEX manager instance
cex_manager = CEXManager()
