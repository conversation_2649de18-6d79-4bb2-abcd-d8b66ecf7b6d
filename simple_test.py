#!/usr/bin/env python3
"""
Simple test script to verify basic functionality
"""
import sys
import json

def test_basic_imports():
    """Test if basic Python modules work"""
    print("🐍 Testing Python environment...")
    print(f"   Python version: {sys.version}")
    
    try:
        import asyncio
        print("   ✅ asyncio available")
    except ImportError:
        print("   ❌ asyncio not available")
        return False
    
    try:
        import json
        print("   ✅ json available")
    except ImportError:
        print("   ❌ json not available")
        return False
    
    return True

def test_http_libraries():
    """Test HTTP libraries"""
    print("\n🌐 Testing HTTP libraries...")
    
    libraries = ['httpx', 'aiohttp', 'requests']
    available = []
    
    for lib in libraries:
        try:
            __import__(lib)
            print(f"   ✅ {lib} available")
            available.append(lib)
        except ImportError:
            print(f"   ❌ {lib} not available")
    
    return len(available) > 0

def test_fastapi():
    """Test FastAPI"""
    print("\n⚡ Testing FastAPI...")
    
    try:
        import fastapi
        print(f"   ✅ FastAPI {fastapi.__version__} available")
        return True
    except ImportError:
        print("   ❌ FastAPI not available")
        return False

def test_public_api_simulation():
    """Simulate public API calls without actual HTTP requests"""
    print("\n🧪 Testing Public API simulation...")
    
    # Simulate CoinGecko response
    coingecko_response = {
        "bitcoin": {
            "usd": 45000,
            "usd_24h_change": 2.5
        },
        "ethereum": {
            "usd": 3000,
            "usd_24h_change": 1.8
        }
    }
    print(f"   ✅ CoinGecko simulation: BTC=${coingecko_response['bitcoin']['usd']}")
    
    # Simulate DexScreener response
    dexscreener_response = {
        "pairs": [
            {
                "baseToken": {"symbol": "USDC", "address": "0xa0b86a33e6..."},
                "priceUsd": "1.0001",
                "liquidity": {"usd": 150000},
                "volume": {"h24": 50000}
            }
        ]
    }
    print(f"   ✅ DexScreener simulation: {len(dexscreener_response['pairs'])} pairs")
    
    # Simulate Binance response
    binance_response = {
        "symbol": "BTCUSDT",
        "lastPrice": "45000.00",
        "volume": "25000.50",
        "priceChangePercent": "2.50"
    }
    print(f"   ✅ Binance simulation: {binance_response['symbol']} = ${binance_response['lastPrice']}")
    
    # Simulate Reddit response
    reddit_response = {
        "data": {
            "children": [
                {"data": {"title": "Bitcoin to the moon!", "score": 150}},
                {"data": {"title": "Crypto market analysis", "score": 89}},
                {"data": {"title": "DeFi opportunities", "score": 67}}
            ]
        }
    }
    print(f"   ✅ Reddit simulation: {len(reddit_response['data']['children'])} posts")
    
    return True

def test_arbitrage_logic():
    """Test basic arbitrage calculation logic"""
    print("\n💰 Testing Arbitrage Logic...")
    
    # Sample price data
    prices = {
        "binance": {"BTCUSDT": 45000},
        "uniswap": {"BTC/USDT": 45100},
        "pancakeswap": {"BTC/USDT": 44950}
    }
    
    # Find arbitrage opportunities
    opportunities = []
    
    binance_price = prices["binance"]["BTCUSDT"]
    uniswap_price = prices["uniswap"]["BTC/USDT"]
    pancakeswap_price = prices["pancakeswap"]["BTC/USDT"]
    
    # Binance -> Uniswap
    if uniswap_price > binance_price:
        profit_pct = ((uniswap_price - binance_price) / binance_price) * 100
        opportunities.append({
            "buy_exchange": "binance",
            "sell_exchange": "uniswap", 
            "profit_percentage": profit_pct
        })
    
    # Pancakeswap -> Binance
    if binance_price > pancakeswap_price:
        profit_pct = ((binance_price - pancakeswap_price) / pancakeswap_price) * 100
        opportunities.append({
            "buy_exchange": "pancakeswap",
            "sell_exchange": "binance",
            "profit_percentage": profit_pct
        })
    
    print(f"   ✅ Found {len(opportunities)} arbitrage opportunities")
    for opp in opportunities:
        print(f"      Buy on {opp['buy_exchange']}, sell on {opp['sell_exchange']}: {opp['profit_percentage']:.2f}% profit")
    
    return len(opportunities) > 0

def test_sentiment_analysis():
    """Test basic sentiment analysis logic"""
    print("\n🧠 Testing Sentiment Analysis...")
    
    # Sample Reddit posts
    posts = [
        "Bitcoin is going to the moon! 🚀 HODL diamond hands",
        "Market crash incoming, sell everything now!",
        "DeFi yields are looking good, bullish on ETH",
        "Bear market continues, red everywhere",
        "Crypto adoption is growing, very bullish long term"
    ]
    
    positive_keywords = ["moon", "bullish", "hodl", "diamond hands", "good", "growing"]
    negative_keywords = ["crash", "sell", "bear", "red", "down"]
    
    sentiment_scores = []
    
    for post in posts:
        post_lower = post.lower()
        positive_count = sum(1 for keyword in positive_keywords if keyword in post_lower)
        negative_count = sum(1 for keyword in negative_keywords if keyword in post_lower)
        
        if positive_count > negative_count:
            sentiment = "bullish"
            score = 1
        elif negative_count > positive_count:
            sentiment = "bearish"
            score = -1
        else:
            sentiment = "neutral"
            score = 0
        
        sentiment_scores.append(score)
        print(f"      '{post[:50]}...' -> {sentiment}")
    
    overall_sentiment = sum(sentiment_scores) / len(sentiment_scores)
    sentiment_label = "bullish" if overall_sentiment > 0 else "bearish" if overall_sentiment < 0 else "neutral"
    
    print(f"   ✅ Overall sentiment: {sentiment_label} (score: {overall_sentiment:.2f})")
    
    return True

def main():
    """Run all tests"""
    print("🧪 Crypto Arbitrage Bot v2.0 - Simple Test Suite")
    print("=" * 60)
    
    tests = [
        ("Basic Python Environment", test_basic_imports),
        ("HTTP Libraries", test_http_libraries),
        ("FastAPI Framework", test_fastapi),
        ("Public API Simulation", test_public_api_simulation),
        ("Arbitrage Logic", test_arbitrage_logic),
        ("Sentiment Analysis", test_sentiment_analysis),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"   ❌ {test_name} failed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed! Core logic is working.")
        print("\n🚀 Next steps:")
        print("   1. Install dependencies: pip install -r requirements.txt")
        print("   2. Test public APIs: python test_public_apis.py")
        print("   3. Start application: python start.py")
    else:
        print("⚠️  Some tests failed. Check the environment setup.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}")
