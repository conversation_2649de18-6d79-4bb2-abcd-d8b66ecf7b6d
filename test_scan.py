#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import json
import time

async def test_v3_scanning():
    """Test v3.0 scanning functionality"""
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("🚀 Testing Enhanced Crypto Arbitrage Bot v3.0 Scanning\n")
        
        # Test scan endpoint
        print("🔍 Starting v3.0 tier-based scan...")
        scan_start = time.time()
        
        try:
            async with session.get(f"{base_url}/api/scan") as response:
                if response.status == 200:
                    data = await response.json()
                    scan_duration = time.time() - scan_start
                    
                    print(f"✅ Scan completed in {scan_duration:.2f} seconds")
                    print(f"📊 Status: {data.get('status', 'unknown')}")
                    
                    if 'opportunities' in data:
                        opportunities = data['opportunities']
                        print(f"🎯 Opportunities found: {len(opportunities)}")
                        
                        if opportunities:
                            # Show first few opportunities
                            for i, opp in enumerate(opportunities[:3]):
                                token = opp.get('token_symbol', 'Unknown')
                                profit = opp.get('profit_percentage', 0)
                                buy_exchange = opp.get('buy_exchange', 'Unknown')
                                sell_exchange = opp.get('sell_exchange', 'Unknown')
                                tier = opp.get('tier', 'N/A')
                                
                                print(f"   {i+1}. {token}: {profit:.2f}% profit")
                                print(f"      Buy: {buy_exchange} → Sell: {sell_exchange}")
                                print(f"      Tier: {tier}")
                                print()
                    
                    if 'summary' in data:
                        summary = data['summary']
                        print(f"📈 Summary:")
                        print(f"   Total opportunities: {summary.get('total_opportunities', 0)}")
                        print(f"   Average profit: {summary.get('average_profit', 0):.2f}%")
                        print(f"   Tokens scanned: {summary.get('tokens_scanned', 0)}")
                        print(f"   Scan performance: {summary.get('tokens_per_second', 0):.1f} tokens/sec")
                
                else:
                    print(f"❌ Scan failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"❌ Scan error: {e}")
        
        print("\n" + "="*60)
        
        # Test performance metrics
        print("📊 Testing performance metrics...")
        
        try:
            async with session.get(f"{base_url}/api/v3/performance-metrics") as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'performance_metrics' in data:
                        metrics = data['performance_metrics']
                        
                        # Scan performance
                        scan_perf = metrics.get('scan_performance', {})
                        print(f"⚡ Scan Performance:")
                        print(f"   Tokens/second: {scan_perf.get('tokens_per_second', 0):.1f}")
                        print(f"   Cache hit rate: {scan_perf.get('cache_hit_rate', 0):.1f}%")
                        print(f"   Success rate: {scan_perf.get('success_rate', 0):.1f}%")
                        
                        # v3.0 features
                        v3_features = metrics.get('v3_features', {})
                        print(f"\n🚀 v3.0 Features:")
                        print(f"   Dynamic discovery: {v3_features.get('dynamic_discovery_enabled', False)}")
                        print(f"   Tier management: {v3_features.get('tier_management_enabled', False)}")
                        print(f"   Adaptive thresholds: {v3_features.get('adaptive_thresholds_enabled', False)}")
                        print(f"   Max tokens: {v3_features.get('max_total_tokens', 0)}")
                
                else:
                    print(f"❌ Performance metrics failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"❌ Performance metrics error: {e}")
        
        print("\n🎯 v3.0 Scanning Test Complete!")

if __name__ == "__main__":
    asyncio.run(test_v3_scanning())
