import React, { useMemo } from 'react'
import { <PERSON>, <PERSON>att<PERSON> } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  LinearScale,
  CategoryScale,
} from 'chart.js'
import { motion } from 'framer-motion'
import Card, { CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import Badge from '@components/ui/Badge'
import { ArbitrageOpportunity } from '@types/index'
import { formatPercentage } from '@utils/formatters'

ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  LinearScale,
  CategoryScale
)

interface MLInsightsChartProps {
  opportunities: ArbitrageOpportunity[]
  chartType: 'radar' | 'scatter'
}

const MLInsightsChart: React.FC<MLInsightsChartProps> = ({
  opportunities,
  chartType
}) => {
  const { radarData, scatterData, mlStats } = useMemo(() => {
    // Filter opportunities with ML data
    const mlOpportunities = opportunities.filter(opp => opp.ml_score)

    if (mlOpportunities.length === 0) {
      return { radarData: null, scatterData: null, mlStats: null }
    }

    // Calculate average ML factors
    const avgFactors = mlOpportunities.reduce(
      (acc, opp) => {
        const ml = opp.ml_score!
        return {
          profit: acc.profit + ml.profit_score,
          prediction: acc.prediction + ml.prediction_factor,
          sentiment: acc.sentiment + ml.sentiment_factor,
          pattern: acc.pattern + ml.pattern_factor,
          volume: acc.volume + ml.volume_factor,
          risk: acc.risk + ml.risk_factor,
          confidence: acc.confidence + ml.confidence,
        }
      },
      { profit: 0, prediction: 0, sentiment: 0, pattern: 0, volume: 0, risk: 0, confidence: 0 }
    )

    const count = mlOpportunities.length
    Object.keys(avgFactors).forEach(key => {
      avgFactors[key as keyof typeof avgFactors] /= count
    })

    // Radar chart data
    const radar = {
      labels: [
        'Profit Score',
        'Price Prediction',
        'Sentiment',
        'Pattern Analysis',
        'Volume Factor',
        'Risk Assessment'
      ],
      datasets: [
        {
          label: 'ML Analysis',
          data: [
            avgFactors.profit,
            avgFactors.prediction,
            avgFactors.sentiment,
            avgFactors.pattern,
            avgFactors.volume,
            avgFactors.risk,
          ],
          backgroundColor: 'rgba(139, 92, 246, 0.2)',
          borderColor: 'rgba(139, 92, 246, 1)',
          borderWidth: 2,
          pointBackgroundColor: 'rgba(139, 92, 246, 1)',
          pointBorderColor: 'rgba(255, 255, 255, 0.8)',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6,
        },
      ],
    }

    // Scatter chart data (ML Score vs Profit)
    const scatter = {
      datasets: [
        {
          label: 'High Confidence',
          data: mlOpportunities
            .filter(opp => opp.ml_score!.confidence >= 0.7)
            .map(opp => ({
              x: opp.ml_score!.total_score,
              y: opp.profit_percentage,
              token: opp.token_symbol,
              confidence: opp.ml_score!.confidence,
            })),
          backgroundColor: 'rgba(16, 185, 129, 0.6)',
          borderColor: 'rgba(16, 185, 129, 1)',
          borderWidth: 2,
          pointRadius: 6,
          pointHoverRadius: 8,
        },
        {
          label: 'Medium Confidence',
          data: mlOpportunities
            .filter(opp => opp.ml_score!.confidence >= 0.4 && opp.ml_score!.confidence < 0.7)
            .map(opp => ({
              x: opp.ml_score!.total_score,
              y: opp.profit_percentage,
              token: opp.token_symbol,
              confidence: opp.ml_score!.confidence,
            })),
          backgroundColor: 'rgba(245, 158, 11, 0.6)',
          borderColor: 'rgba(245, 158, 11, 1)',
          borderWidth: 2,
          pointRadius: 5,
          pointHoverRadius: 7,
        },
        {
          label: 'Low Confidence',
          data: mlOpportunities
            .filter(opp => opp.ml_score!.confidence < 0.4)
            .map(opp => ({
              x: opp.ml_score!.total_score,
              y: opp.profit_percentage,
              token: opp.token_symbol,
              confidence: opp.ml_score!.confidence,
            })),
          backgroundColor: 'rgba(239, 68, 68, 0.6)',
          borderColor: 'rgba(239, 68, 68, 1)',
          borderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6,
        },
      ],
    }

    // ML Statistics
    const stats = {
      totalAnalyzed: mlOpportunities.length,
      avgConfidence: avgFactors.confidence,
      highConfidenceCount: mlOpportunities.filter(opp => opp.ml_score!.confidence >= 0.7).length,
      avgMLScore: mlOpportunities.reduce((sum, opp) => sum + opp.ml_score!.total_score, 0) / mlOpportunities.length,
      topFactors: [
        { name: 'Profit Score', value: avgFactors.profit },
        { name: 'Prediction', value: avgFactors.prediction },
        { name: 'Sentiment', value: avgFactors.sentiment },
        { name: 'Pattern', value: avgFactors.pattern },
        { name: 'Volume', value: avgFactors.volume },
        { name: 'Risk', value: avgFactors.risk },
      ].sort((a, b) => b.value - a.value),
    }

    return { radarData: radar, scatterData: scatter, mlStats: stats }
  }, [opportunities])

  const radarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Inter',
            size: 12,
          },
        },
      },
      tooltip: {
        backgroundColor: 'rgba(15, 15, 35, 0.95)',
        titleColor: 'rgba(255, 255, 255, 0.9)',
        bodyColor: 'rgba(255, 255, 255, 0.8)',
        borderColor: 'rgba(139, 92, 246, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context: any) {
            return `${context.label}: ${context.parsed.r.toFixed(3)}`
          }
        }
      },
    },
    scales: {
      r: {
        beginAtZero: true,
        max: 2,
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
        angleLines: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
        pointLabels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Inter',
            size: 11,
          },
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Inter',
            size: 10,
          },
          stepSize: 0.5,
        },
      },
    },
  }

  const scatterOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Inter',
            size: 12,
          },
          usePointStyle: true,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(15, 15, 35, 0.95)',
        titleColor: 'rgba(255, 255, 255, 0.9)',
        bodyColor: 'rgba(255, 255, 255, 0.8)',
        borderColor: 'rgba(139, 92, 246, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          title: function(context: any) {
            const point = context[0].raw
            return point.token || 'Unknown Token'
          },
          label: function(context: any) {
            const point = context.raw
            return [
              `ML Score: ${point.x.toFixed(3)}`,
              `Profit: ${formatPercentage(point.y)}`,
              `Confidence: ${formatPercentage(point.confidence * 100)}`
            ]
          }
        }
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'ML Score',
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Inter',
            size: 12,
          },
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
          drawBorder: false,
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Inter',
            size: 11,
          },
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Profit Percentage',
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Inter',
            size: 12,
          },
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
          drawBorder: false,
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Inter',
            size: 11,
          },
          callback: function(value: any) {
            return formatPercentage(value)
          }
        },
      },
    },
  }

  if (!mlStats) {
    return (
      <Card variant="glass" className="h-96">
        <CardContent className="h-full flex items-center justify-center">
          <div className="text-center text-gray-400">
            <div className="text-lg mb-2">No ML Data Available</div>
            <div className="text-sm">Start scanning to see ML insights</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Chart */}
      <div className="lg:col-span-2">
        <Card variant="glass" className="h-96">
          <CardHeader>
            <CardTitle>
              ML Analysis {chartType === 'radar' ? 'Overview' : 'Correlation'}
            </CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
              className="h-full"
            >
              {chartType === 'radar' ? (
                <Radar data={radarData!} options={radarOptions} />
              ) : (
                <Scatter data={scatterData!} options={scatterOptions} />
              )}
            </motion.div>
          </CardContent>
        </Card>
      </div>

      {/* ML Stats */}
      <Card variant="glass" className="h-96">
        <CardHeader>
          <CardTitle>ML Insights</CardTitle>
        </CardHeader>
        <CardContent className="h-80 overflow-y-auto">
          <div className="space-y-4">
            {/* Overall Stats */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Analyzed</span>
                <Badge variant="info">{mlStats.totalAnalyzed}</Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Avg Confidence</span>
                <Badge variant="success">
                  {formatPercentage(mlStats.avgConfidence * 100)}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">High Confidence</span>
                <Badge variant="purple">
                  {mlStats.highConfidenceCount}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Avg ML Score</span>
                <Badge variant="gradient">
                  {mlStats.avgMLScore.toFixed(3)}
                </Badge>
              </div>
            </div>

            <hr className="border-dark-600/50" />

            {/* Top Factors */}
            <div>
              <div className="text-sm font-medium text-white mb-3">
                Top ML Factors
              </div>
              <div className="space-y-2">
                {mlStats.topFactors.slice(0, 3).map((factor, index) => (
                  <motion.div
                    key={factor.name}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex justify-between items-center"
                  >
                    <span className="text-xs text-gray-400">
                      {factor.name}
                    </span>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 h-2 bg-dark-700 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-neon-purple to-neon-blue rounded-full transition-all duration-500"
                          style={{ width: `${Math.min(100, (factor.value / 2) * 100)}%` }}
                        />
                      </div>
                      <span className="text-xs text-white font-medium">
                        {factor.value.toFixed(2)}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default MLInsightsChart
