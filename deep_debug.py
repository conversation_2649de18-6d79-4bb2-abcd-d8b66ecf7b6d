#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import json
import time

async def deep_debug_dexscreener():
    """Deep debug of DexScreener API responses"""
    
    print("🔍 DEEP DEBUG: DexScreener API Analysis")
    print("=" * 70)
    
    # Test popular tokens that should have arbitrage opportunities
    test_tokens = ["USDC", "USDT", "WETH", "WBTC", "SOL", "BNB", "MATIC"]
    test_chains = ["ethereum", "bsc", "polygon", "solana"]
    
    async with aiohttp.ClientSession() as session:
        
        for token in test_tokens[:3]:  # Test first 3 tokens
            print(f"\n🎯 Testing Token: {token}")
            print("-" * 50)
            
            token_pairs = {}
            
            for chain in test_chains:
                try:
                    # Direct DexScreener API call
                    url = f"https://api.dexscreener.com/latest/dex/search"
                    params = {"q": token, "chain": chain}
                    
                    print(f"   📡 Fetching {chain} data for {token}...")
                    
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            pairs = data.get('pairs', [])
                            
                            if pairs:
                                print(f"   ✅ {chain}: Found {len(pairs)} pairs")
                                
                                # Analyze first few pairs
                                valid_pairs = []
                                for pair in pairs[:5]:  # Check first 5 pairs
                                    base_token = pair.get('baseToken', {})
                                    quote_token = pair.get('quoteToken', {})
                                    
                                    if base_token.get('symbol', '').upper() == token.upper():
                                        price_usd = pair.get('priceUsd')
                                        liquidity = pair.get('liquidity', {}).get('usd', 0)
                                        volume_24h = pair.get('volume', {}).get('h24', 0)
                                        dex_id = pair.get('dexId', 'unknown')
                                        
                                        if price_usd and liquidity > 1000 and volume_24h > 100:
                                            valid_pairs.append({
                                                'dex': dex_id,
                                                'price': float(price_usd),
                                                'liquidity': liquidity,
                                                'volume_24h': volume_24h,
                                                'quote': quote_token.get('symbol', 'unknown')
                                            })
                                
                                if valid_pairs:
                                    token_pairs[chain] = valid_pairs
                                    print(f"      Valid pairs: {len(valid_pairs)}")
                                    for pair in valid_pairs[:2]:  # Show first 2
                                        print(f"         {pair['dex']}: ${pair['price']:.6f} (${pair['liquidity']:,.0f} liq)")
                                else:
                                    print(f"      ❌ No valid pairs found")
                            else:
                                print(f"   ❌ {chain}: No pairs found")
                        else:
                            print(f"   ❌ {chain}: HTTP {response.status}")
                            
                except Exception as e:
                    print(f"   ❌ {chain}: Error - {e}")
                
                await asyncio.sleep(0.5)  # Rate limiting
            
            # Analyze arbitrage opportunities for this token
            if len(token_pairs) >= 2:
                print(f"\n   🔍 Arbitrage Analysis for {token}:")
                
                chains_with_data = list(token_pairs.keys())
                opportunities_found = 0
                
                for i, chain1 in enumerate(chains_with_data):
                    for chain2 in chains_with_data[i+1:]:
                        pairs1 = token_pairs[chain1]
                        pairs2 = token_pairs[chain2]
                        
                        for pair1 in pairs1:
                            for pair2 in pairs2:
                                price1 = pair1['price']
                                price2 = pair2['price']
                                
                                if price1 > 0 and price2 > 0:
                                    if price2 > price1:
                                        profit_pct = ((price2 - price1) / price1) * 100
                                        direction = f"{chain1} → {chain2}"
                                    else:
                                        profit_pct = ((price1 - price2) / price2) * 100
                                        direction = f"{chain2} → {chain1}"
                                    
                                    if profit_pct > 0.1:  # Very low threshold
                                        opportunities_found += 1
                                        print(f"      💰 {direction}: {profit_pct:.2f}% profit")
                                        print(f"         {pair1['dex']} vs {pair2['dex']}")
                                        print(f"         ${price1:.6f} vs ${price2:.6f}")
                
                if opportunities_found == 0:
                    print(f"      ❌ No arbitrage opportunities found for {token}")
                    
                    # Show price comparison anyway
                    all_prices = []
                    for chain, pairs in token_pairs.items():
                        for pair in pairs:
                            all_prices.append((chain, pair['dex'], pair['price']))
                    
                    if len(all_prices) >= 2:
                        print(f"      📊 Price comparison:")
                        for chain, dex, price in all_prices[:4]:
                            print(f"         {chain}/{dex}: ${price:.6f}")
                        
                        # Calculate max spread
                        prices = [p[2] for p in all_prices]
                        max_price = max(prices)
                        min_price = min(prices)
                        spread = ((max_price - min_price) / min_price) * 100
                        print(f"      📈 Max spread: {spread:.2f}%")
                        
                        if spread < 0.1:
                            print(f"         ⚠️  Spread too small for arbitrage")
            else:
                print(f"   ❌ Insufficient data for arbitrage analysis")
        
        print("\n" + "=" * 70)
        print("🎯 DEEP DEBUG ANALYSIS COMPLETE")
        print("\nKey Findings:")
        print("1. Check if DexScreener returns valid price data")
        print("2. Verify if price spreads exist between DEXs")
        print("3. Confirm if liquidity requirements are met")
        print("4. Analyze if our arbitrage logic is working correctly")

if __name__ == "__main__":
    asyncio.run(deep_debug_dexscreener())
