"""
Public API clients that don't require API keys
All endpoints used are free and publicly accessible
"""
import asyncio
import time
import json
from typing import Dict, Any, Optional, List
import httpx
from backend.core.logging import get_logger
from backend.core.cache import cache

logger = get_logger(__name__)


class RateLimiter:
    """Simple rate limiter for public APIs"""
    
    def __init__(self, requests_per_minute: int = 30):
        self.requests_per_minute = requests_per_minute
        self.tokens = requests_per_minute
        self.last_update = time.time()
        self.lock = asyncio.Lock()
    
    async def acquire(self) -> bool:
        """Acquire a token for making a request"""
        async with self.lock:
            now = time.time()
            time_passed = now - self.last_update
            self.tokens = min(
                self.requests_per_minute,
                self.tokens + time_passed * (self.requests_per_minute / 60.0)
            )
            self.last_update = now
            
            if self.tokens >= 1:
                self.tokens -= 1
                return True
            return False
    
    async def wait_for_token(self):
        """Wait until a token is available"""
        while not await self.acquire():
            await asyncio.sleep(1)


class CoinGeckoPublicClient:
    """CoinGecko public API client - no API key required"""
    
    def __init__(self):
        self.base_url = "https://api.coingecko.com/api/v3"
        self.rate_limiter = RateLimiter(10)  # Conservative 10 requests/minute
        self.client = httpx.AsyncClient(
            timeout=30.0,
            headers={
                "User-Agent": "ArbitrageBot/2.0",
                "Accept": "application/json"
            }
        )
    
    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make rate-limited request to CoinGecko public API"""
        await self.rate_limiter.wait_for_token()
        
        if params is None:
            params = {}
        
        url = f"{self.base_url}/{endpoint}"
        cache_key = f"coingecko:{endpoint}:{json.dumps(params, sort_keys=True)}"
        
        # Check cache first
        cached_result = await cache.get(cache_key)
        if cached_result:
            return cached_result
        
        try:
            response = await self.client.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                # Cache for 5 minutes
                await cache.set(cache_key, data, ttl=300)
                return data
            elif response.status_code == 429:
                logger.warning("CoinGecko rate limit hit, waiting...")
                await asyncio.sleep(60)
                raise Exception("Rate limited by CoinGecko")
            else:
                raise Exception(f"CoinGecko API error: {response.status_code}")
                
        except Exception as e:
            logger.error(f"CoinGecko request failed: {e}")
            raise
    
    async def get_price(self, coin_ids: List[str], vs_currencies: List[str] = None) -> Dict[str, Any]:
        """Get current prices for coins"""
        if vs_currencies is None:
            vs_currencies = ["usd"]
        
        params = {
            "ids": ",".join(coin_ids),
            "vs_currencies": ",".join(vs_currencies),
            "include_24hr_change": "true",
            "include_24hr_vol": "true",
            "include_market_cap": "true"
        }
        
        return await self._make_request("simple/price", params)
    
    async def get_coins_list(self) -> List[Dict[str, Any]]:
        """Get list of all supported coins"""
        return await self._make_request("coins/list")
    
    async def get_coin_data(self, coin_id: str) -> Dict[str, Any]:
        """Get detailed coin data"""
        params = {
            "localization": "false",
            "tickers": "false",
            "market_data": "true",
            "community_data": "false",
            "developer_data": "false"
        }
        return await self._make_request(f"coins/{coin_id}", params)


class DexScreenerPublicClient:
    """DexScreener public API client - no API key required"""
    
    def __init__(self):
        self.base_url = "https://api.dexscreener.com/latest"
        self.rate_limiter = RateLimiter(60)  # 60 requests/minute allowed
        self.client = httpx.AsyncClient(
            timeout=30.0,
            headers={
                "User-Agent": "ArbitrageBot/2.0",
                "Accept": "application/json"
            }
        )
    
    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make request to DexScreener public API"""
        await self.rate_limiter.wait_for_token()
        
        url = f"{self.base_url}/{endpoint}"
        cache_key = f"dexscreener:{endpoint}:{json.dumps(params or {}, sort_keys=True)}"
        
        # Check cache first
        cached_result = await cache.get(cache_key)
        if cached_result:
            return cached_result
        
        try:
            response = await self.client.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                # Cache for 2 minutes (DexScreener data changes frequently)
                await cache.set(cache_key, data, ttl=120)
                return data
            else:
                raise Exception(f"DexScreener API error: {response.status_code}")
                
        except Exception as e:
            logger.error(f"DexScreener request failed: {e}")
            raise
    
    async def get_pairs_by_token(self, token_address: str) -> Dict[str, Any]:
        """Get trading pairs for a token address"""
        return await self._make_request(f"dex/tokens/{token_address}")
    
    async def search_pairs(self, query: str) -> Dict[str, Any]:
        """Search for trading pairs"""
        return await self._make_request(f"dex/search/?q={query}")
    
    async def get_pairs_by_chain(self, chain_id: str) -> Dict[str, Any]:
        """Get pairs by blockchain"""
        return await self._make_request(f"dex/pairs/{chain_id}")


class BinancePublicClient:
    """Binance public API client - no API key required"""
    
    def __init__(self):
        self.base_url = "https://api.binance.com/api/v3"
        self.rate_limiter = RateLimiter(1200)  # 1200 requests/minute for public endpoints
        self.client = httpx.AsyncClient(
            timeout=30.0,
            headers={
                "User-Agent": "ArbitrageBot/2.0",
                "Accept": "application/json"
            }
        )
    
    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make request to Binance public API"""
        await self.rate_limiter.wait_for_token()
        
        url = f"{self.base_url}/{endpoint}"
        cache_key = f"binance:{endpoint}:{json.dumps(params or {}, sort_keys=True)}"
        
        # Check cache first
        cached_result = await cache.get(cache_key)
        if cached_result:
            return cached_result
        
        try:
            response = await self.client.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                # Cache for 1 minute (price data changes frequently)
                await cache.set(cache_key, data, ttl=60)
                return data
            elif response.status_code == 429:
                logger.warning("Binance rate limit hit, waiting...")
                await asyncio.sleep(60)
                raise Exception("Rate limited by Binance")
            else:
                raise Exception(f"Binance API error: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Binance request failed: {e}")
            raise
    
    async def get_ticker_24hr(self, symbol: str = None) -> Dict[str, Any]:
        """Get 24hr ticker price change statistics"""
        params = {}
        if symbol:
            params["symbol"] = symbol
        
        return await self._make_request("ticker/24hr", params)
    
    async def get_orderbook(self, symbol: str, limit: int = 100) -> Dict[str, Any]:
        """Get order book for a symbol"""
        params = {
            "symbol": symbol,
            "limit": limit
        }
        return await self._make_request("depth", params)
    
    async def get_exchange_info(self) -> Dict[str, Any]:
        """Get exchange trading rules and symbol information"""
        return await self._make_request("exchangeInfo")


class RedditPublicClient:
    """Reddit public API client - no authentication required"""
    
    def __init__(self):
        self.base_url = "https://www.reddit.com"
        self.rate_limiter = RateLimiter(60)  # Conservative rate limiting
        self.client = httpx.AsyncClient(
            timeout=30.0,
            headers={
                "User-Agent": "ArbitrageBot/2.0 (by /u/arbitragebot)",
                "Accept": "application/json"
            }
        )
    
    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make request to Reddit public API"""
        await self.rate_limiter.wait_for_token()
        
        # Add .json to endpoint for JSON response
        if not endpoint.endswith('.json'):
            endpoint += '.json'
        
        url = f"{self.base_url}/{endpoint}"
        cache_key = f"reddit:{endpoint}:{json.dumps(params or {}, sort_keys=True)}"
        
        # Check cache first
        cached_result = await cache.get(cache_key)
        if cached_result:
            return cached_result
        
        try:
            response = await self.client.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                # Cache for 10 minutes
                await cache.set(cache_key, data, ttl=600)
                return data
            elif response.status_code == 429:
                logger.warning("Reddit rate limit hit, waiting...")
                await asyncio.sleep(60)
                raise Exception("Rate limited by Reddit")
            else:
                raise Exception(f"Reddit API error: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Reddit request failed: {e}")
            raise
    
    async def get_subreddit_posts(self, subreddit: str, sort: str = "hot", limit: int = 25) -> Dict[str, Any]:
        """Get posts from a subreddit"""
        params = {
            "limit": limit,
            "raw_json": 1  # Avoid HTML encoding
        }
        return await self._make_request(f"r/{subreddit}/{sort}", params)
    
    async def search_posts(self, query: str, subreddit: str = None, limit: int = 25) -> Dict[str, Any]:
        """Search for posts"""
        params = {
            "q": query,
            "limit": limit,
            "raw_json": 1,
            "sort": "relevance"
        }
        
        if subreddit:
            endpoint = f"r/{subreddit}/search"
            params["restrict_sr"] = "on"
        else:
            endpoint = "search"
        
        return await self._make_request(endpoint, params)


# Global instances
coingecko_client = CoinGeckoPublicClient()
dexscreener_client = DexScreenerPublicClient()
binance_client = BinancePublicClient()
reddit_client = RedditPublicClient()


async def cleanup_clients():
    """Cleanup all HTTP clients"""
    await coingecko_client.client.aclose()
    await dexscreener_client.client.aclose()
    await binance_client.client.aclose()
    await reddit_client.client.aclose()
