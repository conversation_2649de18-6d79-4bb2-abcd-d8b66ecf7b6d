#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import main_fixed

async def test_v3_logic_with_mock_data():
    """Test v3.0 logic with mock data to verify arbitrage detection works"""
    
    print("🧪 TESTING v3.0 LOGIC WITH MOCK DATA")
    print("=" * 60)
    
    # Create detector instance
    detector = main_fixed.AdvancedArbitrageDetector()
    
    # Mock data that should create arbitrage opportunities
    mock_pairs = [
        # USDC on different DEXs with price differences
        {
            'baseToken': {'symbol': 'USDC', 'address': '0x123'},
            'quoteToken': {'symbol': 'WETH', 'address': '0x456'},
            'chainId': 'ethereum',
            'dexId': 'uniswap_v3',
            'priceUsd': '0.999',  # Slightly below $1
            'liquidity': {'usd': 100000},
            'volume': {'h24': 50000},
            'pairAddress': '0xabc123'
        },
        {
            'baseToken': {'symbol': 'USDC', 'address': '0x123'},
            'quoteToken': {'symbol': 'WETH', 'address': '0x456'},
            'chainId': 'ethereum',
            'dexId': 'sushiswap',
            'priceUsd': '1.002',  # Slightly above $1
            'liquidity': {'usd': 80000},
            'volume': {'h24': 40000},
            'pairAddress': '0xdef456'
        },
        # SOL with bigger price difference
        {
            'baseToken': {'symbol': 'SOL', 'address': '0x789'},
            'quoteToken': {'symbol': 'USDC', 'address': '0x123'},
            'chainId': 'solana',
            'dexId': 'raydium',
            'priceUsd': '95.50',
            'liquidity': {'usd': 200000},
            'volume': {'h24': 100000},
            'pairAddress': 'sol123'
        },
        {
            'baseToken': {'symbol': 'SOL', 'address': '0x789'},
            'quoteToken': {'symbol': 'USDC', 'address': '0x123'},
            'chainId': 'solana',
            'dexId': 'orca',
            'priceUsd': '97.00',  # 1.57% higher
            'liquidity': {'usd': 150000},
            'volume': {'h24': 80000},
            'pairAddress': 'sol456'
        }
    ]
    
    print("📊 Mock Data Created:")
    print("   USDC: $0.999 (Uniswap) vs $1.002 (Sushiswap) = 0.30% spread")
    print("   SOL: $95.50 (Raydium) vs $97.00 (Orca) = 1.57% spread")
    
    print("\n🔍 Testing v3.0 arbitrage detection logic...")
    
    try:
        # Test the v3.0 arbitrage detection method
        tokens = ['USDC', 'SOL']
        opportunities = await detector._process_pairs_for_arbitrage_v3(mock_pairs, tokens, tier=1)
        
        print(f"✅ v3.0 Logic Test Complete")
        print(f"🎯 Opportunities found: {len(opportunities)}")
        
        if opportunities:
            print(f"\n💰 Detected Opportunities:")
            for i, opp in enumerate(opportunities):
                token = opp.get('token_symbol', 'Unknown')
                profit = opp.get('profit_percentage', 0)
                buy_exchange = opp.get('buy_exchange', 'Unknown')
                sell_exchange = opp.get('sell_exchange', 'Unknown')
                tier = opp.get('tier', 'N/A')
                
                print(f"   {i+1}. {token}: {profit:.2f}% profit (Tier {tier})")
                print(f"      Buy: {buy_exchange} → Sell: {sell_exchange}")
                print(f"      Type: {opp.get('type', 'unknown')}")
        else:
            print("   ❌ No opportunities detected from mock data")
            print("   🚨 This indicates a bug in the arbitrage detection logic!")
        
        # Test profit threshold logic
        print(f"\n📈 Testing profit threshold logic...")
        
        # Test USDC (stablecoins category)
        usdc_threshold = detector.profit_threshold_manager.get_profit_threshold('USDC', 'stablecoins')
        print(f"   USDC threshold: {usdc_threshold['min']:.1f}% - {usdc_threshold['max']:.1f}%")
        
        # Test SOL (solana_ecosystem category)
        sol_threshold = detector.profit_threshold_manager.get_profit_threshold('SOL', 'solana_ecosystem')
        print(f"   SOL threshold: {sol_threshold['min']:.1f}% - {sol_threshold['max']:.1f}%")
        
        # Check if our mock profits meet thresholds
        usdc_profit = 0.30  # 0.30% profit
        sol_profit = 1.57   # 1.57% profit
        
        usdc_meets = usdc_threshold['min'] <= usdc_profit <= usdc_threshold['max']
        sol_meets = sol_threshold['min'] <= sol_profit <= sol_threshold['max']
        
        print(f"   USDC 0.30% meets threshold: {'✅' if usdc_meets else '❌'}")
        print(f"   SOL 1.57% meets threshold: {'✅' if sol_meets else '❌'}")
        
        if not usdc_meets or not sol_meets:
            print("   ⚠️  Thresholds might still be too high!")
        
    except Exception as e:
        print(f"❌ Error testing v3.0 logic: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎯 MOCK DATA TEST COMPLETE")
    
    if opportunities:
        print("✅ v3.0 arbitrage logic is working correctly!")
        print("🔍 The issue is likely with DexScreener API data quality")
    else:
        print("❌ v3.0 arbitrage logic has bugs that need fixing")
        print("🔧 Focus on debugging the arbitrage detection algorithm")

if __name__ == "__main__":
    asyncio.run(test_v3_logic_with_mock_data())
