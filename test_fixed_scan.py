#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import json
import time

async def test_fixed_scan():
    """Test the fixed scanning with lower thresholds"""
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("🔍 TESTING FIXED SCAN WITH LOWER THRESHOLDS")
        print("=" * 60)
        
        # Wait for server
        print("⏳ Waiting for server...")
        for i in range(5):
            try:
                async with session.get(f"{base_url}/api/status") as response:
                    if response.status == 200:
                        print("✅ Server ready!")
                        break
            except:
                pass
            await asyncio.sleep(2)
        
        # Check current profit thresholds
        print("\n📊 Checking current profit thresholds...")
        try:
            async with session.get(f"{base_url}/api/v3/profit-thresholds") as response:
                if response.status == 200:
                    data = await response.json()
                    thresholds = data.get('profit_thresholds', {})
                    
                    volatility = thresholds.get('current_volatility', 'unknown')
                    threshold_data = thresholds.get('thresholds', {})
                    
                    print(f"   Market Volatility: {volatility}")
                    print(f"   Updated Thresholds:")
                    
                    for category, thresh in list(threshold_data.items())[:5]:
                        if isinstance(thresh, dict):
                            min_thresh = thresh.get('min', 0)
                            max_thresh = thresh.get('max', 0)
                            print(f"      {category}: {min_thresh:.1f}% - {max_thresh:.1f}%")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print("\n" + "=" * 60)
        print("🚀 Starting scan with fixed logic...")
        
        scan_start = time.time()
        try:
            async with session.get(f"{base_url}/api/scan") as response:
                scan_duration = time.time() - scan_start
                
                if response.status == 200:
                    data = await response.json()
                    
                    print(f"✅ Scan completed in {scan_duration:.2f} seconds")
                    print(f"📊 Status: {data.get('status', 'unknown')}")
                    
                    opportunities = data.get('opportunities', [])
                    print(f"🎯 Opportunities found: {len(opportunities)}")
                    
                    if opportunities:
                        print(f"\n💰 First few opportunities:")
                        for i, opp in enumerate(opportunities[:5]):
                            token = opp.get('token_symbol', 'Unknown')
                            profit = opp.get('profit_percentage', 0)
                            buy_exchange = opp.get('buy_exchange', 'Unknown')
                            sell_exchange = opp.get('sell_exchange', 'Unknown')
                            tier = opp.get('tier', 'N/A')
                            
                            print(f"   {i+1}. {token}: {profit:.2f}% profit (Tier {tier})")
                            print(f"      Buy: {buy_exchange} → Sell: {sell_exchange}")
                    else:
                        print("   ❌ No opportunities found despite lower thresholds")
                        print("   🔍 This suggests a deeper issue with data or logic")
                    
                    if 'summary' in data:
                        summary = data['summary']
                        print(f"\n📈 Summary:")
                        print(f"   Tokens scanned: {summary.get('tokens_scanned', 0)}")
                        print(f"   Scan performance: {summary.get('tokens_per_second', 0):.1f} tokens/sec")
                        print(f"   Cache hit rate: {summary.get('cache_hit_rate', 0):.1f}%")
                
                else:
                    print(f"❌ Scan failed: HTTP {response.status}")
                    
        except Exception as e:
            print(f"❌ Scan error: {e}")
        
        print("\n" + "=" * 60)
        print("🎯 ANALYSIS COMPLETE")
        
        if opportunities:
            print("✅ SUCCESS: Opportunities found with fixed logic!")
        else:
            print("❌ ISSUE: Still no opportunities found")
            print("   Possible causes:")
            print("   1. DexScreener API returning invalid data")
            print("   2. Chain filtering not working correctly")
            print("   3. Price comparison logic has bugs")
            print("   4. Liquidity/volume filters too restrictive")

if __name__ == "__main__":
    asyncio.run(test_fixed_scan())
