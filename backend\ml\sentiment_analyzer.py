"""
Sentiment analysis for crypto tokens using multiple sources
"""
import asyncio
import re
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import httpx
from textblob import TextBlob
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
from backend.core.logging import get_logger
from backend.core.cache import cache
from backend.config.settings import settings

logger = get_logger(__name__)


class SentimentAnalyzer:
    """Analyze sentiment from various sources"""
    
    def __init__(self):
        self.vader_analyzer = SentimentIntensityAnalyzer()
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def analyze_text_sentiment(self, text: str) -> Dict[str, float]:
        """Analyze sentiment of a single text using multiple methods"""
        if not text or not text.strip():
            return {"compound": 0.0, "positive": 0.0, "negative": 0.0, "neutral": 1.0}
        
        # Clean text
        cleaned_text = self._clean_text(text)
        
        # VADER sentiment
        vader_scores = self.vader_analyzer.polarity_scores(cleaned_text)
        
        # TextBlob sentiment
        try:
            blob = TextBlob(cleaned_text)
            textblob_polarity = blob.sentiment.polarity
            textblob_subjectivity = blob.sentiment.subjectivity
        except:
            textblob_polarity = 0.0
            textblob_subjectivity = 0.0
        
        # Combine scores
        combined_sentiment = {
            "compound": (vader_scores["compound"] + textblob_polarity) / 2,
            "positive": vader_scores["pos"],
            "negative": vader_scores["neg"],
            "neutral": vader_scores["neu"],
            "subjectivity": textblob_subjectivity,
            "confidence": abs(vader_scores["compound"]) * textblob_subjectivity
        }
        
        return combined_sentiment
    
    def _clean_text(self, text: str) -> str:
        """Clean and preprocess text for sentiment analysis"""
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove mentions and hashtags (but keep the text)
        text = re.sub(r'[@#](\w+)', r'\1', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Convert to lowercase
        text = text.lower()
        
        return text
    
    async def get_reddit_sentiment(self, token_symbol: str, limit: int = 100) -> Dict[str, Any]:
        """Get sentiment from Reddit (using public API)"""
        try:
            # Search for posts about the token
            search_terms = [token_symbol.lower(), f"${token_symbol.lower()}"]
            all_posts = []
            
            for term in search_terms:
                # Use Reddit's JSON API (public, no auth required)
                url = f"https://www.reddit.com/search.json"
                params = {
                    "q": term,
                    "sort": "new",
                    "limit": limit // len(search_terms),
                    "t": "day"  # Last 24 hours
                }
                
                try:
                    response = await self.client.get(url, params=params)
                    if response.status_code == 200:
                        data = response.json()
                        posts = data.get("data", {}).get("children", [])
                        all_posts.extend(posts)
                except Exception as e:
                    logger.warning(f"Error fetching Reddit data for {term}: {e}")
                    continue
                
                # Rate limiting
                await asyncio.sleep(1)
            
            if not all_posts:
                return {"sentiment_score": 0.0, "post_count": 0, "confidence": 0.0}
            
            # Analyze sentiment of posts
            sentiments = []
            for post in all_posts:
                post_data = post.get("data", {})
                title = post_data.get("title", "")
                selftext = post_data.get("selftext", "")
                
                # Combine title and text
                full_text = f"{title} {selftext}".strip()
                
                if full_text and len(full_text) > 10:  # Minimum text length
                    sentiment = self.analyze_text_sentiment(full_text)
                    sentiments.append(sentiment)
            
            if not sentiments:
                return {"sentiment_score": 0.0, "post_count": 0, "confidence": 0.0}
            
            # Calculate aggregate sentiment
            avg_compound = sum(s["compound"] for s in sentiments) / len(sentiments)
            avg_confidence = sum(s["confidence"] for s in sentiments) / len(sentiments)
            
            return {
                "sentiment_score": avg_compound,
                "post_count": len(sentiments),
                "confidence": avg_confidence,
                "source": "reddit"
            }
            
        except Exception as e:
            logger.error(f"Error getting Reddit sentiment for {token_symbol}: {e}")
            return {"sentiment_score": 0.0, "post_count": 0, "confidence": 0.0}
    
    async def get_twitter_sentiment(self, token_symbol: str) -> Dict[str, Any]:
        """Get sentiment from Twitter-like sources (simulated for demo)"""
        # Note: Real implementation would use Twitter API v2
        # For demo purposes, we'll simulate Twitter sentiment
        
        try:
            # Simulate Twitter sentiment based on token characteristics
            # In real implementation, this would fetch actual tweets
            
            # Generate simulated sentiment based on token symbol patterns
            base_sentiment = 0.0
            
            # Some heuristics for demo
            if len(token_symbol) <= 4:  # Established tokens tend to have shorter symbols
                base_sentiment += 0.1
            
            if token_symbol.upper() in ["BTC", "ETH", "BNB", "ADA", "SOL"]:
                base_sentiment += 0.2  # Major tokens tend to have positive sentiment
            
            # Add some randomness to simulate real sentiment variation
            import random
            random.seed(hash(token_symbol) % 1000)  # Deterministic randomness
            sentiment_variation = random.uniform(-0.3, 0.3)
            
            final_sentiment = max(-1.0, min(1.0, base_sentiment + sentiment_variation))
            
            return {
                "sentiment_score": final_sentiment,
                "tweet_count": random.randint(10, 100),
                "confidence": random.uniform(0.3, 0.8),
                "source": "twitter_simulated"
            }
            
        except Exception as e:
            logger.error(f"Error getting Twitter sentiment for {token_symbol}: {e}")
            return {"sentiment_score": 0.0, "tweet_count": 0, "confidence": 0.0}
    
    async def get_news_sentiment(self, token_symbol: str) -> Dict[str, Any]:
        """Get sentiment from crypto news sources"""
        try:
            # Use CryptoPanic API (free tier available)
            url = "https://cryptopanic.com/api/v1/posts/"
            params = {
                "auth_token": "free",  # Use free tier
                "currencies": token_symbol.upper(),
                "filter": "hot",
                "public": "true"
            }
            
            try:
                response = await self.client.get(url, params=params)
                if response.status_code == 200:
                    data = response.json()
                    posts = data.get("results", [])
                    
                    if not posts:
                        return {"sentiment_score": 0.0, "article_count": 0, "confidence": 0.0}
                    
                    # Analyze sentiment of news titles
                    sentiments = []
                    for post in posts:
                        title = post.get("title", "")
                        if title and len(title) > 10:
                            sentiment = self.analyze_text_sentiment(title)
                            
                            # Weight by vote count if available
                            votes = post.get("votes", {})
                            positive_votes = votes.get("positive", 0)
                            negative_votes = votes.get("negative", 0)
                            
                            # Adjust sentiment based on community votes
                            if positive_votes > 0 or negative_votes > 0:
                                vote_sentiment = (positive_votes - negative_votes) / (positive_votes + negative_votes + 1)
                                sentiment["compound"] = (sentiment["compound"] + vote_sentiment) / 2
                            
                            sentiments.append(sentiment)
                    
                    if not sentiments:
                        return {"sentiment_score": 0.0, "article_count": 0, "confidence": 0.0}
                    
                    # Calculate aggregate sentiment
                    avg_compound = sum(s["compound"] for s in sentiments) / len(sentiments)
                    avg_confidence = sum(s["confidence"] for s in sentiments) / len(sentiments)
                    
                    return {
                        "sentiment_score": avg_compound,
                        "article_count": len(sentiments),
                        "confidence": avg_confidence,
                        "source": "crypto_news"
                    }
                    
            except Exception as e:
                logger.warning(f"Error fetching news data: {e}")
                
            # Fallback: simulate news sentiment
            import random
            random.seed(hash(token_symbol) % 1000)
            
            return {
                "sentiment_score": random.uniform(-0.2, 0.2),
                "article_count": random.randint(1, 10),
                "confidence": random.uniform(0.2, 0.6),
                "source": "news_simulated"
            }
            
        except Exception as e:
            logger.error(f"Error getting news sentiment for {token_symbol}: {e}")
            return {"sentiment_score": 0.0, "article_count": 0, "confidence": 0.0}
    
    async def get_comprehensive_sentiment(self, token_symbol: str) -> Dict[str, Any]:
        """Get comprehensive sentiment analysis from multiple sources"""
        cache_key = f"sentiment:{token_symbol}"
        cached_result = await cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # Gather sentiment from all sources concurrently
        tasks = [
            self.get_reddit_sentiment(token_symbol),
            self.get_twitter_sentiment(token_symbol),
            self.get_news_sentiment(token_symbol)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        sentiment_sources = {}
        valid_sentiments = []
        
        source_names = ["reddit", "twitter", "news"]
        for i, result in enumerate(results):
            if isinstance(result, dict) and "sentiment_score" in result:
                sentiment_sources[source_names[i]] = result
                if result["confidence"] > 0.1:  # Only include confident sentiments
                    valid_sentiments.append(result)
            else:
                logger.warning(f"Error in {source_names[i]} sentiment: {result}")
        
        # Calculate weighted average sentiment
        if valid_sentiments:
            # Weight by confidence and source reliability
            source_weights = {"reddit": 0.4, "twitter": 0.3, "news": 0.3}
            
            total_weight = 0
            weighted_sentiment = 0
            
            for sentiment in valid_sentiments:
                source = sentiment.get("source", "").split("_")[0]  # Remove "_simulated" suffix
                weight = source_weights.get(source, 0.2) * sentiment["confidence"]
                weighted_sentiment += sentiment["sentiment_score"] * weight
                total_weight += weight
            
            final_sentiment = weighted_sentiment / total_weight if total_weight > 0 else 0.0
            overall_confidence = total_weight / sum(source_weights.values())
        else:
            final_sentiment = 0.0
            overall_confidence = 0.0
        
        # Classify sentiment
        if final_sentiment > 0.1:
            sentiment_label = "positive"
        elif final_sentiment < -0.1:
            sentiment_label = "negative"
        else:
            sentiment_label = "neutral"
        
        result = {
            "token_symbol": token_symbol,
            "overall_sentiment": {
                "score": final_sentiment,
                "label": sentiment_label,
                "confidence": overall_confidence
            },
            "sources": sentiment_sources,
            "analysis_timestamp": datetime.utcnow().isoformat(),
            "recommendation": self._get_sentiment_recommendation(final_sentiment, overall_confidence)
        }
        
        # Cache for 30 minutes
        await cache.set(cache_key, result, ttl=1800)
        
        return result
    
    def _get_sentiment_recommendation(self, sentiment_score: float, confidence: float) -> str:
        """Get trading recommendation based on sentiment"""
        if confidence < 0.3:
            return "insufficient_data"
        
        if sentiment_score > 0.3 and confidence > 0.6:
            return "strong_positive"
        elif sentiment_score > 0.1 and confidence > 0.4:
            return "moderate_positive"
        elif sentiment_score < -0.3 and confidence > 0.6:
            return "strong_negative"
        elif sentiment_score < -0.1 and confidence > 0.4:
            return "moderate_negative"
        else:
            return "neutral"


class SentimentService:
    """Service for managing sentiment analysis"""
    
    def __init__(self):
        self.analyzer = None
    
    async def get_sentiment_analysis(self, token_symbol: str) -> Dict[str, Any]:
        """Get sentiment analysis for a token"""
        if not self.analyzer:
            self.analyzer = SentimentAnalyzer()
        
        try:
            async with self.analyzer:
                return await self.analyzer.get_comprehensive_sentiment(token_symbol)
        except Exception as e:
            logger.error(f"Error in sentiment analysis for {token_symbol}: {e}")
            return {
                "token_symbol": token_symbol,
                "overall_sentiment": {
                    "score": 0.0,
                    "label": "neutral",
                    "confidence": 0.0
                },
                "sources": {},
                "analysis_timestamp": datetime.utcnow().isoformat(),
                "recommendation": "insufficient_data",
                "error": str(e)
            }
    
    async def get_batch_sentiment(self, token_symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get sentiment analysis for multiple tokens"""
        if not self.analyzer:
            self.analyzer = SentimentAnalyzer()
        
        results = {}
        
        # Process in batches to avoid overwhelming APIs
        batch_size = 5
        for i in range(0, len(token_symbols), batch_size):
            batch = token_symbols[i:i + batch_size]
            
            async with self.analyzer:
                tasks = [self.get_sentiment_analysis(symbol) for symbol in batch]
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for j, result in enumerate(batch_results):
                    symbol = batch[j]
                    if isinstance(result, dict):
                        results[symbol] = result
                    else:
                        results[symbol] = {
                            "token_symbol": symbol,
                            "overall_sentiment": {"score": 0.0, "label": "neutral", "confidence": 0.0},
                            "error": str(result)
                        }
            
            # Rate limiting between batches
            if i + batch_size < len(token_symbols):
                await asyncio.sleep(2)
        
        return results


# Global sentiment service
sentiment_service = SentimentService()
