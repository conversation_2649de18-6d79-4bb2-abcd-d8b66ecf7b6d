import numeral from 'numeral'
import { formatDistanceToNow, parseISO } from 'date-fns'

/**
 * Format currency values with appropriate precision and symbols
 */
export const formatCurrency = (
  value: number,
  currency: string = 'USD',
  compact: boolean = false
): string => {
  if (value === 0) return '$0.00'
  if (!isFinite(value)) return 'N/A'

  if (compact) {
    if (Math.abs(value) >= 1e9) {
      return `$${numeral(value).format('0.00a').toUpperCase()}`
    }
    if (Math.abs(value) >= 1e6) {
      return `$${numeral(value).format('0.00a').toUpperCase()}`
    }
    if (Math.abs(value) >= 1e3) {
      return `$${numeral(value).format('0.00a').toUpperCase()}`
    }
  }

  // For very small values, show more decimal places
  if (Math.abs(value) < 0.01) {
    return `$${numeral(value).format('0.0000')}`
  }
  
  // For small values, show 4 decimal places
  if (Math.abs(value) < 1) {
    return `$${numeral(value).format('0.0000')}`
  }

  // For regular values, show 2 decimal places
  return `$${numeral(value).format('0,0.00')}`
}

/**
 * Format percentage values with appropriate precision
 */
export const formatPercentage = (
  value: number,
  decimals: number = 2,
  showSign: boolean = true
): string => {
  if (!isFinite(value)) return 'N/A'

  const formatted = numeral(value).format(`0.${'0'.repeat(decimals)}`)
  const sign = showSign && value > 0 ? '+' : ''
  
  return `${sign}${formatted}%`
}

/**
 * Format large numbers with appropriate suffixes (K, M, B)
 */
export const formatNumber = (
  value: number,
  decimals: number = 2,
  compact: boolean = true
): string => {
  if (!isFinite(value)) return 'N/A'
  if (value === 0) return '0'

  if (compact) {
    return numeral(value).format('0.00a').toUpperCase()
  }

  return numeral(value).format(`0,0.${'0'.repeat(decimals)}`)
}

/**
 * Format time ago from ISO string
 */
export const formatTimeAgo = (isoString: string): string => {
  try {
    const date = parseISO(isoString)
    return formatDistanceToNow(date, { addSuffix: true })
  } catch {
    return 'Unknown'
  }
}

/**
 * Format duration in seconds to human readable format
 */
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`
  }
  
  if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.round(seconds % 60)
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`
  }
  
  const hours = Math.floor(seconds / 3600)
  const remainingMinutes = Math.floor((seconds % 3600) / 60)
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
}

/**
 * Format token symbol with proper casing
 */
export const formatTokenSymbol = (symbol: string): string => {
  if (!symbol) return ''
  
  // Special cases for well-known tokens
  const specialCases: Record<string, string> = {
    'btc': 'BTC',
    'eth': 'ETH',
    'usdt': 'USDT',
    'usdc': 'USDC',
    'bnb': 'BNB',
    'ada': 'ADA',
    'sol': 'SOL',
    'dot': 'DOT',
    'matic': 'MATIC',
    'avax': 'AVAX',
  }
  
  const lowerSymbol = symbol.toLowerCase()
  return specialCases[lowerSymbol] || symbol.toUpperCase()
}

/**
 * Format chain name with proper casing
 */
export const formatChainName = (chainId: string): string => {
  const chainNames: Record<string, string> = {
    'ethereum': 'Ethereum',
    'bsc': 'BSC',
    'polygon': 'Polygon',
    'arbitrum': 'Arbitrum',
    'avalanche': 'Avalanche',
    'solana': 'Solana',
    'fantom': 'Fantom',
    'optimism': 'Optimism',
  }
  
  return chainNames[chainId.toLowerCase()] || chainId
}

/**
 * Format DEX name with proper casing
 */
export const formatDexName = (dexId: string): string => {
  const dexNames: Record<string, string> = {
    'uniswap_v2': 'Uniswap V2',
    'uniswap_v3': 'Uniswap V3',
    'sushiswap': 'SushiSwap',
    'pancakeswap': 'PancakeSwap',
    'quickswap': 'QuickSwap',
    'traderjoe': 'Trader Joe',
    'raydium': 'Raydium',
    'orca': 'Orca',
    'serum': 'Serum',
    '1inch': '1inch',
    'balancer': 'Balancer',
    'curve': 'Curve',
  }
  
  return dexNames[dexId.toLowerCase()] || dexId
}

/**
 * Format risk level with appropriate styling
 */
export const formatRiskLevel = (riskScore: number): {
  level: string
  color: string
  description: string
} => {
  if (riskScore <= 0.3) {
    return {
      level: 'Low',
      color: 'text-success',
      description: 'Low risk opportunity with stable conditions'
    }
  }
  
  if (riskScore <= 0.6) {
    return {
      level: 'Medium',
      color: 'text-warning',
      description: 'Moderate risk with some volatility'
    }
  }
  
  if (riskScore <= 0.8) {
    return {
      level: 'High',
      color: 'text-error',
      description: 'High risk due to market conditions'
    }
  }
  
  return {
    level: 'Very High',
    color: 'text-error',
    description: 'Very high risk - proceed with caution'
  }
}

/**
 * Format ML confidence score
 */
export const formatConfidence = (confidence: number): {
  level: string
  color: string
  description: string
} => {
  if (confidence >= 0.8) {
    return {
      level: 'Very High',
      color: 'text-success',
      description: 'High confidence in ML analysis'
    }
  }
  
  if (confidence >= 0.6) {
    return {
      level: 'High',
      color: 'text-success',
      description: 'Good confidence in analysis'
    }
  }
  
  if (confidence >= 0.4) {
    return {
      level: 'Medium',
      color: 'text-warning',
      description: 'Moderate confidence'
    }
  }
  
  if (confidence >= 0.2) {
    return {
      level: 'Low',
      color: 'text-error',
      description: 'Low confidence in analysis'
    }
  }
  
  return {
    level: 'Very Low',
    color: 'text-error',
    description: 'Very low confidence - use with caution'
  }
}

/**
 * Format address with ellipsis
 */
export const formatAddress = (
  address: string,
  startChars: number = 6,
  endChars: number = 4
): string => {
  if (!address || address.length <= startChars + endChars) {
    return address
  }
  
  return `${address.slice(0, startChars)}...${address.slice(-endChars)}`
}

/**
 * Format gas price in gwei
 */
export const formatGasPrice = (gasPrice: number, unit: string = 'gwei'): string => {
  if (unit === 'gwei') {
    return `${numeral(gasPrice).format('0.0')} gwei`
  }
  
  return `${numeral(gasPrice).format('0.000000')} ${unit}`
}

/**
 * Format volume with appropriate units
 */
export const formatVolume = (volume: number): string => {
  return formatCurrency(volume, 'USD', true)
}

/**
 * Format market cap
 */
export const formatMarketCap = (marketCap: number): string => {
  return formatCurrency(marketCap, 'USD', true)
}

/**
 * Format price change with color indication
 */
export const formatPriceChange = (change: number): {
  formatted: string
  color: string
  isPositive: boolean
} => {
  const isPositive = change > 0
  const isNeutral = change === 0
  
  return {
    formatted: formatPercentage(change),
    color: isPositive ? 'text-success' : isNeutral ? 'text-gray-400' : 'text-error',
    isPositive
  }
}

/**
 * Format execution time estimate
 */
export const formatExecutionTime = (minutes: number): string => {
  if (minutes < 1) {
    return '< 1 min'
  }
  
  if (minutes < 60) {
    return `${Math.round(minutes)} min`
  }
  
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = Math.round(minutes % 60)
  
  if (remainingMinutes === 0) {
    return `${hours}h`
  }
  
  return `${hours}h ${remainingMinutes}m`
}

/**
 * Format slippage percentage
 */
export const formatSlippage = (slippage: number): {
  formatted: string
  color: string
  level: string
} => {
  const level = slippage <= 0.5 ? 'low' : slippage <= 2 ? 'medium' : 'high'
  const color = level === 'low' ? 'text-success' : level === 'medium' ? 'text-warning' : 'text-error'
  
  return {
    formatted: formatPercentage(slippage),
    color,
    level
  }
}
