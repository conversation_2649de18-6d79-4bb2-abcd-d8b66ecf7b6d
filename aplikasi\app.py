# app.py - Dark Futuristic Crypto Arbitrage Bot UI
# Advanced Flask application with real-time WebSocket controls and cyberpunk design
# Untuk menjalankan aplikasi ini:
# 1. pip install Flask requests flask-socketio eventlet
# 2. python app.py
# 3. Buka browser: http://127.0.0.1:5000

from flask import Flask, render_template_string, jsonify, request
from flask_socketio import SocketIO, emit
import requests
import threading
import time
import json
from datetime import datetime, timedelta, timezone
from collections import deque
import logging
import asyncio
import subprocess
import os
import signal

# --- Konfigurasi Aplikasi Flask ---
app = Flask(__name__)
app.config['SECRET_KEY'] = 'crypto_arbitrage_bot_v3_secret'
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='eventlet')

# Menonaktifkan log standar Flask untuk tampilan konsol yang lebih bersih
log = logging.getLogger('werkzeug')
log.setLevel(logging.ERROR)

# --- Enhanced State Management dengan Real-time Parameters ---
app_state = {
    'status': 'Idle',
    'is_running': False,
    'opportunities': [],
    'scan_count': 0,
    'last_scan_time': None,
    'logs': deque(maxlen=500),
    'simulation_capital': 100.0,
    'max_profit_threshold': 25.0,
    'progress': {
        'percentage': 0,
        'current_action': 'Menunggu...',
        'tokens_scanned': 0,
        'total_tokens': 0,
        'tokens_per_second': 0,
        'api_calls_made': 0,
        'opportunities_found': 0,
        'estimated_time_remaining': 0
    },
    'bot_parameters': {
        'profit_thresholds': {
            'stablecoins': {'min': 0.1, 'max': 0.7},
            'blue_chips': {'min': 0.3, 'max': 1.4},
            'defi': {'min': 0.7, 'max': 2.1},
            'solana_ecosystem': {'min': 0.6, 'max': 2.1},
            'meme_coins': {'min': 1.0, 'max': 5.6}
        },
        'scan_intervals': {
            'priority_tier': 30,
            'regular_tier': 120,
            'discovery_tier': 300
        },
        'blockchain_selection': {
            'ethereum': True,
            'solana': True,
            'bsc': True,
            'polygon': True,
            'arbitrum': True
        },
        'min_liquidity': 1000,
        'max_tokens_per_scan': 1000,
        'enable_pair_validation': True,
        'enable_demo_mode': True
    },
    'bot_process': None,
    'statistics': {
        'total_scans': 0,
        'total_opportunities': 0,
        'avg_profit_percentage': 0,
        'best_opportunity': None,
        'uptime_seconds': 0,
        'start_time': None
    }
}

# --- Enhanced Logging dengan Real-time WebSocket ---
def add_log(message, level="info"):
    """Enhanced logging dengan real-time WebSocket broadcast"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    log_entry = {
        'timestamp': timestamp,
        'message': message,
        'level': level,
        'full_text': f"[{timestamp}] {message}"
    }
    app_state['logs'].appendleft(log_entry)
    print(log_entry['full_text'])

    # Broadcast ke semua connected clients
    socketio.emit('new_log', log_entry)

def update_progress(percentage, action, tokens_scanned=0, total_tokens=0, api_calls=0, opportunities=0):
    """Update progress dengan real-time broadcast"""
    app_state['progress'].update({
        'percentage': percentage,
        'current_action': action,
        'tokens_scanned': tokens_scanned,
        'total_tokens': total_tokens,
        'api_calls_made': api_calls,
        'opportunities_found': opportunities
    })

    # Calculate tokens per second dan estimated time
    if app_state['statistics']['start_time']:
        elapsed = time.time() - app_state['statistics']['start_time']
        if elapsed > 0:
            app_state['progress']['tokens_per_second'] = round(tokens_scanned / elapsed, 2)

            if tokens_scanned > 0 and total_tokens > tokens_scanned:
                remaining_tokens = total_tokens - tokens_scanned
                time_per_token = elapsed / tokens_scanned
                app_state['progress']['estimated_time_remaining'] = round(remaining_tokens * time_per_token)

    # Broadcast progress update
    socketio.emit('progress_update', app_state['progress'])

# --- WebSocket Event Handlers ---
@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    add_log("🔗 Client terhubung ke WebSocket", "info")
    emit('status_update', {
        'status': app_state['status'],
        'is_running': app_state['is_running'],
        'parameters': app_state['bot_parameters'],
        'statistics': app_state['statistics']
    })

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    add_log("🔌 Client terputus dari WebSocket", "info")

@socketio.on('update_parameters')
def handle_parameter_update(data):
    """Handle real-time parameter updates"""
    try:
        parameter_type = data.get('type')
        parameter_key = data.get('key')
        parameter_value = data.get('value')

        if parameter_type == 'profit_threshold':
            category = data.get('category')
            threshold_type = data.get('threshold_type')  # 'min' or 'max'
            app_state['bot_parameters']['profit_thresholds'][category][threshold_type] = float(parameter_value)

        elif parameter_type == 'scan_interval':
            app_state['bot_parameters']['scan_intervals'][parameter_key] = int(parameter_value)

        elif parameter_type == 'blockchain':
            app_state['bot_parameters']['blockchain_selection'][parameter_key] = bool(parameter_value)

        elif parameter_type == 'general':
            app_state['bot_parameters'][parameter_key] = parameter_value

        add_log(f"⚙️ Parameter diperbarui: {parameter_type}.{parameter_key} = {parameter_value}", "success")

        # Broadcast parameter update to all clients
        socketio.emit('parameter_updated', {
            'type': parameter_type,
            'key': parameter_key,
            'value': parameter_value,
            'parameters': app_state['bot_parameters']
        })

    except Exception as e:
        add_log(f"❌ Error updating parameter: {e}", "error")

@socketio.on('start_bot')
def handle_start_bot():
    """Handle bot start request"""
    if not app_state['is_running']:
        start_arbitrage_bot()
    else:
        add_log("⚠️ Bot sudah berjalan", "warning")

@socketio.on('stop_bot')
def handle_stop_bot():
    """Handle bot stop request"""
    if app_state['is_running']:
        stop_arbitrage_bot()
    else:
        add_log("⚠️ Bot tidak sedang berjalan", "warning")

# --- Bot Control Functions ---
DEXSCREENER_API_BASE_URL = "https://api.dexscreener.com/latest/dex"

def start_arbitrage_bot():
    """Start the arbitrage bot with enhanced progress tracking"""
    if app_state['is_running']:
        add_log("⚠️ Bot sudah berjalan", "warning")
        return

    app_state['is_running'] = True
    app_state['status'] = 'Memulai bot...'
    app_state['statistics']['start_time'] = time.time()
    app_state['scan_count'] = 0

    add_log("🚀 Memulai Enhanced Crypto Arbitrage Bot v3.0", "success")
    update_progress(0, "Inisialisasi bot...")

    # Start bot in separate thread
    bot_thread = threading.Thread(target=run_arbitrage_bot, daemon=True)
    bot_thread.start()

def stop_arbitrage_bot():
    """Stop the arbitrage bot"""
    app_state['is_running'] = False
    app_state['status'] = 'Menghentikan bot...'

    # Kill bot process if running
    if app_state['bot_process']:
        try:
            app_state['bot_process'].terminate()
            app_state['bot_process'] = None
        except:
            pass

    add_log("🛑 Bot dihentikan", "warning")
    update_progress(0, "Bot dihentikan")

    # Calculate final statistics
    if app_state['statistics']['start_time']:
        app_state['statistics']['uptime_seconds'] = int(time.time() - app_state['statistics']['start_time'])

def run_arbitrage_bot():
    """Main bot execution loop with enhanced progress tracking"""
    try:
        while app_state['is_running']:
            update_progress(10, "Mencari token potensial...")
            potential_tokens = find_potential_tokens()

            if not app_state['is_running']:
                break

            update_progress(30, f"Menganalisis {len(potential_tokens)} token...")

            opportunities_found = 0
            for i, token_address in enumerate(potential_tokens):
                if not app_state['is_running']:
                    break

                progress = 30 + (i / len(potential_tokens)) * 60
                update_progress(
                    progress,
                    f"Menganalisis token {i+1}/{len(potential_tokens)}...",
                    i + 1,
                    len(potential_tokens),
                    app_state['progress']['api_calls_made'] + 1
                )

                pairs = get_token_prices(token_address)
                arbitrage_opportunities = find_arbitrage_opportunities(pairs, token_address)

                if arbitrage_opportunities:
                    opportunities_found += len(arbitrage_opportunities)
                    app_state['opportunities'].extend(arbitrage_opportunities)

                    # Keep only latest 50 opportunities
                    app_state['opportunities'] = app_state['opportunities'][-50:]

                    # Broadcast new opportunities
                    socketio.emit('new_opportunities', arbitrage_opportunities)

                time.sleep(0.1)  # Rate limiting

            app_state['scan_count'] += 1
            app_state['statistics']['total_scans'] += 1
            app_state['statistics']['total_opportunities'] += opportunities_found

            update_progress(100, f"Scan selesai - {opportunities_found} peluang ditemukan")
            add_log(f"✅ Scan #{app_state['scan_count']} selesai - {opportunities_found} peluang ditemukan", "success")

            # Wait before next scan
            for i in range(30):
                if not app_state['is_running']:
                    break
                update_progress(100, f"Menunggu scan berikutnya... {30-i}s")
                time.sleep(1)

    except Exception as e:
        add_log(f"❌ Error dalam bot execution: {e}", "error")
    finally:
        app_state['is_running'] = False
        app_state['status'] = 'Idle'

def find_arbitrage_opportunities(pairs, token_address):
    """Find arbitrage opportunities from token pairs"""
    opportunities = []

    try:
        if len(pairs) < 2:
            return opportunities

        # Group pairs by DEX
        dex_prices = {}
        for pair in pairs:
            dex_name = pair.get('dexId', 'unknown')
            price_usd = float(pair.get('priceUsd', 0))
            liquidity_usd = pair.get('liquidity', {}).get('usd', 0)

            if price_usd > 0 and liquidity_usd > 1000:
                dex_prices[dex_name] = {
                    'price': price_usd,
                    'liquidity': liquidity_usd,
                    'pair_address': pair.get('pairAddress', ''),
                    'pair_data': pair
                }

        # Find arbitrage between DEXs
        dex_list = list(dex_prices.items())
        for i, (dex1_id, dex1_data) in enumerate(dex_list):
            for j, (dex2_id, dex2_data) in enumerate(dex_list[i+1:], i+1):
                price1 = dex1_data["price"]
                price2 = dex2_data["price"]

                if price1 <= 0 or price2 <= 0:
                    continue

                # Calculate profit percentage
                if price2 > price1:
                    profit_pct = ((price2 - price1) / price1) * 100
                    buy_dex = dex1_id
                    sell_dex = dex2_id
                    buy_price = price1
                    sell_price = price2
                    min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])
                else:
                    profit_pct = ((price1 - price2) / price2) * 100
                    buy_dex = dex2_id
                    sell_dex = dex1_id
                    buy_price = price2
                    sell_price = price1
                    min_liquidity = min(dex1_data["liquidity"], dex2_data["liquidity"])

                # Check if profitable
                if profit_pct > 0.5 and min_liquidity > 5000:
                    opportunity = {
                        "id": f"{token_address}_{int(time.time())}_{i}_{j}",
                        "token_symbol": token_address[:10],
                        "buy_exchange": buy_dex,
                        "sell_exchange": sell_dex,
                        "buy_price": round(buy_price, 6),
                        "sell_price": round(sell_price, 6),
                        "profit_percentage": round(profit_pct, 4),
                        "min_liquidity": round(min_liquidity, 2),
                        "buy_pair_address": dex_prices[buy_dex].get("pair_address"),
                        "sell_pair_address": dex_prices[sell_dex].get("pair_address"),
                        "timestamp": datetime.now().isoformat(),
                        "type": "flask_scan"
                    }
                    opportunities.append(opportunity)

        return opportunities

    except Exception as e:
        add_log(f"❌ Error finding arbitrage: {e}", "error")
        return []

def find_potential_tokens():
    """
    Tahap 1: Menemukan token yang berpotensi menggunakan logika multi-strategi yang kompleks dan luas.
    """
    add_log("Memulai siklus baru: Mencari pasangan yang berpotensi...")
    app_state['status'] = 'Mencari pasangan yang berpotensi...'
    
    # Kueri pencarian yang luas untuk mendapatkan sampel pasar yang besar
    search_queries = ['weth', 'sol', 'usdc', 'usdt', 'bnb', 'avax', 'matic']
    
    potential_token_addresses = set()
    
    add_log("[Strategi Terpadu] Memindai pasar untuk aktivitas...")
    
    for query in search_queries:
        if not app_state['is_running']: break
        add_log(f"Menganalisis pasar melalui kueri '{query}'...")
        try:
            search_url = f"{DEXSCREENER_API_BASE_URL}/search?q={query}"
            response = requests.get(search_url, timeout=15)
            response.raise_for_status()
            pairs = response.json().get('pairs', [])
            if not pairs: continue

            fifteen_minutes_ago = datetime.now(timezone.utc) - timedelta(minutes=15)

            for pair in pairs:
                # Ekstrak data yang relevan dengan aman
                volume_h24 = pair.get('volume', {}).get('h24', 0)
                price_change_h24 = abs(pair.get('priceChange', {}).get('h24', 0))
                txns_h1 = pair.get('txns', {}).get('h1', {}).get('buys', 0) + pair.get('txns', {}).get('h1', {}).get('sells', 0)
                
                is_new = False
                pair_created_at_val = pair.get('pairCreatedAt')
                if pair_created_at_val:
                    try:
                        if isinstance(pair_created_at_val, (int, float)):
                            if pair_created_at_val > 10**12: pair_created_at_val /= 1000
                            pair_created_at = datetime.fromtimestamp(pair_created_at_val, tz=timezone.utc)
                        elif isinstance(pair_created_at_val, str):
                            if not pair_created_at_val.endswith('Z'): pair_created_at_val += 'Z'
                            pair_created_at = datetime.fromisoformat(pair_created_at_val.replace('Z', '+00:00'))
                        
                        if pair_created_at and pair_created_at > fifteen_minutes_ago:
                            is_new = True
                    except (ValueError, TypeError, OSError):
                        pass # Abaikan jika timestamp tidak valid

                # Tambahkan token jika memenuhi SALAH SATU kriteria
                if is_new or volume_h24 > 75000 or price_change_h24 > 20 or txns_h1 > 200:
                    if 'baseToken' in pair and 'address' in pair['baseToken']:
                        potential_token_addresses.add(pair['baseToken']['address'])
            
            time.sleep(1) # Jeda antar kueri untuk menghormati API
        except requests.RequestException as e:
            add_log(f"Gagal mengambil data untuk kueri '{query}': {e}")
            continue
            
    if not potential_token_addresses:
        add_log("Pencarian selesai. Tidak ada token yang memenuhi kriteria aktivitas.")
        return []

    add_log(f"Pencarian selesai. Ditemukan {len(potential_token_addresses)} token unik untuk dianalisis.")
    return list(potential_token_addresses)


def get_token_prices(token_address):
    app_state['status'] = f"Mengambil data harga untuk token: {token_address[:10]}..."
    try:
        pairs_url = f"{DEXSCREENER_API_BASE_URL}/tokens/{token_address}"
        response = requests.get(pairs_url, timeout=10)
        response.raise_for_status()
        return response.json().get('pairs', [])
    except requests.RequestException as e:
        error_msg = f"Error saat mengambil harga: {e}"
        app_state['status'] = error_msg
        add_log(error_msg)
        return []

def analyze_opportunities(pairs):
    if not pairs:
        return

    base_token_symbol = pairs[0].get('baseToken', {}).get('symbol', 'N/A')
    add_log(f"Menganalisis {len(pairs)} pasangan untuk token {base_token_symbol}...")
    
    grouped_by_quote_token = {}
    for pair in pairs:
        if pair.get('priceUsd') and pair.get('liquidity', {}).get('usd', 0) > 500:
            quote_token_address = pair['quoteToken']['address']
            if quote_token_address not in grouped_by_quote_token:
                grouped_by_quote_token[quote_token_address] = []
            grouped_by_quote_token[quote_token_address].append(pair)

    if not grouped_by_quote_token:
        add_log(f"Tidak ada pasangan {base_token_symbol} yang memenuhi syarat likuiditas > $500.")
        return

    for quote_token_symbol, pair_group in grouped_by_quote_token.items():
        best_price_per_dex = {}
        for pair in pair_group:
            dex_id = pair.get('dexId')
            if not dex_id:
                continue
            price = float(pair['priceUsd'])
            if dex_id not in best_price_per_dex or price < float(best_price_per_dex[dex_id]['priceUsd']):
                best_price_per_dex[dex_id] = pair
        
        unique_dex_pairs = list(best_price_per_dex.values())
        
        if len(unique_dex_pairs) < 2:
            add_log(f"Hanya ditemukan 1 DEX unik untuk {base_token_symbol}/{unique_dex_pairs[0]['quoteToken']['symbol']}. Analisis dilewati.")
            continue 

        sorted_pairs = sorted(unique_dex_pairs, key=lambda p: float(p['priceUsd']))
        
        buy_dex = sorted_pairs[0]
        sell_dex = sorted_pairs[-1]

        if not buy_dex.get('dexId') or not sell_dex.get('dexId'):
            add_log(f"Peluang dilewati: Data DEX tidak lengkap untuk pasangan {base_token_symbol}.")
            continue

        buy_price = float(buy_dex['priceUsd'])
        sell_price = float(sell_dex['priceUsd'])

        log_msg = (f"Membandingkan {base_token_symbol}/{buy_dex['quoteToken']['symbol']}: "
                   f"Beli di {buy_dex['dexId']} @ ${buy_price:.6f}, "
                   f"Jual di {sell_dex['dexId']} @ ${sell_price:.6f}")
        add_log(log_msg)

        if buy_price > 0 and buy_dex['dexId'] != sell_dex['dexId']:
            profit_percentage = ((sell_price - buy_price) / buy_price) * 100

            max_profit = app_state.get('max_profit_threshold', 25.0)
            if profit_percentage > 0.25 and profit_percentage < max_profit:
                capital = app_state.get('simulation_capital', 100.0)
                buy_liquidity = buy_dex.get('liquidity', {}).get('usd', 0)
                sell_liquidity = sell_dex.get('liquidity', {}).get('usd', 0)
                
                slippage_threshold = 0.015 
                
                if capital < (buy_liquidity * slippage_threshold) and capital < (sell_liquidity * slippage_threshold):
                    opportunity = {
                        'baseToken': buy_dex['baseToken']['symbol'],
                        'quoteToken': buy_dex['quoteToken']['symbol'],
                        'chainId': buy_dex['chainId'],
                        'buy_dex_name': buy_dex['dexId'].capitalize(),
                        'buy_price': f"{buy_price:.6f}",
                        'sell_price': f"{sell_price:.6f}",
                        'profit_percentage': f"{profit_percentage:.2f}",
                        'buy_url': buy_dex['url'],
                        'sell_url': sell_dex['url'],
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    }
                    
                    is_duplicate = any(op['buy_url'] == opportunity['buy_url'] and op['sell_url'] == opportunity['sell_url'] for op in app_state['opportunities'])
                    if not is_duplicate:
                        add_log(f"✅ PELUANG DITEMUKAN: Profit {profit_percentage:.2f}% untuk {opportunity['baseToken']}/{opportunity['quoteToken']}")
                        app_state['opportunities'].append(opportunity)
                        app_state['opportunities'].sort(key=lambda x: float(x['profit_percentage']), reverse=True)
                else:
                    add_log(f"Peluang dilewati: Modal ${capital:,.0f} terlalu besar untuk likuiditas (${buy_liquidity:,.0f} & ${sell_liquidity:,.0f}).")
            elif profit_percentage >= max_profit:
                add_log(f"Peluang dilewati: Profit {profit_percentage:.2f}% melebihi ambang batas maksimal {max_profit}%.")


def scanner_worker():
    add_log("Memulai scanner_worker thread.")
    while app_state.get('is_running', False):
        app_state['scan_count'] += 1
        app_state['last_scan_time'] = datetime.now().strftime('%H:%M:%S')

        potential_tokens = find_potential_tokens()
        
        if not potential_tokens:
            app_state['status'] = "Tidak ada token berpotensi ditemukan. Menunggu..."
        else:
            app_state['status'] = f"Ditemukan {len(potential_tokens)} token. Menganalisis..."
            
            for token_addr in potential_tokens:
                if not app_state['is_running']: 
                    add_log("Pemindaian dihentikan di tengah analisis token.")
                    break
                pairs = get_token_prices(token_addr)
                if pairs:
                    analyze_opportunities(pairs)

        app_state['status'] = "Menunggu siklus pindai berikutnya..."
        
        for i in range(60):
             if not app_state['is_running']:
                 add_log("Pemindaian dihentikan saat jeda.")
                 break
             time.sleep(1)

    app_state['status'] = 'Idle'
    add_log("Pemindaian dihentikan oleh pengguna.")


# --- Enhanced Flask Routes for v3.0 ---

@app.route('/')
def index():
    """Main dashboard with dark futuristic UI"""
    return render_template_string(open('templates/index.html', 'r', encoding='utf-8').read())

@app.route('/toggle-scan', methods=['POST'])
def toggle_scan():
    print("SERVER: /toggle-scan endpoint diakses.") # Log di konsol server
    if not app_state['is_running']:
        app_state['is_running'] = True
        app_state['opportunities'] = []
        app_state['scan_count'] = 0
        app_state['logs'].clear()
        add_log("Bot arbitrase dimulai.")
        thread = threading.Thread(target=scanner_worker)
        thread.daemon = True
        thread.start()
        print("SERVER: Thread scanner_worker telah dimulai.")
        return jsonify({'status': 'scan_started'})
    else:
        app_state['is_running'] = False
        print("SERVER: Status is_running diubah menjadi False.")
        return jsonify({'status': 'scan_stopped'})

@app.route('/update-settings', methods=['POST'])
def update_settings():
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'error': 'Invalid data'}), 400

    updated = False
    if 'capital' in data:
        try:
            capital = float(data['capital'])
            if capital > 0:
                app_state['simulation_capital'] = capital
                add_log(f"Pengaturan diperbarui: Modal simulasi diatur ke ${capital:,.2f}")
                updated = True
        except (ValueError, TypeError): pass
    
    if 'max_profit' in data:
        try:
            max_profit = float(data['max_profit'])
            if max_profit > 0:
                app_state['max_profit_threshold'] = max_profit
                add_log(f"Pengaturan diperbarui: Maksimal profit diatur ke {max_profit:.2f}%")
                updated = True
        except (ValueError, TypeError): pass

    if updated: return jsonify({'success': True})
    else: return jsonify({'success': False, 'error': 'Invalid value'}), 400


@app.route('/get-status')
def get_status():
    status_data = app_state.copy()
    status_data['logs'] = list(app_state['logs'])
    return jsonify(status_data)

# --- Enhanced API Routes for v3.0 ---
@app.route('/api/status')
def api_get_status():
    """Get current bot status for v3.0 UI"""
    return jsonify({
        'status': app_state['status'],
        'is_running': app_state['is_running'],
        'opportunities': app_state['opportunities'][-10:],  # Latest 10
        'scan_count': app_state['scan_count'],
        'last_scan_time': app_state['last_scan_time'],
        'logs': list(app_state['logs'])[-20:],  # Latest 20 logs
        'parameters': app_state['bot_parameters'],
        'statistics': app_state['statistics'],
        'progress': app_state['progress']
    })

@app.route('/api/start', methods=['POST'])
def api_start_bot():
    """Start the arbitrage bot"""
    if not app_state['is_running']:
        start_arbitrage_bot()
        return jsonify({'success': True, 'message': 'Bot started'})
    else:
        return jsonify({'success': False, 'message': 'Bot already running'})

@app.route('/api/stop', methods=['POST'])
def api_stop_bot():
    """Stop the arbitrage bot"""
    if app_state['is_running']:
        stop_arbitrage_bot()
        return jsonify({'success': True, 'message': 'Bot stopped'})
    else:
        return jsonify({'success': False, 'message': 'Bot not running'})

@app.route('/api/opportunities')
def api_get_opportunities():
    """Get current arbitrage opportunities"""
    return jsonify({
        'opportunities': app_state['opportunities'],
        'count': len(app_state['opportunities'])
    })

@app.route('/api/logs')
def api_get_logs():
    """Get recent logs"""
    return jsonify({
        'logs': list(app_state['logs'])
    })

@app.route('/api/parameters', methods=['GET', 'POST'])
def api_handle_parameters():
    """Get or update bot parameters"""
    if request.method == 'GET':
        return jsonify(app_state['bot_parameters'])
    else:
        # Update parameters
        data = request.get_json()
        if data:
            # Update specific parameter
            param_type = data.get('type')
            param_key = data.get('key')
            param_value = data.get('value')

            if param_type == 'profit_threshold':
                category = data.get('category')
                threshold_type = data.get('threshold_type')
                app_state['bot_parameters']['profit_thresholds'][category][threshold_type] = float(param_value)
            elif param_type == 'scan_interval':
                app_state['bot_parameters']['scan_intervals'][param_key] = int(param_value)
            elif param_type == 'blockchain':
                app_state['bot_parameters']['blockchain_selection'][param_key] = bool(param_value)
            elif param_type == 'general':
                app_state['bot_parameters'][param_key] = param_value

            add_log(f"⚙️ Parameter updated: {param_type}.{param_key} = {param_value}", "success")

            return jsonify({'success': True, 'parameters': app_state['bot_parameters']})

        return jsonify({'success': False, 'message': 'Invalid data'})

# --- Template HTML ---
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bot Arbitrase Dex Screener</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Fira+Code&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #111827; /* bg-gray-900 */
            color: #e5e7eb; /* text-gray-200 */
        }
        .glass-card {
            background: rgba(31, 41, 55, 0.6); /* bg-gray-800 with opacity */
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .glass-card:hover {
            border: 1px solid rgba(255, 255, 255, 0.2);
            transform: translateY(-4px);
        }
        .btn-glow { box-shadow: 0 0 8px #3b82f6, 0 0 16px #3b82f6; }
        .btn-glow-stop { box-shadow: 0 0 8px #ef4444, 0 0 16px #ef4444; }
        .log-panel { font-family: 'Fira Code', monospace; }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .fade-in { animation: fadeIn 0.5s ease-out forwards; }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        .animate-pulse-green { animation: pulse 1.5s infinite; }
        
        ::-webkit-scrollbar { width: 6px; }
        ::-webkit-scrollbar-track { background: #1f2937; }
        ::-webkit-scrollbar-thumb { background: #4b5563; border-radius: 3px; }
        ::-webkit-scrollbar-thumb:hover { background: #6b7280; }
    </style>
</head>
<body class="p-4 md:p-8">
    <div class="container mx-auto max-w-7xl">
        
        <header class="text-center mb-10 fade-in">
            <h1 class="text-5xl md:text-6xl font-extrabold text-white">Arbitrage Scanner</h1>
            <p class="text-lg text-gray-400 mt-2">Mendeteksi peluang pasar secara real-time dengan presisi.</p>
        </header>

        <div class="glass-card rounded-xl p-6 mb-8 fade-in" style="animation-delay: 100ms;">
            <div class="flex flex-col lg:flex-row justify-between items-center gap-6">
                <div class="flex items-center gap-4 w-full lg:w-auto">
                    <div id="status-indicator" class="w-4 h-4 rounded-full bg-gray-500 transition-colors duration-300"></div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Status Bot</h2>
                        <p id="status-text" class="text-gray-400">Idle</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:flex items-center gap-6 text-center w-full lg:w-auto">
                    <div>
                        <label for="simulation-capital" class="text-sm text-gray-400 block mb-1">Modal ($)</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">$</span>
                            <input type="number" id="simulation-capital" value="100" class="bg-gray-900 border border-gray-700 rounded-lg w-28 pl-7 pr-2 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:outline-none">
                        </div>
                    </div>
                    <div>
                        <label for="max-profit" class="text-sm text-gray-400 block mb-1">Maks Profit (%)</label>
                        <div class="relative">
                             <input type="number" id="max-profit" value="25" class="bg-gray-900 border border-gray-700 rounded-lg w-28 pl-4 pr-2 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:outline-none">
                             <span class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">%</span>
                        </div>
                    </div>
                    <div class="border-l border-gray-700 pl-6">
                        <p class="text-sm text-gray-400">Siklus Pindai</p>
                        <p id="scan-count" class="text-2xl font-bold text-white">0</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-400">Pindai Terakhir</p>
                        <p id="last-scan-time" class="text-2xl font-bold text-white">N/A</p>
                    </div>
                </div>
                <button id="toggle-scan-btn" class="w-full lg:w-auto px-8 py-4 font-semibold text-white bg-blue-600 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300 btn-glow">
                    Mulai Memindai
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-5 gap-8">
            <div class="lg:col-span-3">
                <h2 class="text-3xl font-bold text-white mb-4 fade-in flex items-center gap-3" style="animation-delay: 200ms;">
                    Peluang Ditemukan
                </h2>
                <div id="opportunities-container" class="space-y-4 max-h-[65vh] overflow-y-auto pr-3">
                    <div id="placeholder" class="text-center py-16 text-gray-500 fade-in" style="animation-delay: 300ms;">
                        <svg class="mx-auto h-12 w-12 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
                        <p class="mt-2">Mulai pemindaian untuk mencari peluang.</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="flex justify-between items-center mb-4 fade-in" style="animation-delay: 200ms;">
                    <h2 class="text-3xl font-bold text-white flex items-center gap-3">
                        Log Aktivitas
                    </h2>
                    <button id="copy-log-btn" class="text-sm bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-full transition-colors">Salin</button>
                </div>
                <div class="glass-card p-4 h-[65vh] overflow-y-auto">
                    <pre id="log-panel" class="log-panel text-xs text-gray-400 whitespace-pre-wrap"></pre>
                </div>
            </div>
        </div>

        <footer class="text-center text-gray-500 mt-12 fade-in" style="animation-delay: 400ms;">
            <p><strong>Penting:</strong> Perhitungan profit belum termasuk gas fee, slippage, atau pajak. Selalu lakukan riset Anda sendiri (DYOR) sebelum melakukan perdagangan.</p>
        </footer>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log("CLIENT: DOM fully loaded. Initializing script...");

            const toggleBtn = document.getElementById('toggle-scan-btn');
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            const scanCountEl = document.getElementById('scan-count');
            const lastScanTimeEl = document.getElementById('last-scan-time');
            const opportunitiesContainer = document.getElementById('opportunities-container');
            const placeholder = document.getElementById('placeholder');
            const logPanel = document.getElementById('log-panel');
            const copyLogBtn = document.getElementById('copy-log-btn');
            const capitalInput = document.getElementById('simulation-capital');
            const maxProfitInput = document.getElementById('max-profit');

            let isScanning = false;
            let updateInterval;
            let debounceTimer;
            
            function generateAvatar(symbol) {
                const colors = ['#f87171', '#fb923c', '#fbbf24', '#a3e635', '#4ade80', '#34d399', '#22d3ee', '#60a5fa', '#818cf8', '#c084fc', '#f472b6'];
                if (!symbol) return `<div class="w-10 h-10 rounded-full flex-shrink-0 bg-gray-700"></div>`;
                const charCodeSum = symbol.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
                const color = colors[charCodeSum % colors.length];
                const initial = symbol.charAt(0).toUpperCase();
                return `<div class="w-10 h-10 rounded-full flex-shrink-0 flex items-center justify-center text-white font-bold text-lg" style="background-color: ${color};">${initial}</div>`;
            }

            function handleToggleScan() {
                console.log("CLIENT: handleToggleScan function called.");
                fetch('/toggle-scan', { method: 'POST' })
                    .then(response => {
                        console.log("CLIENT: Received response from /toggle-scan. Status:", response.status);
                        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                        return response.json();
                    })
                    .then(data => {
                        console.log("CLIENT: Data from /toggle-scan:", data);
                        if (data.status === 'scan_started') {
                            isScanning = true;
                            updateUIForScanning();
                            updateInterval = setInterval(getStatus, 2000);
                        } else if (data.status === 'scan_stopped') {
                            isScanning = false;
                            updateUIForStopped();
                            if (updateInterval) clearInterval(updateInterval);
                        }
                    })
                    .catch(e => {
                        console.error("CLIENT: Error during fetch to /toggle-scan:", e);
                        statusText.textContent = "Error memulai scan.";
                    });
            }

            function setupSettingsListener(inputElement, settingsKey) {
                inputElement.addEventListener('input', (e) => {
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        const value = e.target.value;
                        if (value && value > 0) {
                            fetch('/update-settings', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ [settingsKey]: value })
                            });
                        }
                    }, 500);
                });
            }

            function getStatus() {
                fetch('/get-status')
                    .then(response => response.json())
                    .then(data => {
                        statusText.textContent = data.status;
                        scanCountEl.textContent = data.scan_count;
                        lastScanTimeEl.textContent = data.last_scan_time || 'N/A';
                        
                        if (data.is_running && !isScanning) {
                            isScanning = true;
                            updateUIForScanning();
                            if (!updateInterval) updateInterval = setInterval(getStatus, 2000);
                        } else if (!data.is_running && isScanning) {
                            isScanning = false;
                            updateUIForStopped();
                            if (updateInterval) clearInterval(updateInterval);
                            updateInterval = null;
                        }

                        renderOpportunities(data.opportunities);
                        renderLogs(data.logs, logPanel);
                    });
            }
            
            function renderOpportunities(opportunities) {
                if (opportunities.length === 0) {
                    placeholder.style.display = 'block';
                    opportunitiesContainer.innerHTML = '';
                    opportunitiesContainer.appendChild(placeholder);
                    return;
                }

                placeholder.style.display = 'none';
                opportunitiesContainer.innerHTML = '';

                opportunities.forEach((op, index) => {
                    const avatar = generateAvatar(op.baseToken);
                    const cardId = `op-card-${index}`;
                    const card = document.createElement('div');
                    card.id = cardId;
                    card.className = "glass-card p-4 fade-in";
                    card.style.animationDelay = `${index * 50}ms`;
                    card.innerHTML = `
                        <div class="flex items-center gap-4">
                            ${avatar}
                            <div class="flex-grow">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-bold text-white">${op.baseToken}/${op.quoteToken}</h3>
                                        <p class="text-xs text-gray-400 bg-gray-700 px-2 py-0.5 rounded-full inline-block">${op.chainId}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-bold text-green-400">+${op.profit_percentage}%</p>
                                        <p class="text-xs text-gray-500">${op.timestamp}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-3 text-sm mt-3">
                            <a href="${op.buy_url}" target="_blank" class="bg-gray-900 p-3 rounded-xl hover:bg-gray-700 transition-colors">
                                <p class="text-green-400 font-semibold mb-1">BELI di ${op.buy_dex_name}</p>
                                <p class="text-lg text-white font-mono">$${op.buy_price}</p>
                            </a>
                            <a href="${op.sell_url}" target="_blank" class="bg-gray-900 p-3 rounded-xl hover:bg-gray-700 transition-colors">
                                <p class="text-red-400 font-semibold mb-1">JUAL di ${op.sell_dex_name}</p>
                                <p class="text-lg text-white font-mono">$${op.sell_price}</p>
                            </a>
                        </div>
                        <div class="mt-3 pt-3 border-t border-gray-700">
                            <button class="copy-info-btn w-full text-center text-sm text-blue-400 font-semibold hover:bg-gray-700 py-2 rounded-lg transition-colors">
                                Salin Info
                            </button>
                        </div>
                    `;
                    opportunitiesContainer.appendChild(card);
                });
            }
            
            opportunitiesContainer.addEventListener('click', function(e) {
                const button = e.target.closest('.copy-info-btn');
                if (button) {
                    const card = button.closest('.glass-card');
                    const baseToken = card.querySelector('h3').textContent.split('/')[0];
                    const quoteToken = card.querySelector('h3').textContent.split('/')[1];
                    const chainId = card.querySelector('.text-xs.text-gray-400').textContent;
                    const profit = card.querySelector('.text-green-400').textContent;
                    const buyDex = card.querySelector('a:nth-child(1) p:nth-child(1)').textContent.replace('BELI di ', '');
                    const buyPrice = card.querySelector('a:nth-child(1) p:nth-child(2)').textContent;
                    const sellDex = card.querySelector('a:nth-child(2) p:nth-child(1)').textContent.replace('JUAL di ', '');
                    const sellPrice = card.querySelector('a:nth-child(2) p:nth-child(2)').textContent;
                    const buyUrl = card.querySelector('a:nth-child(1)').href;
                    const sellUrl = card.querySelector('a:nth-child(2)').href;
                    
                    const textToCopy = '--- PELUANG ARBITRASE ---\\n' +
                        'Pasangan: ' + baseToken + '/' + quoteToken + '\\n' +
                        'Jaringan: ' + chainId + '\\n' +
                        'Potensi Profit: ' + profit + '\\n\\n' +
                        '-- STRATEGI --\\n' +
                        'Beli di: ' + buyDex + '\\n' +
                        'Harga Beli: ' + buyPrice + '\\n' +
                        'Link Beli: ' + buyUrl + '\\n\\n' +
                        'Jual di: ' + sellDex + '\\n' +
                        'Harga Jual: ' + sellPrice + '\\n' +
                        'Link Jual: ' + sellUrl + '\\n' +
                        '---------------------------';

                    navigator.clipboard.writeText(textToCopy).then(() => {
                        button.textContent = 'Info Disalin!';
                        setTimeout(() => { 
                            button.textContent = 'Salin Info';
                        }, 2000);
                    });
                }
            });

            function renderLogs(logs, panel) {
                const logContent = logs.join('\\n');
                if (panel.textContent !== logContent) {
                    panel.textContent = logContent;
                    panel.scrollTop = 0;
                }
            }

            function updateUIForScanning() {
                toggleBtn.textContent = 'Hentikan Pemindaian';
                toggleBtn.classList.remove('bg-blue-600', 'btn-glow');
                toggleBtn.classList.add('bg-red-600', 'btn-glow-stop');
                statusIndicator.classList.remove('bg-gray-500');
                statusIndicator.classList.add('bg-green-500', 'animate-pulse-green');
                capitalInput.disabled = true;
                maxProfitInput.disabled = true;
            }

            function updateUIForStopped() {
                toggleBtn.textContent = 'Mulai Memindai';
                toggleBtn.classList.remove('bg-red-600', 'btn-glow-stop');
                toggleBtn.classList.add('bg-blue-600', 'btn-glow');
                statusIndicator.classList.remove('bg-green-500', 'animate-pulse-green');
                statusIndicator.classList.add('bg-gray-500');
                statusText.textContent = 'Idle';
                capitalInput.disabled = false;
                maxProfitInput.disabled = false;
            }

            // Inisialisasi
            if (toggleBtn) {
                console.log("CLIENT: Tombol 'Mulai Memindai' ditemukan. Menambahkan event listener.");
                toggleBtn.addEventListener('click', handleToggleScan);
            } else {
                console.error("CLIENT: KRITIS: Tombol 'Mulai Memindai' tidak ditemukan saat inisialisasi.");
            }
            
            setupSettingsListener(capitalInput, 'capital');
            setupSettingsListener(maxProfitInput, 'max_profit');
            getStatus();
        });
    </script>
</body>
</html>
"""

if __name__ == '__main__':
    print("🚀 Memulai Enhanced Crypto Arbitrage Bot v3.0...")
    print("📱 Buka browser dan kunjungi: http://127.0.0.1:5000")
    print("⚙️ Gunakan panel kontrol untuk mengatur parameter secara real-time")
    print("🔍 Bot akan mencari peluang arbitrase dengan validasi pair yang ketat")
    print("💎 Sistem pair validation mencegah false arbitrage signals")
    print("🌐 Mendukung 1000+ tokens across 5 major blockchains")
    print("🎮 Dark futuristic UI dengan real-time WebSocket controls")

    # Start with SocketIO support
    try:
        socketio.run(app, debug=True, host='0.0.0.0', port=5000, allow_unsafe_werkzeug=True)
    except ImportError:
        print("⚠️ Flask-SocketIO not installed, falling back to standard Flask")
        app.run(host='0.0.0.0', port=5000, debug=True)

