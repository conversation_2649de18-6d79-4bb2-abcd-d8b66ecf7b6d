#!/usr/bin/env python3
"""
Simple installation script for Crypto Arbitrage Bot v2.0
Handles dependency issues and provides fallback options
"""
import subprocess
import sys
import os

def print_banner():
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║     🚀 Crypto Arbitrage Bot v2.0 - Simple Installation      ║
    ║                                                              ║
    ║     ✅ Minimal Dependencies                                  ║
    ║     ✅ No Compilation Required                               ║
    ║     ✅ Public APIs Only                                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def install_package(package):
    """Install a single package with error handling"""
    try:
        print(f"📦 Installing {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package} installed successfully")
            return True
        else:
            print(f"❌ Failed to install {package}: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout installing {package}")
        return False
    except Exception as e:
        print(f"❌ Error installing {package}: {e}")
        return False

def install_minimal_requirements():
    """Install minimal requirements one by one"""
    packages = [
        "fastapi>=0.100.0",
        "uvicorn[standard]>=0.20.0", 
        "pydantic>=2.0.0",
        "python-multipart>=0.0.6",
        "httpx>=0.25.0",
        "aiohttp>=3.8.0",
        "requests>=2.30.0",
        "python-dateutil>=2.8.0",
        "python-dotenv>=1.0.0",
        "websockets>=11.0.0"
    ]
    
    successful = []
    failed = []
    
    for package in packages:
        if install_package(package):
            successful.append(package)
        else:
            failed.append(package)
    
    print(f"\n📊 Installation Summary:")
    print(f"✅ Successful: {len(successful)}")
    print(f"❌ Failed: {len(failed)}")
    
    if failed:
        print(f"\n⚠️  Failed packages: {', '.join(failed)}")
        print("💡 You can try installing them manually later")
    
    return len(successful) >= 6  # Need at least core packages

def test_imports():
    """Test if core packages can be imported"""
    core_packages = ["fastapi", "uvicorn", "httpx", "pydantic"]
    
    print("\n🧪 Testing imports...")
    
    working = []
    broken = []
    
    for package in core_packages:
        try:
            __import__(package)
            print(f"✅ {package} - OK")
            working.append(package)
        except ImportError as e:
            print(f"❌ {package} - Failed: {e}")
            broken.append(package)
    
    return len(working) >= 3  # Need at least 3 core packages

def create_simple_env():
    """Create simple .env file"""
    env_content = """# Crypto Arbitrage Bot v2.0 - Simple Configuration
# No API keys required!

# Application Settings
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Public APIs (no authentication needed)
USE_PUBLIC_APIS_ONLY=true

# Rate Limiting (conservative)
COINGECKO_RATE_LIMIT=10
DEXSCREENER_RATE_LIMIT=60
BINANCE_RATE_LIMIT=300

# Logging
LOG_LEVEL=INFO
"""
    
    try:
        with open(".env", "w") as f:
            f.write(env_content)
        print("✅ Created .env configuration file")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env: {e}")
        return False

def main():
    print_banner()
    
    print("🔍 Checking Python version...")
    python_version = sys.version_info
    if python_version < (3, 8):
        print(f"❌ Python 3.8+ required, found {python_version.major}.{python_version.minor}")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    print("\n📦 Installing minimal dependencies...")
    if not install_minimal_requirements():
        print("❌ Failed to install core dependencies")
        print("💡 Try running: pip install fastapi uvicorn httpx")
        return False
    
    print("\n🧪 Testing installation...")
    if not test_imports():
        print("❌ Some core packages failed to import")
        print("💡 Try restarting your terminal and running again")
        return False
    
    print("\n⚙️  Creating configuration...")
    create_simple_env()
    
    print("\n🎉 Installation completed successfully!")
    print("\n🚀 To start the bot:")
    print("   python main_simple.py")
    print("\n🌐 Then open: http://localhost:8000")
    print("\n📚 API docs: http://localhost:8000/docs")
    
    # Ask if user wants to start now
    try:
        start_now = input("\n❓ Start the bot now? (y/n): ").lower().strip()
        if start_now in ['y', 'yes']:
            print("\n🚀 Starting Crypto Arbitrage Bot...")
            os.system(f"{sys.executable} main_simple.py")
    except KeyboardInterrupt:
        print("\n👋 Installation complete. Run 'python main_simple.py' to start!")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
