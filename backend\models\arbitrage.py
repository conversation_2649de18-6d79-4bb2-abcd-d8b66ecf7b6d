"""
Database models for arbitrage opportunities and related data
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON, Index
from sqlalchemy.sql import func
from backend.core.database import Base
from datetime import datetime
from typing import Dict, Any, Optional


class ArbitrageOpportunity(Base):
    """Model for storing arbitrage opportunities"""
    __tablename__ = "arbitrage_opportunities"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Token Information
    base_token_symbol = Column(String(20), nullable=False, index=True)
    base_token_address = Column(String(100), nullable=False, index=True)
    quote_token_symbol = Column(String(20), nullable=False)
    quote_token_address = Column(String(100), nullable=False)
    
    # Network Information
    chain_id = Column(String(20), nullable=False, index=True)
    network_name = Column(String(50), nullable=False)
    
    # DEX Information
    buy_dex_id = Column(String(50), nullable=False)
    buy_dex_name = Column(String(100), nullable=False)
    buy_pair_address = Column(String(100), nullable=False)
    buy_price_usd = Column(Float, nullable=False)
    buy_liquidity_usd = Column(Float, nullable=False)
    buy_url = Column(Text)
    
    sell_dex_id = Column(String(50), nullable=False)
    sell_dex_name = Column(String(100), nullable=False)
    sell_pair_address = Column(String(100), nullable=False)
    sell_price_usd = Column(Float, nullable=False)
    sell_liquidity_usd = Column(Float, nullable=False)
    sell_url = Column(Text)
    
    # Profit Analysis
    profit_percentage = Column(Float, nullable=False, index=True)
    profit_usd = Column(Float)
    gas_fee_estimate = Column(Float)
    net_profit_percentage = Column(Float, index=True)
    
    # Risk Assessment
    risk_score = Column(Float, default=0.0)
    slippage_estimate = Column(Float)
    liquidity_ratio = Column(Float)
    volume_24h = Column(Float)
    
    # Validation Status
    is_validated = Column(Boolean, default=False)
    validation_errors = Column(JSON)
    
    # Timestamps
    discovered_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    last_updated = Column(DateTime(timezone=True), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True), index=True)
    
    # Additional Data
    metadata = Column(JSON)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_profit_chain', 'profit_percentage', 'chain_id'),
        Index('idx_token_pair', 'base_token_symbol', 'quote_token_symbol'),
        Index('idx_discovery_time', 'discovered_at'),
        Index('idx_active_opportunities', 'is_validated', 'expires_at'),
    )


class TokenMetrics(Base):
    """Model for storing token metrics and analysis"""
    __tablename__ = "token_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Token Information
    token_symbol = Column(String(20), nullable=False, index=True)
    token_address = Column(String(100), nullable=False, index=True)
    token_name = Column(String(200))
    chain_id = Column(String(20), nullable=False, index=True)
    
    # Price Metrics
    current_price_usd = Column(Float, nullable=False)
    price_change_1h = Column(Float)
    price_change_24h = Column(Float)
    price_change_7d = Column(Float)
    
    # Volume Metrics
    volume_24h = Column(Float)
    volume_change_24h = Column(Float)
    
    # Market Metrics
    market_cap = Column(Float)
    fully_diluted_valuation = Column(Float)
    circulating_supply = Column(Float)
    total_supply = Column(Float)
    
    # Trading Metrics
    total_liquidity = Column(Float)
    dex_count = Column(Integer)
    pair_count = Column(Integer)
    
    # Sentiment & Social
    sentiment_score = Column(Float)
    social_mentions = Column(Integer)
    social_sentiment = Column(Float)
    
    # Technical Analysis
    rsi_14 = Column(Float)
    macd_signal = Column(String(10))  # 'BUY', 'SELL', 'HOLD'
    bollinger_position = Column(Float)  # Position relative to Bollinger Bands
    
    # Risk Metrics
    volatility_24h = Column(Float)
    liquidity_risk = Column(Float)
    smart_money_flow = Column(Float)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), index=True)
    
    # Additional Data
    metadata = Column(JSON)
    
    # Indexes
    __table_args__ = (
        Index('idx_token_chain', 'token_symbol', 'chain_id'),
        Index('idx_price_change', 'price_change_24h'),
        Index('idx_volume', 'volume_24h'),
        Index('idx_updated', 'updated_at'),
    )


class ScanSession(Base):
    """Model for tracking scan sessions and performance"""
    __tablename__ = "scan_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Session Information
    session_id = Column(String(100), unique=True, nullable=False, index=True)
    status = Column(String(20), nullable=False, index=True)  # 'running', 'completed', 'failed'
    
    # Configuration
    scan_config = Column(JSON, nullable=False)
    networks_scanned = Column(JSON)
    
    # Performance Metrics
    tokens_analyzed = Column(Integer, default=0)
    pairs_found = Column(Integer, default=0)
    opportunities_discovered = Column(Integer, default=0)
    api_calls_made = Column(Integer, default=0)
    
    # Timing
    started_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    completed_at = Column(DateTime(timezone=True))
    duration_seconds = Column(Float)
    
    # Error Tracking
    errors_count = Column(Integer, default=0)
    error_details = Column(JSON)
    
    # Results Summary
    best_opportunity_profit = Column(Float)
    average_profit = Column(Float)
    total_volume_analyzed = Column(Float)
    
    # Additional Data
    metadata = Column(JSON)


class APIUsage(Base):
    """Model for tracking API usage and rate limiting"""
    __tablename__ = "api_usage"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # API Information
    api_provider = Column(String(50), nullable=False, index=True)
    endpoint = Column(String(200), nullable=False)
    
    # Usage Metrics
    requests_count = Column(Integer, default=1)
    success_count = Column(Integer, default=0)
    error_count = Column(Integer, default=0)
    
    # Response Metrics
    avg_response_time = Column(Float)
    last_response_time = Column(Float)
    last_status_code = Column(Integer)
    
    # Rate Limiting
    rate_limit_remaining = Column(Integer)
    rate_limit_reset = Column(DateTime(timezone=True))
    
    # Timestamps
    first_request = Column(DateTime(timezone=True), server_default=func.now())
    last_request = Column(DateTime(timezone=True), onupdate=func.now(), index=True)
    
    # Error Details
    last_error = Column(Text)
    
    # Indexes
    __table_args__ = (
        Index('idx_api_endpoint', 'api_provider', 'endpoint'),
        Index('idx_last_request', 'last_request'),
    )
