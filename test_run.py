#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing import of main_fixed.py...")
    
    # Test basic imports first
    from datetime import datetime
    import asyncio
    import aiohttp
    print("✅ Basic imports successful")
    
    # Test main module import
    import main_fixed
    print("✅ main_fixed module imported successfully")
    
    # Test detector creation
    detector = main_fixed.AdvancedArbitrageDetector()
    print("✅ AdvancedArbitrageDetector created successfully")
    
    # Test v3.0 components
    print(f"✅ Token categories: {len(detector.token_categories)} categories")
    print(f"✅ Tiered scanner initialized: {len(detector.tiered_scanner.tier_assignments)} tiers")
    print(f"✅ Dynamic discovery initialized: {detector.dynamic_discovery is not None}")
    print(f"✅ Profit threshold manager initialized: {detector.profit_threshold_manager is not None}")
    
    print("\n🚀 All v3.0 components initialized successfully!")
    print("✅ Ready to start Enhanced Crypto Arbitrage Bot v3.0")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
