# Minimal requirements for Crypto Arbitrage Bot v2.0
# This version avoids compilation issues and focuses on core functionality

# Core Web Framework
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
pydantic>=2.0.0
python-multipart>=0.0.6

# HTTP Clients for Public APIs
httpx>=0.25.0
aiohttp>=3.8.0
requests>=2.30.0

# Database (SQLite - no compilation needed)
sqlalchemy>=2.0.0
aiofiles>=23.0.0

# Basic Data Processing (no heavy ML dependencies)
python-dateutil>=2.8.0
python-dotenv>=1.0.0

# WebSocket Support
websockets>=11.0.0

# Logging
structlog>=23.0.0

# Development Tools
black>=23.0.0
isort>=5.12.0

# Optional: Redis (can be skipped)
# redis>=5.0.0
# aioredis>=2.0.0

# Optional: Advanced ML (install separately if needed)
# scikit-learn>=1.3.0
# pandas>=2.0.0
# numpy>=1.24.0
