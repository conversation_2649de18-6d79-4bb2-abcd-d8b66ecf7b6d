# Multi-stage build for Python backend
FROM python:3.11-slim as backend-base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY backend/ ./backend/
COPY aplikasi/ ./aplikasi/
COPY alembic.ini .
COPY alembic/ ./alembic/

# Create logs directory
RUN mkdir -p logs

# Create non-root user
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Start command
CMD ["uvicorn", "aplikasi.app:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]

# Development stage
FROM backend-base as development
USER root
RUN pip install --no-cache-dir pytest pytest-cov pytest-asyncio black isort flake8
USER app
CMD ["uvicorn", "aplikasi.app:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# Production stage
FROM backend-base as production
# Additional production optimizations
ENV PYTHONOPTIMIZE=1
CMD ["gunicorn", "aplikasi.app:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
